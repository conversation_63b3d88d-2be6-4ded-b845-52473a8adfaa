import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin, loadEnv, swcPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react-swc'
import svgr from 'vite-plugin-svgr'
import packageJson from '../package.json'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    main: {
      resolve: {
        alias: {
          '@main': resolve(__dirname, './src/main'),
          '@common': resolve(__dirname, './src/common'),
        },
      },
      plugins: [swcPlugin(), externalizeDepsPlugin({ exclude: ['electron-store'] })],
      build: {
        rollupOptions: {
          external: ['sharp'],
        },
        watch: {},
        outDir: resolve(__dirname, './out/main'),
        lib: {
          entry: resolve(__dirname, './src/main/index.ts'),
        },
      },
      envDir: process.cwd(),
    },
    preload: {
      build: {
        rollupOptions: {
          input: {
            'account-authorize': resolve(__dirname, './src/preload/account-authorization/index.ts'),
            index: resolve(__dirname, './src/preload/index.ts'),
          },
        },
        watch: {},
        outDir: resolve(__dirname, './out/preload'),
        lib: {
          entry: {
            index: resolve(__dirname, './src/preload/index.ts'),
            'account-authorize': resolve(__dirname, './src/preload/account-authorization/index.ts'),
          },
          formats: ['cjs'],
        },
      },
      resolve: {
        alias: {
          '@main': resolve(__dirname, './src/main'),
          '@common': resolve(__dirname, './src/common'),
        },
      },
      plugins: [externalizeDepsPlugin()],
      envDir: process.cwd(),
    },
    renderer: {
      envDir: process.cwd(),
      root: resolve(__dirname, './src/renderer'),
      define: {
        'import.meta.env.APP_VERSION': JSON.stringify(packageJson.version),
      },
      build: {
        rollupOptions: {
          input: {
            index: resolve(__dirname, './src/renderer/index.html'),
            hahaha: resolve(__dirname, './src/renderer/browserHeader.html'),
          },
        },
        outDir: resolve(__dirname, './out/renderer'),
      },
      resolve: {
        alias: {
          '@browser': resolve(__dirname, './src/renderer/browser'),
          '@renderer': resolve(__dirname, './src/renderer/src'),
          '@common': resolve(__dirname, './src/common'),
          '@': resolve(__dirname, './../shared'),
        },
      },
      plugins: [react(), svgr()],
      optimizeDeps: {
        include: ['react/jsx-runtime'],
      },
      css: {
        preprocessorOptions: {
          less: {
            javascriptEnabled: true,
          },
        },
      },
      server: {
        proxy: {
          '/api': {
            target: env.VITE_API_BASE_URL,
            changeOrigin: true,
            secure: false,
          },
        },
      },
    },
  }
})
