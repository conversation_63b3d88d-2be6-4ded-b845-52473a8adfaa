/**
 * const mutex = new Mutex();
 *
 * async function exampleTask(name: string, delay: number) {
 *
 *   console.log(`${name} is waiting for the lock`);
 *
 *   // 获取锁
 *   const unlock = await mutex.lock();
 *
 *   console.log(`${name} acquired the lock`);
 *
 *   // 模拟异步操作
 *   await new Promise((resolve) => setTimeout(resolve, delay));
 *
 *   console.log(`${name} is done with the lock`);
 *
 *   // 释放锁
 *   unlock();
 * }
 *
 * // 启动两个异步任务
 * exampleTask('Task A', 2000);
 * exampleTask('Task B', 1000);
 */

// 定义一个互斥锁类
export class Mutex {
  private _locking: Promise<unknown>
  private _locks: number
  private _rejecters: Set<(errorMessage: string) => void>

  constructor() {
    this._locking = Promise.resolve()
    this._locks = 0
    this._rejecters = new Set()
  }

  get locked() {
    return this._locks > 0
  }

  // lock 方法用于获取锁
  async lock() {
    // 每次获取锁，_locks 都会增加 1
    this._locks += 1

    let unlockNext: () => void
    let rejectLock: (errorMessage: string) => void

    // 创建一个新的 Promise，这个 Promise 在锁被释放时解析
    const willLock = new Promise<void>((resolve, reject) => {
      unlockNext = resolve
      rejectLock = (errorMessage: string) => {
        reject(errorMessage)
      }
      // 保存 reject 函数，以便在强制释放时调用
      this._rejecters.add(reject)
    })

    // 创建一个新的 Promise，这个 Promise 在锁可用时解析，并返回一个解锁函数
    const willUnlock = this._locking
      .then(() => {
        // 当获取到锁后，从拒绝器列表中移除
        this._rejecters.delete(rejectLock)

        // 返回解锁函数，这个函数会减少锁计数并解锁下一个等待者
        return () => {
          this._locks -= 1 // 减少锁计数
          unlockNext() // 解锁下一个等待者
        }
      })
      .catch((error) => {
        // 处理 Promise 链中的错误
        this._locks -= 1
        throw error
      })

    // 更新 _locking，使其在当前的锁被释放后解析
    this._locking = this._locking.then(() => willLock)

    // 返回一个 Promise，这个 Promise 在锁可用时解析，并返回一个解锁函数
    return willUnlock
  }

  // 强制释放所有等待中的锁
  forceRelease() {
    // 向所有等待中的任务抛出错误
    for (const reject of this._rejecters) {
      reject('互斥锁已被强制释放')
    }

    // 清空拒绝器列表
    this._rejecters.clear()

    // 重置锁状态
    this._locking = Promise.resolve()
    this._locks = 0
  }
}
