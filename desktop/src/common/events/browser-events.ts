export const browserChannel = {
  openTab: 'openTab',
  newOpenTab: 'newOpenTab',
  activeTab: 'activeTab',
  closeTab: 'closeTab',
  restoreContext: 'restoreContext',
  restoreAccountContext: 'restoreAccountContext',
  createNewContext: 'createNewContext',
  createAccountContext: 'createAccountContext',
  closeAllTab: 'closeAllTab',
  openAccountTab: 'openAccountTab',
  openAccountTabInNewWindow: 'openAccountTabInNewWindow',
  openNewAccountContext: 'openNewAccountContext',
  isContextCreated: 'isContextCreated',
  anyWebSpaceTabOpened: 'anyWebSpaceTabOpened',
  anyAccountSpaceTabOpened: 'anyAccountSpaceTabOpened',
  getOpenedUrls: 'getOpenedUrls',
  syncFavorites: 'syncFavorites',
  contextUpdated: 'contextUpdated',
  closeOtherTab: 'closeOtherTab',
  closeRightTab: 'closeRightTab',
  saveFavorite: 'saveFavorite',
  removeFavorite: 'removeFavorite',
  getContextDump: 'getContextDump',
  cleanupSessionDirectory: 'cleanupSessionDirectory',
  contextFaviconUpdated: 'contextFaviconUpdated',
  hasSpaceIcon: 'hasSpaceIcon',
  getSpaceIcon: 'getSpaceIcon',
  updateSpaceIcon: 'updateSpaceIcon',
  spaceIconUpdated: 'spaceIconUpdated',
  setHeaderViewTop: 'setHeaderViewTop',
  setTabViewTop: 'setTabViewTop',
  tabViewTopped: 'tabViewTopped',
  headerUpdated: 'headerUpdated',
  headerInitialed: 'HeaderInitialed',
  goBack: 'goBack',
  goForward: 'goForward',
  refresh: 'refresh',
  isContextOpened: 'isContextOpened',
  closeBrowser: 'closeBrowser',
  getAccountSession: 'getAccountSession',
}

export const browserAuthorizeChannel = {
  accountAuthorizing: 'accountAuthorizing',
  accountAuthorizeSuccess: 'accountAuthorizeSuccess',
  accountAuthorizeError: 'accountAuthorizeError',
  accountSessionFailed: 'accountSessionFailed',
  accountIdentityVerified: 'accountIdentityVerified',
}
