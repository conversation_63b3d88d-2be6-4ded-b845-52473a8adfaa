import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'

export interface BrowserTabsData {
  tabs: BrowserTabListItem[]
  activeTab: BrowserActiveTabData | null
}

export interface BrowserTabItem {
  contextIdentifier: BrowserContextIdentifier
  title: string
  url: string
}

export interface BrowserTabListItem extends BrowserTabItem {
  id: string
  icon: string | null
  contextColor: string
  contextName: string
}

export interface BrowserActiveTabData extends BrowserTabItem {
  id: string
  canGoBack: boolean
  canGoForward: boolean
  canAddFavorite: boolean
}
