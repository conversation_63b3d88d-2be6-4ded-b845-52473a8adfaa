import type { auditStatusEnum } from '@yixiaoer/platform-service'
import type { BaseResult } from '@yixiaoer/platform-service/dist/media-platform/baseResult'

export interface InnerPushingResult extends BaseResult {
  errorInfo?: string
  publishId?: string
}

export interface InnerQueryStateResult {
  /**
   * 结果状态
   */
  code: number
  /**
   * 提示信息
   */
  msg?: string
  /**
   * 作品唯一id
   */
  docId?: string
  /**
   * 审核状态
   */
  auditStatus?: auditStatusEnum
  /**
   * 作品创建时间
   */
  create_time?: number
  openUrl?: string
}
