import Store from 'electron-store'
import { mainWindow } from '.'
import { platform } from '@electron-toolkit/utils'

export type StoreSchema = {
  sidebarCollapsed: boolean
  windowSize: {
    width: number
    height: number
  }
  windowPosition: {
    x: undefined
    y: undefined
  }
  displayId: number
}

const store = new Store<StoreSchema>({
  defaults: {
    sidebarCollapsed: false,
    windowSize: { width: 1060, height: 700 },
    windowPosition: { x: undefined, y: undefined },
    displayId: 0,
  },
})

store.onDidChange('sidebarCollapsed', (value) => {
  if (platform.isMacOS) {
    mainWindow.setWindowButtonPosition(getButtonPosition(value!))
  }
  console.log('store changed')
})

function getButtonPosition(value: boolean): Electron.Point {
  return {
    x: value ? 5 : 10,
    y: 10,
  }
}

export { store, getButtonPosition }
