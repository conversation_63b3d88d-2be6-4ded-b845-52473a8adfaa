import type { ProgressInfo, UpdateInfo, UpdaterEvents } from 'electron-updater'
import { autoUpdater } from 'electron-updater'
import { type WebContents } from 'electron'
import log from 'electron-log/main'
import { uiEvents } from '@common/events/ui-events'

console.log('Use auto update...')

let isQuitting = false

let isCheckingUpdate = false

function setIsQuitting(value: boolean) {
  isQuitting = value
}

function getIsQuitting() {
  return isQuitting
}

log.transports.file.level = 'debug'

autoUpdater.logger = log

autoUpdater.channel = `${process.platform}-${process.arch}`
console.log('autoUpdater.channel', autoUpdater.channel)
let win: WebContents
let autoQuitAndInstall = false

function sendStatusToWindow(event: UpdaterEvents, info?: UpdateInfo | Error | ProgressInfo) {
  console.log(event, info)
  win.send(uiEvents.autoUpdate, {
    event,
    data: info,
    autoDownload: autoUpdater.autoDownload,
  })

  if (
    ['checking-for-update', 'update-not-available', 'update-available', 'error'].includes(event)
  ) {
    isCheckingUpdate = false
  }
}

autoUpdater.on('checking-for-update', () => {
  sendStatusToWindow('checking-for-update')
})
autoUpdater.on('update-available', (info) => {
  sendStatusToWindow('update-available', info)
})
autoUpdater.on('update-not-available', (info) => {
  sendStatusToWindow('update-not-available', info)
})
autoUpdater.on('error', (err) => {
  sendStatusToWindow('error', err)
})
autoUpdater.on('download-progress', (progressObj) => {
  sendStatusToWindow('download-progress', progressObj)
})
autoUpdater.on('update-downloaded', (info) => {
  if (autoQuitAndInstall) {
    quitAndInstall()
  }
  sendStatusToWindow('update-downloaded', info)
})

const quitAndInstall = () => {
  setIsQuitting(true)
  autoUpdater.quitAndInstall(true, true)
}

const downloadUpdate = () => {
  void autoUpdater.downloadUpdate()
}

const checkForUpdatesAndNotify = (
  winCenter: WebContents,
  autoDownload: boolean,
  AcquitAndInstall: boolean,
  url: string,
) => {
  if (isCheckingUpdate) {
    return
  }
  if (url) {
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: url,
      channel: `${process.platform}-${process.arch}`,
    })
  }
  isCheckingUpdate = true
  win = winCenter
  autoQuitAndInstall = AcquitAndInstall
  autoUpdater.autoDownload = autoDownload || false
  void autoUpdater.checkForUpdatesAndNotify({
    title: '{appName} 发现新的版本',
    body: '{version} 版本已准备就绪，重启后可自动完成更新。',
  })
}

export { checkForUpdatesAndNotify, quitAndInstall, downloadUpdate, setIsQuitting, getIsQuitting }
