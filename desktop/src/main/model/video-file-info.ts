import type { VideoFileInfoStructure } from '@common/model/video-file-info'

export class VideoFileInfo implements VideoFileInfoStructure {
  constructor(
    public filePath: string,
    public fileSize: number,
    public fileFormat: string,
    public fileDuration: number,
    public videoWidth: number,
    public videoHeight: number,
    public videoEncoding: string,
    public videoDepth: number,
    public fileName: string,
    public hasAudioTrack: boolean,
  ) {}

  static of(
    filePath: string,
    fileSize: number,
    fileFormat: string,
    fileDuration: number,
    videoWidth: number,
    videoHeight: number,
    videoEncoding: string,
    videoDepth: number,
    fileName: string,
    hasAudioTrack: boolean,
  ) {
    return new VideoFileInfo(
      filePath,
      fileSize,
      fileFormat,
      fileDuration,
      videoWidth,
      videoHeight,
      videoEncoding,
      videoDepth,
      fileName,
      hasAudioTrack,
    )
  }
}
