import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { baiJiaHaoPlatformService } from '../../services/platform-service/bai-jia-hao'
import type { PushContentType } from '@common/model/content-type'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.baiJiaHao
    ipcMain.handle(
      platform.getTopics,
      (_event, cookies: Electron.Cookie[], categoryId: number, keyword: string, title: string) => {
        return baiJiaHaoPlatformService.getTopics(cookies, keyword, title)
      },
    )
    ipcMain.handle(platform.getLocations, (_event, cookies: Electron.Cookie[], keyword: string) => {
      return baiJiaHaoPlatformService.getLocations(cookies, keyword)
    })
    ipcMain.handle(
      platform.getCategories,
      (_event, cookies: Electron.Cookie[], contentType: PushContentType) => {
        return baiJiaHaoPlatformService.getCategoryList(cookies, contentType)
      },
    )
  },
}
