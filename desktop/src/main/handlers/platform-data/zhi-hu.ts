import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { zhiHuPlatformService } from '@main/services/platform-service/zhi-hu'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.zhiHu

    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyword: string) => {
      return zhiHuPlatformService.getTopics(cookies, keyword)
    })
  },
}
