import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { douYinPlatformService } from '../../services/platform-service/dou-yin'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.douYin
    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return douYinPlatformService.getTopics(cookies, keyWord)
    })
    ipcMain.handle(platform.getFriends, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return douYinPlatformService.getFriends(cookies, keyWord)
    })
    ipcMain.handle(platform.getLocations, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return douYinPlatformService.getLocations(cookies, keyWord)
    })
    ipcMain.handle(platform.getMusicCategories, () => {
      return douYinPlatformService.getMusicCategories()
    })
    ipcMain.handle(
      platform.getMusic,
      (_event, cookies: Electron.Cookie[], cursor?: number, keyWord?: string, id?: string) => {
        return douYinPlatformService.getMusicList(cookies, cursor, keyWord, id)
      },
    )
  },
}
