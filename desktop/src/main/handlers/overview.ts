import { ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import { getPlatformService } from '../services/platform-service/factory'
import type { OverviewContentType } from '@common/structure'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(
      uiEvents.queryAccountOverview,
      async (_event, platformName: string, accountId: string, cookies: Electron.Cookie[]) => {
        return getPlatformService(platformName).queryAccountOverview(cookies)
      },
    )
    ipcMain.handle(
      uiEvents.queryPublishOverviewsHeaders,
      async (_event, platformName: string, contentType: OverviewContentType | undefined) => {
        return getPlatformService(platformName).queryPublishOverviewsHeaders(
          platformName,
          contentType,
        )
      },
    )
    ipcMain.handle(
      uiEvents.queryPublishOverviews,
      async (_event, platformName: string, accountId: string, cookies: Electron.Cookie[]) => {
        return getPlatformService(platformName).queryPublishOverview(cookies)
      },
    )
  },
}
