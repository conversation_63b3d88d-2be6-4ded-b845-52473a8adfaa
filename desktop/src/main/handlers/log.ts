import { type Cookie, ipcMain } from 'electron'
import { logChannel } from '@common/events/log-channel'
import { authorizeLogService } from '@main/services/authorize-log-service'
import { publishLogService } from '@main/services/publish-log-service'
import type { AccountSession } from '@common/structure'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import { pathService } from '@main/services/path-service'
import { compressionService } from '@main/services/compression-service'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.on(
      logChannel.authorizeSuccess,
      (
        _event,
        platform: string,
        authorId: string,
        authorName: string,
        accountSession: AccountSession,
      ) => {
        authorizeLogService.logAuthorizeSuccess(platform, authorId, authorName, accountSession)
      },
    )
    ipcMain.on(
      logChannel.authorizeUpdate,
      (
        _event,
        platform: string,
        authorId: string,
        authorName: string,
        accountSession: AccountSession,
      ) => {
        authorizeLogService.logAuthorizeUpdate(platform, authorId, authorName, accountSession)
      },
    )

    ipcMain.on(
      logChannel.authorizeFail,
      (
        _event,
        platform: string,
        authorId: string,
        authorName: string,
        detail: string | unknown,
        cookies: Cookie[],
      ) => {
        authorizeLogService.logAuthorizeFail(platform, authorId, authorName, detail, cookies)
      },
    )

    ipcMain.on(
      logChannel.authorizeError,
      (
        _event,
        platform: string,
        authorId: string,
        authorName: string,
        accountSession: AccountSession,
        error: Error,
      ) => {
        authorizeLogService.logAuthorizeError(platform, authorId, authorName, accountSession, error)
      },
    )

    ipcMain.on(
      logChannel.publishScheduling,
      (
        _event,
        taskId: string,
        platform: string,
        authorId: string,
        authorName: string,
        wechatToken: [string, string] | null,
        accountSession: AccountSession,
        body: LiteralObject,
      ) => {
        publishLogService.publishScheduling(
          taskId,
          platform,
          authorId,
          authorName,
          wechatToken,
          accountSession,
          body,
        )
      },
    )

    ipcMain.handle(logChannel.getLogArchive, async () => {
      const path = pathService.getLogPath()
      return await compressionService.compress(path)
    })
  },
}
