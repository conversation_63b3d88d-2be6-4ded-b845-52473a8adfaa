import type { PlatformService } from './index'
import { platformNames } from '@common/model/platform-name'
import { douYinPlatformService } from './dou-yin'
import { kuaiShouPlatformService } from './kuai-shou'
import { xiaoHongShuPlatformService } from './xiao-hong-shu'
import { weiXinShiPinHaoPlatformService } from './wei-xin-shi-pin-hao'
import { bilibiliPlatformService } from './bili-bili'
import { baiJiaHaoPlatformService } from './bai-jia-hao'
import { touTiaoHaoPlatformService } from './tou-tiao-hao'
import { xiGuaShiPinPlatformService } from './xi-gua-shi-pin'
import { zhiHuPlatformService } from './zhi-hu'
import { xinLangWeiBoPlatformService } from './xin-lang-wei-bo'
import { qiEHaoPlatformService } from './qi-e-hao'
import { souHuHaoPlatformService } from './sou-hu-hao'
import { yiDianHaoPlatformService } from './yi-dian-hao'
import { daYuHaoPlatformService } from './da-yu-hao'
import { wangYiHaoPlatformService } from './wang-yi-hao'
import { aiQiYiPlatformService } from '@main/services/platform-service/ai-qi-yi'
import { tengXunWeiShiPlatformService } from '@main/services/platform-service/teng-xun-wei-shi'

export function getPlatformService(platformName: string): PlatformService {
  switch (platformName) {
    case platformNames.DouYin:
      return douYinPlatformService
    case platformNames.KuaiShou:
      return kuaiShouPlatformService
    case platformNames.WeiXinShiPinHao:
      return weiXinShiPinHaoPlatformService
    case platformNames.XiaoHongShu:
      return xiaoHongShuPlatformService
    case platformNames.BiliBili:
      return bilibiliPlatformService
    case platformNames.BaiJiaHao:
      return baiJiaHaoPlatformService
    case platformNames.TouTiaoHao:
      return touTiaoHaoPlatformService
    case platformNames.XiGuaShiPin:
      return xiGuaShiPinPlatformService
    case platformNames.SouHuHao:
      return souHuHaoPlatformService
    case platformNames.YiDianHao:
      return yiDianHaoPlatformService
    case platformNames.DaYuHao:
      return daYuHaoPlatformService
    case platformNames.ZhiHu:
      return zhiHuPlatformService
    case platformNames.QiEHao:
      return qiEHaoPlatformService
    case platformNames.WangYiHao:
      return wangYiHaoPlatformService
    case platformNames.XinLangWeiBo:
      return xinLangWeiBoPlatformService
    case platformNames.AiQiYi:
      return aiQiYiPlatformService
    case platformNames.TengXunWeiShi:
      return tengXunWeiShiPlatformService
    default:
      throw new Error(`platformService 未找到 : ${platformName}`)
  }
}
