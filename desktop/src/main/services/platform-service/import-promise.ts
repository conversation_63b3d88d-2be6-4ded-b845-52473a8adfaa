/* eslint-disable @typescript-eslint/consistent-type-imports */
import { pathService } from '../path-service'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import { getPlatformService } from '@main/services/platform-service/factory'
import { injection, Cloud } from '@yixiaoer/platform-service'
import { systemService } from '../system-service'

const pushingSchedulerService = new Cloud.PushingScheduleService()
const stateQueryScheduleService = new Cloud.StateQueryScheduleService()

export const getPlatformServicePromise = async () => {
  const path = pathService.getReptileFilePath()
  delete require.cache[path]
  const promise = require(path) as typeof import('@yixiaoer/platform-service')
  const config = (await promise).PubConfig
  config.setLogPath(pathService.getLogPath())
  config.API_URL = import.meta.env.VITE_API_URL
  config.API_Token = systemService.getAuthorizationToken()
  config.xClient = 'desktop'
  config.setUserDataPath(pathService.getUserDataPath())
  config.pushingSchedulerService = pushingSchedulerService
  config.stateQuerySchedulerService = stateQueryScheduleService
  return promise
}

const tabAbortControllerManager = new injection.TabAbortControllerManager()
const authorizeStateManager = new injection.AuthorizeStateManager()

export const getAuthorizeServicePromise = async () => {
  const value = await getPlatformServicePromise()
  value.injection.config.authorizedCallback = async (
    view: Electron.CrossProcessExports.WebContentsView,
    contextIdentifier: BrowserContextIdentifier,
    color: string,
    platformName: string,
    fromAccountId: string | null,
  ) => {}
  value.injection.config.externalIsAuthorizedCallback = async (
    view: Electron.CrossProcessExports.WebContentsView,
    contextIdentifier: BrowserContextIdentifier,
    color: string,
    platformName: string,
    fromAccountId: string | null,
  ) => {
    try {
      const cookies = await view.webContents.session.cookies.get({})
      const accountInfo = await getPlatformService(platformName).getAccountInfo(cookies)
      return !!accountInfo
    } catch (e) {
      console.error(e)
      return false
    }
  }

  value.injection.config.authorizeFailedCallback = async (
    view: Electron.CrossProcessExports.WebContentsView,
    contextIdentifier: BrowserContextIdentifier,
    color: string,
    platformName: string,
    fromAccountId: string | null,
  ): Promise<void> => {}

  value.injection.config.tabAbortControllerManager = tabAbortControllerManager

  value.injection.config.authorizeStateManager = authorizeStateManager

  return value.injection
}
