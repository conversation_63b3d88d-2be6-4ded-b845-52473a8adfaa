import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { XiguaVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/xiguashipin'

class XiGuaShiPinPlatformService extends PlatformService {
  constructor() {
    super(platformNames.XiGuaShiPin)
  }

  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)

    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Xiguashipin.publishVideo(
          cookie,
          body as XiguaVideoTaskInput,
          eventEmitter,
          authorId,
        ),
      progressCallBack,
    )

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Xiguashipin.getXiguashipinUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.XiGuaShiPin,
      info.userId,
      info.userName,
      info.userImage,
    )
  }
}

export const xiGuaShiPinPlatformService = new XiGuaShiPinPlatformService()
