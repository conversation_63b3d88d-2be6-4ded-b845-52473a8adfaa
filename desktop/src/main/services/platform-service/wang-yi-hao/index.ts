import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { WangyihaoArticleTaskInput, WangyiVideoTaskInput } from '@yixiaoer/platform-service'

class WangYiHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.WangYiHao)
  }

  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Wangyihao.publishVideo(
        cookie,
        body as WangyiVideoTaskInput,
        eventEmitter,
        '',
      )
    }, progressCallBack)

    return result.publishId!
  }

  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Wangyihao.publishArticle(
        cookie,
        body as WangyihaoArticleTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    return this.convert2PlatformAccountInfo(
      async () => (await getPlatformServicePromise()).Wangyihao.getWangyihaoUserInfo(cookie),
      (x) =>
        new AuthorizingAccountInfo(
          platformNames.WangYiHao,
          x.data!.userId,
          x.data!.tname,
          x.data!.icon,
          x.verify,
        ),
    )
  }
}

export const wangYiHaoPlatformService = new WangYiHaoPlatformService()
