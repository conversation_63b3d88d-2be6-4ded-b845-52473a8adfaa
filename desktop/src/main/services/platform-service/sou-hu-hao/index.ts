import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { SouhuhaoArticleTaskInput, SouhuhaoVideoTaskInput } from '@yixiaoer/platform-service'

class SouHuHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.SouHuHao)
  }
  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Souhuhao.publishVideo(
        cookie,
        Number.parseInt(authorId),
        body as SouhuhaoVideoTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Souhuhao.publishArticle(
        cookie,
        body as SouhuhaoArticleTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    return this.convert2PlatformAccountInfo(
      async () => (await getPlatformServicePromise()).Souhuhao.getSouhuhaoUserInfo(cookie),
      (x) =>
        new AuthorizingAccountInfo(
          platformNames.SouHuHao,
          x.yixiaoerId!,
          x.yixiaoerName!,
          x.yixiaoerImageUrl!,
          x.verify,
        ),
    )
  }
}

export const souHuHaoPlatformService = new SouHuHaoPlatformService()
