import { machineIdSync } from 'node-machine-id'
import * as os from 'os'
import * as crypto from 'crypto'
import * as fs from 'fs'
import { pathService } from './path-service'

export class DeviceIdService {
  private storagePath: string

  constructor() {
    // 存储设备ID的文件路径
    this.storagePath = pathService.getUserDataPath('device-id.json')
  }

  /**
   * 获取设备的唯一ID
   */
  public getDeviceId(): string {
    try {
      // 尝试读取已保存的ID
      if (fs.existsSync(this.storagePath)) {
        const data = fs.readFileSync(this.storagePath, 'utf8')
        const json = JSON.parse(data)
        return json.deviceId
      }
    } catch (error) {
      console.error('读取设备ID出错:', error)
    }

    // 如果无法读取存储的ID，则生成一个新的
    return this.generateAndSaveDeviceId()
  }

  /**
   * 生成并保存设备唯一ID
   */
  private generateAndSaveDeviceId(): string {
    try {
      // 使用node-machine-id库获取机器ID
      let deviceId = machineIdSync(true)

      // 如果上面的方法失败，使用备用方法：组合系统信息
      if (!deviceId) {
        const systemInfo = {
          hostname: os.hostname(),
          platform: os.platform(),
          arch: os.arch(),
          cpus: os
            .cpus()
            .map((cpu) => cpu.model)
            .join(),
          totalMem: os.totalmem(),
        }

        // 将系统信息转换为字符串并哈希
        const systemInfoStr = JSON.stringify(systemInfo)
        deviceId = crypto.createHash('sha256').update(systemInfoStr).digest('hex')
      }

      // 保存生成的设备ID
      fs.writeFileSync(this.storagePath, JSON.stringify({ deviceId }))

      return deviceId
    } catch (error) {
      console.error('生成设备ID出错:', error)

      // 如果所有方法都失败，生成一个随机ID
      const randomId = crypto.randomUUID()
      try {
        fs.writeFileSync(this.storagePath, JSON.stringify({ deviceId: randomId }))
      } catch (e) {
        console.error('保存随机设备ID出错:', e)
      }

      return randomId
    }
  }
}

export const deviceIdService = new DeviceIdService()
