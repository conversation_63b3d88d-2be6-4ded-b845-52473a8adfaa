import type { SpaceIdentifier } from '@common/structure/space/space-identifier'
import { pathService } from '@main/services/path-service'
import fsExtra from 'fs-extra'
import axios from 'axios'
import { urlService } from '@main/services/url-service'
import { browserEventBus } from '@main/services/eventBus/eventBus'

class SpaceService {
  hasSpaceIcon(spaceIdentifier: SpaceIdentifier) {
    const path = pathService.getSpaceIconPath(spaceIdentifier)
    return fsExtra.existsSync(path)
  }

  async updateIcon(spaceIdentifier: SpaceIdentifier, favicon: string) {
    const path = pathService.getSpaceIconPath(spaceIdentifier)
    const response = await axios.get(favicon, { responseType: 'arraybuffer' })
    if (response.status === 200) {
      fsExtra.outputFileSync(path, response.data)
      browserEventBus.emit('space-icon-updated', spaceIdentifier)
    }
  }

  getIconUrl(spaceIdentifier: SpaceIdentifier) {
    if (this.hasSpaceIcon(spaceIdentifier)) {
      const spaceIconPath = pathService.getSpaceIconPath(spaceIdentifier)
      return urlService.absolutePathToUrl(spaceIconPath)
    }
    return null
  }
}

export const spaceService = new SpaceService()
