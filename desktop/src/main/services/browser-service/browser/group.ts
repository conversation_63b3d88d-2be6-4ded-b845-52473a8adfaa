import { <PERSON><PERSON><PERSON><PERSON>indow, WebContentsView } from 'electron'
import type { Tab } from './tab'
import { AccountTab } from './tab'
import { is } from '@electron-toolkit/utils'
import { join } from 'path'
import type {
  BrowserTabsData,
  BrowserTabListItem,
  BrowserActiveTabData,
} from '@common/structure/browser/header'
import { browserChannel } from '@common/events/browser-events'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import { identifierService } from '@common/infrastructure/services/identifier-service'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import icon from '../../../../../resources/icon.png?asset'
import { browserEvents } from '@main/services/eventBus/event/events'
import type { ContextFavorite } from '@common/structure/space/context-favorite'
import type { BrowserContext } from '@main/services/browser-service/browser/browser-context'
import { tabManager } from './tab-manager'

export class Group {
  public window!: BrowserWindow
  public headerView!: WebContentsView
  public activeTab: Tab | null = null
  public tabs: Tab[] = []

  constructor(public readonly identifier: string) {
    browserEventBus.on(
      'open-new-page' + this.identifier,
      async ({ url, from }: { url: string; from: Tab }) => {
        const tabId = identifierService.generateUUID()
        let tab: Tab
        // 沿用fromTab的类型
        if (from instanceof AccountTab)
          tab = await tabManager.openAccountSpaceTab(tabId, from.context.identifier, url)
        else tab = await tabManager.openWebSpaceTab(tabId, from.context.identifier, url)
        this.bindTab(tab)
      },
    )
  }

  private initWindow() {
    this.window = new BrowserWindow({
      width: 800,
      height: 600,
      show: false,
      icon: icon,
    })
    this.window.setMenu(null)

    this.window.on('close', () => {
      for (const tab of this.tabs) {
        tabManager.closeTab(tab.id)
      }

      this.window.removeAllListeners()
      this.headerView.removeAllListeners()
      this.headerView.webContents.close()

      browserEventBus.emit('group-closed', this.identifier)
    })

    this.window.on('resize', () => {
      this.setBounds()
    })

    this.window.on('ready-to-show', () => {
      this.setBounds()
    })

    void this.window.loadURL(
      'data:text/html,<html lang="zh"><head><meta charset="UTF-8" /><title>浏览器 | 蚁小二</title></head></html>',
    )
  }

  private initHeaderView() {
    this.headerView = new WebContentsView({
      webPreferences: {
        preload: join(__dirname, '../preload/index.js'),
        sandbox: false,
        transparent: true,
        partition: 'header',
      },
    })

    if (this.identifier) {
      void this.headerView.webContents.executeJavaScript(
        `__ident__ = ${JSON.stringify(this.identifier)}`,
      )
    }

    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      void this.headerView.webContents.loadURL(
        join(process.env['ELECTRON_RENDERER_URL'], 'browserHeader.html'),
      )
    } else {
      void this.headerView.webContents.loadFile(join(__dirname, '../renderer/browserHeader.html'))
    }

    this.setBounds()
    import.meta.env.DEV && this.headerView.webContents.openDevTools()

    this.headerView.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
      delete details.requestHeaders['Referer']
      callback({ cancel: false, requestHeaders: details.requestHeaders })
    })

    this.headerView.webContents.on('ipc-message', (_event, channel, ...args) => {
      switch (channel) {
        case browserChannel.headerInitialed:
          this.updateHeader()
          browserEventBus.emit(browserEvents.headerInitialed + this.identifier)
          return
        case browserChannel.goBack:
          this.activeTab?.goBack()
          return
        case browserChannel.goForward:
          this.activeTab?.goForward()
          return
        case browserChannel.refresh:
          this.activeTab?.refresh()
          return
        case browserChannel.setHeaderViewTop:
          this.setTop(this.headerView)
          return
        case browserChannel.setTabViewTop:
          if (this.activeTab) this.setTabViewToTop(this.activeTab)
          return
        case browserChannel.closeRightTab:
          {
            const [tabId] = args as [string]
            this.closeRightTab(tabId)
          }
          return
        case browserChannel.closeOtherTab:
          {
            const [tabId] = args as [string]
            this.closeOtherTab(tabId)
          }
          return
      }
    })

    browserEventBus.on('tab-favicon-updated' + this.identifier, () => {
      this.updateHeader()
    })

    browserEventBus.on('tab-loaded' + this.identifier, () => {
      this.updateHeader()
    })

    browserEventBus.on('tab-closed' + this.identifier, (_tabId: string) => {
      this.updateHeader()
    })

    browserEventBus.on('tab-activated' + this.identifier, (_tabId: string) => {
      this.updateHeader()
    })

    return new Promise<void>((resolve) => {
      this.headerView.webContents.on('did-finish-load', () => {
        resolve()
      })
    })
  }

  private setBounds(): void {
    const bounds = this.window.contentView.getBounds()
    this.headerView.setBounds(bounds)

    this.tabs.forEach((tab) => {
      tab.setBounds(this)
    })
  }

  getTab(tabId: string) {
    return this.tabs.find((tab) => tab.id === tabId)
  }

  async bindTab(tab: Tab) {
    await this.init()

    tab.bindGroup(this)
    this.tabs.push(tab)

    this.active(tab.id)

    this.setTabViewToTop(tab)

    this.window.show()

    return tab
  }

  async open(context: BrowserContext, tabId: string, url: string, platformName?: string) {
    await this.init()

    const tab = platformName
      ? await tabManager.openAccountSpaceTab(tabId, context.identifier, url)
      : await tabManager.openWebSpaceTab(tabId, context.identifier, url)
    tab.bindGroup(this)
    this.tabs.push(tab)

    this.active(tabId)

    this.setTabViewToTop(tab)

    this.window.show()

    return tab
  }

  private setTabViewToTop(tab: Tab) {
    this.setTop(tab.view)

    this.headerView.webContents.send(browserChannel.tabViewTopped)
  }

  private setTop(view: WebContentsView) {
    this.window.contentView.addChildView(view, 999999)
  }

  public async init(): Promise<void> {
    const needNewWindow = !this.window || this.window.isDestroyed()
    if (needNewWindow) {
      this.initWindow()
    }

    if (
      !this.headerView ||
      !this.headerView.webContents ||
      this.headerView.webContents.isDestroyed()
    ) {
      await this.initHeaderView()
    }

    if (needNewWindow) {
      this.window.contentView.addChildView(this.headerView, 0)
    }
  }

  private updateHeader() {
    if (!this.headerView.webContents) {
      return
    }
    this.headerView.webContents.send(browserChannel.headerUpdated, {
      tabs: this.tabs.map((tab) => {
        return {
          id: tab.id,
          icon: tab.icon,
          contextIdentifier: tab.context.identifier,
          contextColor: tab.context.color,
          title: tab.title,
          url: tab.url,
          contextName: tab.context.contextName,
        } satisfies BrowserTabListItem as BrowserTabListItem
      }),
      activeTab:
        this.activeTab !== null
          ? ({
              id: this.activeTab.id,
              contextIdentifier: this.activeTab.context.identifier,
              title: this.activeTab.title,
              url: this.activeTab.url,
              canGoBack: this.activeTab.canGoBack,
              canGoForward: this.activeTab.canGoForward,
              canAddFavorite: this.activeTab.canAddFavorite,
            } satisfies BrowserActiveTabData as BrowserActiveTabData)
          : null,
    } satisfies BrowserTabsData as BrowserTabsData)
  }

  active(id: string) {
    const targetTab = this.tabs.find((tab) => tab.id === id)
    if (targetTab && targetTab !== this.activeTab) {
      this.tabs
        .filter((tab) => tab.id !== id)
        .forEach((tab) => {
          tab.setVisible(false)
        })
      this.activeTab = targetTab
      this.activeTab.setVisible(true)
      this.setTabViewToTop(this.activeTab)

      browserEventBus.emit('tab-activated' + this.identifier, targetTab.id)
    }
  }

  public removeTab(tabId: string): void {
    const findIndex = this.tabs.findIndex((x) => x.id === tabId)
    const tab = this.tabs[findIndex]
    this.window.contentView.removeChildView(tab.view)
    this.tabs = this.tabs.filter((x) => x.id !== tab.id)
    if (this.activeTab === tab) {
      const nearestTab = this.tabs[findIndex] || this.tabs[findIndex - 1]
      if (nearestTab) {
        this.active(nearestTab.id)
      } else {
        this.activeTab = null
      }
    }

    browserEventBus.emit('tab-closed' + this.identifier, tab.id)

    if (this.tabs.length === 0 && !this.window.isDestroyed) {
      this.window.close()
    }
  }

  closeAllTab(contextId: BrowserContextIdentifier): void {
    const tabs = this.tabs.filter((tab) => tab.context.isTheSame(contextId))
    for (const tab of tabs) {
      this.removeTab(tab.id)
    }
  }

  private closeRightTab(tabId: string) {
    const findIndex = this.tabs.findIndex((tab) => tab.id === tabId)
    if (findIndex > -1) {
      const tabs = this.tabs.slice(findIndex + 1)
      for (const tab of tabs) {
        this.removeTab(tab.id)
      }
    }
  }

  private closeOtherTab(tabId: string) {
    const findIndex = this.tabs.findIndex((tab) => tab.id === tabId)
    if (findIndex > -1) {
      const tabs = this.tabs.filter((tab) => tab.id !== tabId)
      for (const tab of tabs) {
        this.removeTab(tab.id)
      }
    }
  }

  closeGroup(): void {
    for (const tab of this.tabs) {
      this.removeTab(tab.id)
    }
  }

  anyTabOpened(identifier: BrowserContextIdentifier) {
    return this.tabs.some((tab) => tab.context.isTheSame(identifier))
  }

  getOpenedUrls(identifier: BrowserContextIdentifier) {
    return this.tabs.filter((tab) => tab.context.isTheSame(identifier)).map((tab) => tab.url)
  }

  syncFavorites(contextFavorites: Record<string, ContextFavorite[]>) {
    if (this.headerView && this.headerView.webContents)
      this.headerView.webContents.send(browserChannel.syncFavorites, contextFavorites)
  }
}
