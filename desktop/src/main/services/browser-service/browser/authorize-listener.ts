import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import { AccountSpaceBrowserContext } from './browser-context'
import { globalBrowserContextManager } from './contextManager'
import { browserEvents } from '@main/services/eventBus/event/events'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import { tabManager } from './tab-manager'
import { getPlatformService } from '@main/services/platform-service/factory'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'

class AuthorizeListenerManager {
  init() {
    browserEventBus.on(browserEvents.tabsInContextIncreased, (contextId) => {
      if (!this.hasListener(contextId)) {
        const context = globalBrowserContextManager.get(contextId)
        if (
          !(context instanceof AccountSpaceBrowserContext) ||
          context.account?.type === 'wechatSubAccount'
        )
          return

        console.log('开始监听登录状态!!!', context.identifier)
        const listener = new AuthorizeListener(context)
        this.listeners.push(listener)
        void listener.startListen()
      } else {
        console.log('已经监听登录状态!!!', contextId)
        const listener = this.getListener(contextId)
        if (listener) {
          void listener.startListen()
        }
      }
    })

    browserEventBus.on(browserEvents.tabsInContextDecreased, (contextId) => {
      const context = globalBrowserContextManager.get(contextId)
      if (
        !(context instanceof AccountSpaceBrowserContext) ||
        context.account?.type === 'wechatSubAccount'
      )
        return

      const tabs = tabManager.getTabsByContext(contextId)
      if (tabs.length === 0 && this.hasListener(contextId)) {
        console.log('停止监听登录状态!!!', context.identifier)
        const listener = this.getListener(contextId)
        if (listener) {
          listener.abort()
          this.listeners.splice(this.listeners.indexOf(listener), 1)
        }
      }
    })
  }

  listeners: AuthorizeListener[] = []

  private hasListener(contextId: BrowserContextIdentifier) {
    return this.listeners.some((l) => l.context.isTheSame(contextId))
  }

  private getListener(contextId: BrowserContextIdentifier) {
    return this.listeners.find((l) => l.context.isTheSame(contextId)) ?? null
  }
}

class AuthorizeListener {
  constructor(public context: AccountSpaceBrowserContext) {}

  abortController: AbortController | null = null

  abort() {
    this.abortController?.abort()
  }

  async startListen() {
    if (this.abortController && !this.abortController.signal.aborted) {
      return
    }
    this.abortController = new AbortController()

    while (!this.abortController.signal.aborted) {
      console.log('检测登录状态!!!', this.context.identifier)
      try {
        const cookies = await this.context.session.cookies.get({})
        const accountSession = {
          cookies,
          localStorage: {},
        }
        const service = getPlatformService(this.context.platformName)
        const currentState = await service.sessionDetect(accountSession, true)

        const prevSessionState = this.context.sessionState
        this.context.sessionState = currentState

        console.log('检测完成!!!', currentState, prevSessionState, accountSession)

        if (prevSessionState === '已失效' && currentState === '正常') {
          console.log('登录成功!!!', this.context.identifier)

          const tabs = tabManager.getTabsByContext(this.context.identifier)

          // 等待平台特定的完成信号
          const platformConfig = (await getAuthorizeServicePromise()).platformConfig[
            this.context.platformName
          ]

          if (platformConfig && platformConfig.waitForLoginFinish)
            await platformConfig.waitForLoginFinish(
              this.context.identifier,
              tabs.map((x) => x.view),
              this.abortController.signal,
            )

          for (const tab of tabs) {
            accountSession.localStorage = {
              ...accountSession.localStorage,
              ...(await tab.view.webContents.executeJavaScript(`
      (function() {
        const localStorageData = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            const item = localStorage.getItem(key);
            if (item) localStorageData[key] = item;
          }
        }
        return localStorageData;
      })()
    `)),
            }
          }
          browserEventBus.emit(
            browserEvents.accountSessionStateChanged,
            this.context.identifier,
            accountSession,
          )
        } else if (prevSessionState === '正常' && currentState === '已失效') {
          console.log('登录失效!!!', this.context.identifier)
          browserEventBus.emit(
            browserEvents.accountSessionStateChanged,
            this.context.identifier,
            accountSession,
          )
        }

        if (currentState === '正常') {
          this.abort()
          // 补丁：有账号信息的情况下，登录成功后需要更新认证状态
          void service.getAccountInfo(accountSession.cookies).then((accountInfo) => {
            if (
              this.context.account &&
              accountInfo.identityVerified !== this.context.account.identityVerified
            ) {
              browserEventBus.emit(
                browserEvents.identifyVerified,
                this.context.identifier,
                accountInfo.identityVerified,
              )
            }
          })
        }
      } catch (e) {
        console.error(e)
      }
      await new Promise((resolve) => setTimeout(resolve, 3000))
    }
  }
}

export const authorizeListenerManager = new AuthorizeListenerManager()
