import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import type { SessionState } from '@common/structure/session-state'
import { browserEventBus } from '../../eventBus/eventBus'
import { accountContextEvents } from '../../eventBus/event/events'
import type { Session } from 'electron'
import type { AccountInfoStructure } from '@common/model/account-info'

export abstract class BrowserContext {
  constructor(
    public identifier: BrowserContextIdentifier,
    public color: string,
    public contextName: string,
    public session: Session,
  ) {}

  isTheSame(contextId: BrowserContextIdentifier) {
    return (
      this.identifier.userid === contextId.userid &&
      this.identifier.teamId === contextId.teamId &&
      this.identifier.contextId === contextId.contextId
    )
  }

  clear() {
    return this.session.clearStorageData()
  }

  getStoragePath() {
    return this.session.getStoragePath()!
  }

  async flush() {
    await this.session.cookies.flushStore()
    this.session.flushStorageData()
  }
}

export class WebSpaceBrowserContext extends BrowserContext {
  constructor(
    identifier: BrowserContextIdentifier,
    color: string,
    contextName: string,
    session: Session,
  ) {
    super(identifier, color, contextName, session)
  }
}

export class AccountSpaceBrowserContext extends BrowserContext {
  constructor(
    identifier: BrowserContextIdentifier,
    color: string,
    session: Session,
    public platformName: string,
    public account: AccountInfoStructure | null,
  ) {
    super(identifier, color, account?.displayName ?? '新授权', session)
    this._sessionState = account?.sessionState ?? '已失效'
  }

  private _sessionState: SessionState

  public get sessionState(): SessionState {
    return this._sessionState
  }
  public set sessionState(value: SessionState) {
    this._sessionState = value
    if (value !== this._sessionState) {
      browserEventBus.emit(accountContextEvents.sessionStateChange, this.identifier, value)
    }
  }
}

export class WechatShiPinHaoSpaceBrowserContext extends AccountSpaceBrowserContext {}
