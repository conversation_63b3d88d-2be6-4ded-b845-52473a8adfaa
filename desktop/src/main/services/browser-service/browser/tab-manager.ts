import { globalBrowserContextManager } from './contextManager'
import { browserEvents } from '../../eventBus/event/events'
import { browserEventBus } from '../../eventBus/eventBus'
import { AccountTab, Tab } from './tab'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'

export class TabManager {
  tabs: Tab[] = []

  private addTab(tab: Tab) {
    this.tabs.push(tab)
    browserEventBus.emit(browserEvents.tabsInContextIncreased, tab.context.identifier)
  }

  private removeTab(tab: Tab) {
    const index = this.tabs.indexOf(tab)
    if (index === -1) {
      return
    }
    this.tabs.splice(index, 1)
    browserEventBus.emit(browserEvents.tabsInContextDecreased, tab.context.identifier)
  }

  async openWebSpaceTab(tabId: string, contextIdentifier: BrowserContextIdentifier, url: string) {
    const context = globalBrowserContextManager.getWebSpaceContext(contextIdentifier)

    const tab = new Tab(tabId, context)
    tab.createView()
    await tab.initEvents()
    tab.launch(url)
    this.addTab(tab)
    return tab
  }

  async openAccountSpaceTab(
    tabId: string,
    contextIdentifier: BrowserContextIdentifier,
    url: string,
  ) {
    const context = globalBrowserContextManager.getAccountSpaceContext(contextIdentifier)

    const tab = new AccountTab(tabId, context)
    tab.createView()
    await tab.initEvents()
    tab.launch(url)
    this.addTab(tab)
    return tab
  }

  async openPublishTab(tabId: string, contextIdentifier: BrowserContextIdentifier, url: string) {
    const context = globalBrowserContextManager.getAccountSpaceContext(contextIdentifier)

    const tab = new AccountTab(tabId, context)

    tab.createView('../preload/index.js')
    await tab.initEvents()
    tab.launch(url)
    this.addTab(tab)
    return tab
  }

  getTabsByContext(contextId: BrowserContextIdentifier) {
    return this.tabs.filter((tab) => tab.context.isTheSame(contextId))
  }

  closeTab(tabId: string) {
    const tab = this.tabs.find((tab) => tab.id === tabId)
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`)
    }
    this.removeTab(tab)
    tab.close()
  }

  anyWebSpaceTabOpened(identifier: BrowserContextIdentifier) {
    const context = globalBrowserContextManager.getWebSpaceContext(identifier)
    if (!context) {
      return false
    }
    return this.tabs.some((tab) => tab.context.isTheSame(identifier))
  }

  anyAccountSpaceTabOpened(identifier: BrowserContextIdentifier) {
    const context = globalBrowserContextManager.getAccountSpaceContext(identifier)
    if (!context) {
      return false
    }
    return this.tabs.some((tab) => tab.context.isTheSame(identifier))
  }
}

export const tabManager = new TabManager()
