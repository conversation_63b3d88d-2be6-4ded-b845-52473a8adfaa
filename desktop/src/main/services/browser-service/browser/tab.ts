import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON>tem, WebContentsView } from 'electron'
import type { Group } from './group'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'
import { getUserAgentForURL } from '../user-agent'
import type {
  AccountSpaceBrowserContext,
  BrowserContext,
} from '@main/services/browser-service/browser/browser-context'

import type { SessionState } from '@common/structure'

export class Tab {
  public view!: WebContentsView
  public icon: string | null = null

  constructor(
    public id: string,
    public context: BrowserContext,
  ) {}

  public get url() {
    return this.view.webContents.getURL()
  }
  public get title() {
    return this.view.webContents.getTitle()
  }

  public get canAddFavorite() {
    return true
  }

  createView(preloadPath?: string) {
    const absolutePreloadPath = preloadPath ? require.resolve(preloadPath) : undefined
    console.log('absolutePreloadPath', absolutePreloadPath)
    this.view = new WebContentsView({
      webPreferences: {
        preload: absolutePreloadPath,
        sandbox: !preloadPath,
        session: this.context.session,
      },
    })
  }

  async initEvents() {
    this.view.webContents.on('did-start-navigation', (e, url, isInPlace, isMainFrame) => {
      if (!isMainFrame) return
      const newUA = getUserAgentForURL(this.view.webContents.userAgent, url)
      if (this.view.webContents.userAgent !== newUA) {
        this.view.webContents.userAgent = newUA
      }
    })

    this.view.webContents.on('context-menu', () => {
      if (import.meta.env.DEV) {
        const menu = new Menu()
        menu.append(
          new MenuItem({
            label: '检查',
            click: () => {
              this.view.webContents.openDevTools()
            },
          }),
        )
        menu.popup()
      }
    })

    this.view.webContents.on('page-favicon-updated', async (_, [url]) => {
      if (url) {
        this.icon = url
        browserEventBus.emit('tab-favicon-updated', this.id, this.context.identifier, url)
      }
    })

    this.view.webContents.setWindowOpenHandler((details) => {
      console.log('window-open', details)
      switch (details.disposition) {
        case 'new-window':
          return {
            action: 'allow',
            createWindow: (options) => {
              const browserWindow = new BrowserWindow(options)
              browserWindow.setMenu(null)
              return browserWindow.webContents
            },
          }
        case 'default':
        case 'other':
        case 'foreground-tab':
        case 'background-tab':
        default:
          return { action: 'deny' }
      }
    })
  }

  bindGroup(group: Group) {
    this.setBounds(group)
    this.view.webContents.on('did-finish-load', () => {
      this.view.isLoad = true
      browserEventBus.emit('tab-loaded' + group.identifier, this.id)
    })

    this.view.webContents.on('did-navigate-in-page', () => {
      browserEventBus.emit('tab-loaded' + group.identifier, this.id)
    })

    this.view.webContents.on('destroyed', () => {
      group.removeTab(this.id)
    })

    this.view.webContents.setWindowOpenHandler((details) => {
      console.log('window-open', details)
      switch (details.disposition) {
        case 'new-window':
          return {
            action: 'allow',
            createWindow: (options) => {
              const browserWindow = new BrowserWindow(options)
              browserWindow.setMenu(null)
              return browserWindow.webContents
            },
          }
        case 'default':
        case 'other':
        case 'foreground-tab':
        case 'background-tab':
        default:
          browserEventBus.emit('open-new-page' + group.identifier, {
            url: details.url,
            from: this,
          })
          return { action: 'deny' }
      }
    })
  }

  launch(url: string) {
    void this.view.webContents.loadURL(url)
  }

  setVisible(visible: boolean) {
    this.view.setVisible(visible)
  }

  close(): void {
    if (!this.view.webContents || this.view.webContents.isDestroyed()) return
    this.view.webContents.close()
  }

  setBounds(group: Group) {
    const bounds = group.window.contentView.getBounds()
    this.view.setBounds({
      x: bounds.x,
      y: bounds.y + 40,
      width: bounds.width,
      height: bounds.height - 40,
    })
  }

  get canGoBack() {
    return this.view.webContents.canGoBack()
  }

  get canGoForward() {
    return this.view.webContents.canGoForward()
  }

  goBack() {
    this.view.webContents.goBack()
  }

  goForward() {
    this.view.webContents.goForward()
  }

  refresh() {
    this.view.webContents.reload()
  }
}

export class AccountTab extends Tab {
  get sessionState() {
    return this.context.sessionState
  }

  set sessionState(value: SessionState) {
    this.context.sessionState = value
  }

  constructor(
    public id: string,
    public context: AccountSpaceBrowserContext,
  ) {
    super(id, context)
  }

  get platformName() {
    return this.context.platformName
  }

  get accountId() {
    return this.context.account?.accountId ?? null
  }

  override async initEvents(): Promise<void> {
    await super.initEvents()

    const platformConfig = (await getAuthorizeServicePromise()).platformConfig[this.platformName]

    if (!platformConfig) return

    platformConfig.initView(this.view, this.context.account)
  }

  get canAddFavorite() {
    return this.accountId !== null
  }
}
