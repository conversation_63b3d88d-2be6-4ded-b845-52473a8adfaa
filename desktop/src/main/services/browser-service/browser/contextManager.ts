import { session } from 'electron'
import { pathService } from '@main/services/path-service'
import fsExtra from 'fs-extra'
import { compressionService } from '@main/services/compression-service'
import { Lock } from '@main/model/lock'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import path from 'path'
import { fileSystemService } from '@main/services/fileSystemService'
import type { BrowserContext } from '@main/services/browser-service/browser/browser-context'
import { AccountSpaceBrowserContext } from '@main/services/browser-service/browser/browser-context'
import { WebSpaceBrowserContext } from '@main/services/browser-service/browser/browser-context'
import type { AccountSession } from '@common/structure'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'
import type { AccountInfoStructure } from '@common/model/account-info'

export class BrowserContextManager {
  private dumpLock = new Lock()

  public contexts: BrowserContext[] = []

  async openWebSpaceContext(
    contextIdentifier: Browser<PERSON>ontextIdentifier,
    color: string,
    contextName: string,
    sessionZipBuffer: Buffer | undefined = undefined,
  ) {
    if (!this.isOpened(contextIdentifier)) {
      const session = await this.openSession(contextIdentifier, sessionZipBuffer)
      this.addWebSpaceContext(contextIdentifier, color, contextName, session)
    }
    return this.get(contextIdentifier)
  }

  private async openSession(
    contextIdentifier: BrowserContextIdentifier,
    sessionZipBuffer: Buffer | undefined,
  ) {
    if (!this.isCreated(contextIdentifier)) {
      return await this.createNewSession(contextIdentifier, sessionZipBuffer)
    } else {
      return this.restoreSession(contextIdentifier)
    }
  }

  async openAccountSpaceContext(
    contextIdentifier: BrowserContextIdentifier,
    color: string,
    platformName: string,
    account: AccountInfoStructure | null,
    sessionZipBuffer: Buffer | undefined = undefined,
    accountSession: AccountSession | null,
  ) {
    if (!this.isOpened(contextIdentifier)) {
      const session = await this.openSession(contextIdentifier, sessionZipBuffer)
      this.addAccountContext(contextIdentifier, color, session, platformName, account)
    } else {
      this.updateAccountContext(contextIdentifier, account)
    }
    if (accountSession) await this.injectCookie(contextIdentifier, accountSession)
    return this.get(contextIdentifier)
  }

  isOpened(contextId: BrowserContextIdentifier) {
    return this.contexts.some((context) => context.isTheSame(contextId))
  }

  isCreated(contextId: BrowserContextIdentifier) {
    const sessionPath = this.getSessionPath(contextId)
    return fsExtra.existsSync(sessionPath)
  }

  get(contextId: BrowserContextIdentifier) {
    const context = this.contexts.find((context) => context.isTheSame(contextId))
    if (!context) {
      throw new Error(`Context ${contextId.contextId} not found`)
    }
    return context
  }

  getWebSpaceContext(contextIdentifier: BrowserContextIdentifier) {
    const context = this.contexts
      .filter((context) => context instanceof WebSpaceBrowserContext)
      .find((context) => context.isTheSame(contextIdentifier))
    if (!context) {
      throw new Error(`Context ${contextIdentifier.contextId} not found`)
    }
    return context
  }

  getAccountSpaceContext(contextIdentifier: BrowserContextIdentifier) {
    const context = this.contexts
      .filter((context) => context instanceof AccountSpaceBrowserContext)
      .find((context) => context.isTheSame(contextIdentifier))
    if (!context) {
      throw new Error(`Context ${contextIdentifier.contextId} not found`)
    }
    return context
  }

  public getSessionPath(identifier: BrowserContextIdentifier): string {
    return pathService.getSessionPath(identifier)
  }

  async createNewSession(contextId: BrowserContextIdentifier, sessionZip?: Buffer | undefined) {
    const targetPath = this.getSessionPath(contextId)

    if (sessionZip) {
      await this.dumpLock.acquire()
      const tempPath = pathService.getDumpSessionPath(contextId.userid, contextId.teamId)
      fsExtra.removeSync(tempPath)
      await compressionService.decompress(sessionZip, tempPath)
      fsExtra.copySync(tempPath, targetPath)
      this.dumpLock.release()
    }
    return this.sessionFromPath(targetPath)
  }

  async dumpContext(contextId: BrowserContextIdentifier) {
    if (this.isOpened(contextId)) {
      const sourceContext: BrowserContext = this.get(contextId)
      await sourceContext.flush()
    }

    const sourcePath = this.getSessionPath(contextId)

    const tempPath = pathService.getDumpSessionPath(contextId.userid, contextId.teamId)

    await this.dumpLock.acquire()
    fileSystemService.remove(tempPath)
    console.log('dumping', sourcePath, tempPath)
    fileSystemService.copyDirectory(sourcePath, tempPath, {
      blacklist: [/^blob_storage.*/, /.*[Cc]ache.*/],
    })
    const buffer = await compressionService.compress(tempPath, {
      blacklist: [/^blob_storage.*/, /.*[Cc]ache.*/],
    })
    this.dumpLock.release()
    return buffer
  }

  addWebSpaceContext(
    contextId: BrowserContextIdentifier,
    color: string,
    contextName: string,
    session: Electron.CrossProcessExports.Session,
  ) {
    this.contexts.push(new WebSpaceBrowserContext(contextId, color, contextName, session))
  }

  addAccountContext(
    contextId: BrowserContextIdentifier,
    color: string,
    session: Electron.CrossProcessExports.Session,
    platformName: string,
    account: AccountInfoStructure | null,
  ) {
    this.contexts.push(
      new AccountSpaceBrowserContext(contextId, color, session, platformName, account),
    )
  }

  updateAccountContext(contextId: BrowserContextIdentifier, account: AccountInfoStructure | null) {
    const context = this.get(contextId)
    if (context instanceof AccountSpaceBrowserContext) {
      context.account = account
      context.sessionState = account?.sessionState ?? '已失效'
    }
  }

  restoreSession(contextId: BrowserContextIdentifier) {
    const targetPath = this.getSessionPath(contextId)
    return this.sessionFromPath(targetPath)
  }

  private sessionFromPath(path: string): Electron.Session {
    const newSession = session.fromPath(path)

    newSession.protocol.handle('bitbrowser', () => {
      console.log('bitbrowser协议被请求，已忽略')
      throw new Error('bitbrowser')
    })
    newSession.protocol.handle('bytedance', () => {
      console.log('bytedance协议被请求，已忽略')
      throw new Error('bytedance')
    })
    return newSession
  }

  cleanupSessionDirectory(userId: string, teamId: string, existedContextIds: string[]) {
    const sessionBasePath = pathService.getSessionBasePath(userId, teamId)
    const existedPaths = existedContextIds.map((contextId) =>
      pathService.getSessionPath({
        userid: userId,
        teamId: teamId,
        contextId,
      }),
    )
    if (!fsExtra.existsSync(sessionBasePath)) return
    const allPaths = fsExtra
      .readdirSync(sessionBasePath)
      .map((name) => path.join(sessionBasePath, name))
    const toRemove = allPaths.filter((path) => !existedPaths.includes(path))
    console.log('session cleanup start', allPaths, toRemove)
    toRemove.forEach((path) => fsExtra.removeSync(path))
    console.log('session cleanup finished')
  }

  private async injectCookie(contextId: BrowserContextIdentifier, accountSession: AccountSession) {
    const context = this.get(contextId)
    if (context instanceof AccountSpaceBrowserContext) {
      const config = (await getAuthorizeServicePromise()).platformConfig[context.platformName]
      if (!config) {
        console.error('platform config not found', context.platformName)
        return
      }
      const entryUrl = config.entryUrl
      for (const detail of accountSession.cookies.map((cookie) => ({ url: entryUrl, ...cookie }))) {
        await context.session.cookies.set(detail)
      }
    }
  }
}

export const globalBrowserContextManager = new BrowserContextManager()
