export const browserEvents = {
  headerInitialed: 'HeaderInitialed',
  tabsInContextIncreased: 'tabsInContextIncreased',
  tabsInContextDecreased: 'tabsInContextDecreased',
  accountSessionStateChanged: 'accountSessionStateChanged',
  identifyVerified: 'identifyVerified',
}

export const accountContextEvents = {
  sessionStateChange: 'sessionStateChange',
}

export const publishEvents = {
  getAccountSession: 'publish.getAccountSession',
  getAccountSessionReply: 'publish.getAccountSessionReply',
  getAccountSessionFailed: 'publish.getAccountSessionFailed',
}
