import { assert, test, vi } from 'vitest'
import { getTimestamp } from '@common/infrastructure/timestamp'
import { PushingScheduledTask, PushingTaskScheduler } from './pushing'

test('在3个并发限制下，应该按照入队顺序和锁标识互斥的原则顺序执行', async () => {
  const scheduler = new PushingTaskScheduler(3)

  function push_task(lockIdentity: string, content: number) {
    return scheduler.push(
      new PushingScheduledTask(content.toString(), lockIdentity, async () => {
        await new Promise<void>((resolve) => {
          setTimeout(() => {
            console.log(`${getTimestamp()} task ${content} done`)
            resolve()
          }, 500)
        })
      }),
    )
  }

  // Mock timers
  vi.useFakeTimers()

  push_task('group1', 11)
  push_task('group1', 12)
  push_task('group1', 13)

  push_task('group2', 21)
  push_task('group2', 22)
  push_task('group2', 23)

  push_task('group3', 31)
  push_task('group3', 32)
  push_task('group3', 33)

  push_task('group4', 41)
  push_task('group4', 42)
  push_task('group4', 43)

  // Advance timers to ensure all tasks complete
  vi.advanceTimersByTime(5000)

  // Restore real timers
  vi.useRealTimers()
})

test('应该截停任务', async () => {
  const scheduler = new PushingTaskScheduler(3)

  const result: string[] = []

  function push_task(lockIdentity: string, content: number) {
    return scheduler.push(
      new PushingScheduledTask(
        content.toString(),
        lockIdentity,
        async (setCancelCallback: (cancel: () => void) => void) => {
          await new Promise<void>((resolve) => {
            const timeout = setTimeout(() => {
              console.log(`${getTimestamp()} task ${content} done`)
              result.push(`${getTimestamp()} task ${content} done`)
              resolve()
            }, 100)
            console.log(`${getTimestamp()} task ${content} started`)
            setCancelCallback(() => {
              console.log(`${getTimestamp()} task ${content} canceled`)
              clearTimeout(timeout)
              resolve()
            })
          })
        },
      ),
    )
  }

  // Mock timers
  vi.useFakeTimers()

  push_task('group1', 11)
  push_task('group1', 12)
  push_task('group1', 13)

  push_task('group2', 21)
  push_task('group2', 22)
  push_task('group2', 23)

  push_task('group3', 31)
  push_task('group3', 32)
  push_task('group3', 33)

  push_task('group4', 41)
  push_task('group4', 42)
  push_task('group4', 43)

  setTimeout(() => {
    scheduler.stop()
  }, 150)

  // Advance timers to ensure all tasks complete
  vi.advanceTimersByTime(500)

  // Restore real timers
  vi.useRealTimers()

  console.log(result)
  assert.isTrue(result.length < 12)
  assert.isTrue(result.length === 0)
})
