import { TaskScheduler } from '../scheduler'
import { ScheduledTaskBase } from '../task'

export class PushingScheduledTask extends ScheduledTaskBase {}

export class PushingTaskScheduler extends TaskScheduler<PushingScheduledTask> {
  push(task: PushingScheduledTask): void {
    if (this.queue.find((t) => t.taskId === task.taskId)) {
      return
    }
    this.queue.push(task)
  }

  protected tryGetTaskFromQueue() {
    const task = this.queue.find((t) => !this.isTaskRunning(t.runMutexId))
    if (task) {
      this.queue = this.queue.filter((t) => t.taskId !== task.taskId)
      return task
    }
    return null
  }
}
