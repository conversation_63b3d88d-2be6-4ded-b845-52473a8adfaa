import type { ScheduledTaskBase } from './task'

export class TaskProcessor {
  public runningIdentity: string | null
  private endSignal: boolean = false
  private task: ScheduledTaskBase | null = null

  constructor(private getTask: () => Promise<ScheduledTaskBase>) {
    this.runningIdentity = null
    void this.process()
  }

  public end() {
    this.endSignal = true
    this.task?.cancel?.()
  }

  private async process() {
    while (!this.endSignal) {
      this.task = await this.getTask()
      this.runningIdentity = this.task.runMutexId
      await this.task.run(this.task.setCancelCallback)
      this.runningIdentity = null
    }
  }
}
