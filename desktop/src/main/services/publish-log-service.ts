import type { AccountSession } from '@common/structure'
import type { Logger } from 'electron-log'
import log from 'electron-log'
import path from 'path'
import fs from 'fs-extra'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import { pathService } from './path-service'

export class PublishLogService {
  constructor(private logger: Logger) {}

  publishScheduling(
    taskId: string,
    platform: string,
    authorId: string,
    authorName: string,
    wechatToken: [string, string] | null,
    accountSession: AccountSession,
    body: LiteralObject,
  ) {
    this.logger.info(
      `publish scheduling: ${taskId} ${platform} ${authorId} ${authorName} ${wechatToken} accountSession:${JSON.stringify(accountSession)} body:${JSON.stringify(body)}`,
    )
  }

  logPublishSuccess(taskId: string, publishId: string) {
    this.logger.info(`Publish success: ${taskId} ${publishId}`)
  }

  logPublishFail(taskId: string, e: unknown) {
    this.logger.error(`Publish fail: ${taskId} ${JSON.stringify(e)}`)
  }
}

const logger = log.create({
  logId: 'Publish',
})

logger.transports.console.level = false

// Set the maximum log size to 5 MB
logger.transports.file.maxSize = 5 * 1024 * 1024

// Define the custom archive function
logger.transports.file.archiveLogFn = (oldLogFile) => {
  const archivePath = path.dirname(oldLogFile.path)
  const timestamp = Date.now()
  const newLogFile = path.join(
    archivePath,
    `${path.basename(oldLogFile.path, '.log')}-${timestamp}.log`,
  )
  fs.renameSync(oldLogFile.path, newLogFile)
}

logger.transports.file.resolvePathFn = () => {
  const logDir = pathService.getLogPath()
  return path.join(logDir, 'publish.log')
}

export const publishLogService = new PublishLogService(logger)
