import { app } from 'electron'
import path from 'path'
import type { SpaceIdentifier } from '@common/structure/space/space-identifier'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import log from 'electron-log'

class PathService {
  getAppTemperaturePath(...relativePaths: string[]): string {
    return path.join(app.getPath('temp'), 'yixiaoer-lite', ...relativePaths)
  }

  getUserDataPath(...relativePaths: string[]): string {
    return path.join(app.getPath('userData'), ...relativePaths)
  }

  getSessionPath(contextIdentifier: BrowserContextIdentifier): string {
    return this.getUserTeamPath(
      contextIdentifier.userid,
      contextIdentifier.teamId,
      'session',
      contextIdentifier.contextId,
    )
  }

  getDumpSessionPath(userid: string, teamId: string) {
    return this.getUserTeamPath(userid, teamId, 'session', 'dump')
  }

  getOnlineScripts(...relativePaths: string[]) {
    return this.getUserDataPath('online-scripts', ...relativePaths)
  }

  getCacheImageDir(...relativePaths: string[]) {
    return this.getUserDataPath('cache-image', ...relativePaths)
  }

  getRpaFilePath() {
    return import.meta.env.DEV
      ? path.join(process.cwd(), 'rpa', 'dist', 'index.js')
      : this.getOnlineScripts('rpa.js')
  }

  getOnlineScriptConifgPath() {
    return this.getOnlineScripts('config.json')
  }

  getReptileFilePath() {
    return this.getOnlineScripts('reptile.js')
  }

  getTemporarySessionPath(userid: string, teamId: string) {
    return this.getUserTeamPath(userid, teamId, 'session', 'temp')
  }

  getSessionBasePath(userid: string, teamId: string) {
    return this.getUserTeamPath(userid, teamId, 'session')
  }

  getSpaceIconPath(spaceIdentifier: SpaceIdentifier) {
    return this.getSpacePath(spaceIdentifier, 'favicon.ico')
  }

  getUserTeamPath(userid: string, teamId: string, ...relativePaths: string[]) {
    return this.getUserDataPath('yi-users', `user${userid}`, `team${teamId}`, ...relativePaths)
  }

  private getSpacePath(spaceIdentifier: SpaceIdentifier, icon: string) {
    return this.getUserTeamPath(
      spaceIdentifier.userid,
      spaceIdentifier.teamId,
      'space',
      spaceIdentifier.spaceId,
      icon,
    )
  }

  getLogPath() {
    return this.getUserDataPath('logs')
  }
}

export const pathService = new PathService()

console.debug('AppTemperaturePath目录:', pathService.getAppTemperaturePath())
console.debug('getUserDataPath目录:', pathService.getUserDataPath())
console.debug('主log文件:', log.transports.file.getFile().path)
console.debug('logs目录:', pathService.getLogPath())
