import '@renderer/css/reset.css'
import '@renderer/css/globals.css'
import '@renderer/css/app.css'

import '@common/webAPIAdapter'

import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import { registerIpcHandlers } from '@renderer/services/ipc-handler'
import 'react-photo-view/dist/react-photo-view.css'

import { enableMapSet } from 'immer'

enableMapSet()

registerIpcHandlers()

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
