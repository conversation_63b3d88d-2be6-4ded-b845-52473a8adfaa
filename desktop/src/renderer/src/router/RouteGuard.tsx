import { useEffect, useRef } from 'react'
import type { Location } from 'react-router-dom'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import type { BusinessContext } from '@common/model/business-context'
import { uiEvents } from '@common/events/ui-events'
import { checkLogin } from '@/lib/http/'

export function RouteGuard() {
  const navigate = useNavigate()
  const location = useLocation()
  const prevLocation = useRef<Location | null>(null)

  function getBusinessContext(pathname: string | null): BusinessContext {
    if (pathname === null) {
      return null
    } else if (pathname === '/login') {
      return 'Anonymous'
    } else {
      return 'User'
    }
  }

  const currentContext = getBusinessContext(location.pathname)
  const prevContext = getBusinessContext(prevLocation.current?.pathname ?? null)

  useEffect(() => {
    prevLocation.current = location
  }, [location])

  useEffect(() => {
    console.log(prevContext, currentContext)
    if (prevContext !== null && currentContext !== prevContext) {
      window.api.send(uiEvents.businessContextChanged, currentContext)
    }
  }, [currentContext, location, prevContext])

  useEffect(() => {
    async function logout(): Promise<void> {
      navigate('/login')
    }

    if (currentContext !== 'Anonymous' && !checkLogin()) {
      void logout()
    }
  }, [currentContext, location, navigate])

  return <Outlet />
}
