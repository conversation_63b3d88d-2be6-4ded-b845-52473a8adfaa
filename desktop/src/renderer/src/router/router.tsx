import type { RouteObject } from 'react-router-dom'
import { createBrowserRouter } from 'react-router-dom'
import { Tryout } from '@renderer/pages/tryouts/Tryout'
import { Login } from '@renderer/pages/Login/Login'
import { RouteGuard } from '@renderer/router/RouteGuard'
import { MainProvider } from '@renderer/pages/MainProvider'

const routes: RouteObject[] = [
  {
    element: <RouteGuard />,
    children: [
      {
        path: '/',
        element: <MainProvider />,
      },
      {
        path: '/login',
        element: <Login />,
      },
      {
        path: '/tryout',
        element: <Tryout />,
      },
      {
        element: <MainProvider />,
        path: '/newMain',
      },
    ],
  },
]

export const router = createBrowserRouter(routes)
