.szh-menu[data-open='true'] {
  z-index: 9999;
}

.Preview-Root {
  overflow: hidden;
  /* width: 80vw; */
  height: 76vh;
  flex: 1;
}

@media screen and (max-width: 1296px) {
  .Preview-Root {
    height: 60vh;
  }
}

.Preview-Root + button {
  color: white;
}

.AssetLibraryPreview-Root {
  color: #fff;
  gap: 0 !important;
}

.AssetLibraryPreview-Root h2 {
  padding: 20px 24px;
}

.PreviewVideo-Root {
  position: relative;
  height: 100%;
  width: 100%;
  --VideoControl-show: 0;
}
.PreviewVideo-Root:hover {
  --VideoControl-show: 1;
}

.Preview-Root .react-transform-wrapper,
.Preview-Root .react-transform-component {
  height: 80vh;
  width: 100%;
}

.Preview-TitleBar {
  display: flex;
  align-items: center;
  height: 40px;
  gap: 4px;
  padding: 0 8px 0 8px;
  -webkit-app-region: drag;
}

.Preview-TitleBar .MuiIconButton-root {
  -webkit-app-region: no-drag;
  height: 26px;
  width: 26px;
}

.Preview-Divider {
  height: 50%;
  margin: 0 8px;
}

.Preview-SwiperSlide {
  /* background-color: #f0f0f0; */
  /* height: atuo !important;
  width: 100vw; */
  width: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.WindowsAction-root {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.AssetLibrary-Image {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
}
.AssetLibrary-Image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.AssetLibrary-Image:hover .AssetLibrary-MenuMore {
  pointer-events: auto;
  opacity: 1;
}

.AssetLibrary-Image .AssetLibrary-MenuMore {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 8px;
  right: 8px;
}

.szh-menu {
  padding: 4px !important;
  border-radius: 8px !important;
}
.szh-menu__item {
  border-radius: 6px !important;
  padding-inline: 12px !important;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.szh-menu__item:hover {
  background-color: #f1f1fd;
}

.AssetLibrary-Menu {
  width: 100%;
  display: flex !important;
  gap: 8px;
  padding: 8px 8px 8px 14px !important;
}

.AssetLibrary-GroupInput {
  outline-offset: 0 !important;
  --tw-ring-offset-width: 0px !important;
  padding: 8px 8px 8px 14px !important;
}

.AssetLibrary-Menu .AssetLibrary-MenuIcon {
  color: #969499;
}

.AssetLibrary-Menu[data-active='true'] {
  background-color: hsl(var(--accent));
}

.AssetLibrary-Menu[data-active='true'] .AssetLibrary-MenuIcon,
.AssetLibrary-Menu:hover .AssetLibrary-MenuIcon {
  color: #4f46e5 !important;
}
.AssetLibrary-Menu:hover .AssetLibrary-MenuMore {
  pointer-events: auto;
  opacity: 1;
}
.AssetLibrary-MenuMore {
  padding: 4px;
  border-radius: 4px;
  color: #807f7f;
  pointer-events: none;
  opacity: 0;
}
.AssetLibrary-MenuMore:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.AssetLibrary-Menu[data-active='true'],
.AssetLibrary-Menu:hover {
  color: #4f46e5 !important;
}

.AssetLibrary-Type {
  padding: 12px;
  font-size: 12px;
  color: #7f7e80;
}

.AssetLibrary-Store {
  padding: 16px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-top: 1px solid #f0f0f0;
}

.AssetLibrary-Checkbox {
  position: absolute;
  left: 8px;
  top: 8px;
  background-color: white;
}

.AssetLibrary-Image[data-type='image'] {
  --asset-library-image-show-opacity: 0;
}
.AssetLibrary-Image[data-type='image']:hover {
  --asset-library-image-show-opacity: 1;
}
.AssetLibrary-ImageClick {
  opacity: var(--asset-library-image-show-opacity);
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  width: 70%;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.PreviewVideo-Root {
  display: flex;
  flex-direction: column;
}

.PreviewImage-button {
  transition: transform 0.3s;
  cursor: pointer;
  z-index: 99;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.PreviewImage-button:hover {
  transform: translateY(-50%) scale(1.1);
}

.AssetLibrary-list {
  display: grid;
  flex: 1;
  gap: 8px;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
}

@media screen and (min-width: 1296px) {
  .AssetLibrary-list {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
}
