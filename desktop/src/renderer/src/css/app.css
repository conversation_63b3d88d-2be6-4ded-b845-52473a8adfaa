.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}

html,
body {
  height: 100%;
  width: 100%;
}

.tiptap topic {
  color: hsl(var(--primary));
  margin: 0 4px;
  user-select: auto;
}

.electron-drag-region {
  -webkit-app-region: drag;
  & > * {
    -webkit-app-region: no-drag;
  }
  .drag-handler {
    -webkit-app-region: drag;
  }
}

.electron-drag {
  -webkit-app-region: drag;
}

.electron-no-drag {
  -webkit-app-region: no-drag;
}

body:has(div[data-radix-popper-content-wrapper]),
body:has(div[data-state='open']) {
  .electron-drag-region {
    -webkit-app-region: no-drag;
    .drag-handler {
      -webkit-app-region: no-drag;
    }
  }
  .electron-drag {
    -webkit-app-region: no-drag;
  }
}

.table-ping-left::after {
  box-shadow: inset 10px 0 8px -8px rgba(5, 5, 5, 0.06);
}
.table-cell-fix-left-first::after,
.table-cell-fix-left-last::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 30px;
  transform: translateX(100%);
  transition: box-shadow 0.3s;
  content: '';
  pointer-events: none;
}

.scroll-shadow-top,
.scroll-shadow-bottom {
  position: relative;
}

.scroll-shadow-top::before {
  top: 0;
  box-shadow: inset 0 10px 10px -10px rgba(0, 0, 0, 0.2);
}

.scroll-shadow-bottom::after {
  bottom: 0;
  box-shadow: inset 0 -10px 10px -10px rgba(0, 0, 0, 0.2);
}
.scroll-shadow-top::before,
.scroll-shadow-bottom::after {
  position: absolute;
  right: 0;
  left: 0;
  height: 30px;
  /* transform: translateY(100%); */
  transition: box-shadow 0.3s;
  content: '';
  pointer-events: none;
}

.Throttle-root {
  pointer-events: auto;
  animation: throttle 2s step-end forwards;
}

.Throttle-root:active {
  animation: none;
}

@keyframes throttle {
  from {
    cursor: not-allowed;
    pointer-events: none;
  }
  to {
    pointer-events: auto;
  }
}
