import { windowEvents } from '@common/events/ui-events'

export class WindowService {
  maximize() {
    return window.api.invoke(windowEvents.maximum)
  }

  minimize() {
    return window.api.invoke(windowEvents.minimum)
  }

  close() {
    return window.api.invoke(windowEvents.close)
  }

  moveBy(dx: number, dy: number) {
    return window.api.invoke(windowEvents.move, dx, dy)
  }
}

export const windowService = new WindowService()
