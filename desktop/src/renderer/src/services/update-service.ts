import { uiEvents } from '@common/events/ui-events'
import type { autoUpdateInfo } from '@common/model'
import { useUpdateStore } from '@/stores/updateStore'
import type { ProgressInfo, UpdateInfo } from 'electron-updater'
import { throttle } from 'lodash'

class UpdateService {
  // 监听应用更新事件
  on = (args: autoUpdateInfo) => {
    const { event, data, autoDownload } = args
    console.log(event, data, autoDownload)
    const { setNewVersion, setStatus, setProgress, setExceptionMsg } = useUpdateStore.getState()
    switch (event) {
      case 'update-available':
        setNewVersion((data as UpdateInfo).version)
        setStatus('available')
        break
      case 'update-not-available':
        setStatus('latest')
        break
      case 'download-progress':
        setProgress((data as ProgressInfo).percent)
        setStatus('downloading')
        break
      case 'update-downloaded':
        setStatus('pending')
        setProgress(0)
        break
      case 'error':
        setStatus('exception')
        setExceptionMsg((data as Error).message || data?.toString() || 'Unknown error')
        break
      default:
        break
    }
  }

  // 检查自动更新
  checkAutoUpdate = throttle(
    (autoDownload = false, autoQuit = false, url = '') => {
      return window.api.send(uiEvents.autoUpdate, {
        handle: 'checkUpdate',
        data: { autoDownload, autoQuit, url },
      })
    },
    500,
    { trailing: false },
  )

  // 下载更新包
  downloadUpdate = throttle(
    () => {
      return window.api.send(uiEvents.autoUpdate, {
        handle: 'downloadUpdate',
      })
    },
    500,
    { trailing: false },
  )

  // 更新完成重启安装
  quitAndInstall = throttle(
    () => {
      return window.api.send(uiEvents.autoUpdate, {
        handle: 'quitAndInstall',
      })
    },
    500,
    { trailing: false },
  )
}

export const updateService = new UpdateService()
