import { useContextStore } from '@renderer/store/contextStore'
import { useEffect, useState } from 'react'
import { CircleAlert, X } from 'lucide-react'
import { CustomerServiceDialog } from '@renderer/components/vip/CustomerServiceDialog'
import { useVipDialog } from '@renderer/pages/Vip/vipDialogProvider'

export function VipPopoverTips() {
  const currentTeam = useContextStore((state) => state.currentTeam)

  const [showTips, setShowTips] = useState(false)

  const [daysRemaining, setDaysRemaining] = useState(0)

  const [customerDialog, setCustomerDialog] = useState(false)

  const timeKey = 'closeTimestamp'
  const todayTimestamp = new Date(new Date().setHours(0, 0, 0, 0)).getTime()
  // 不再提示
  const noReminder = localStorage.getItem('noReminder') || ''

  function getDaysRemaining() {
    if (!currentTeam?.isVip) {
      localStorage.removeItem('noReminder')
      localStorage.removeItem(timeKey)
      setShowTips(false)
      return
    }
    const expiredTime = new Date(currentTeam?.expiredAt).getTime()

    // 剩余天数
    const daysRemaining = Math.ceil((expiredTime - Date.now()) / (1000 * 60 * 60 * 24))
    setDaysRemaining(daysRemaining)

    const localTimestamp = localStorage.getItem(timeKey) || ''

    if (noReminder === expiredTime.toString()) {
      setShowTips(false)
      return
    } else {
      localStorage.removeItem('noReminder')
    }

    if (daysRemaining > 0 && daysRemaining < 6 && localTimestamp !== todayTimestamp.toString()) {
      setShowTips(true)
    }
  }

  // 显示剩余天数
  useEffect(() => {
    getDaysRemaining()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTeam?.isVip])

  const closeTips = () => {
    localStorage.setItem(timeKey, todayTimestamp.toString())
    setShowTips(false)
  }

  const notRemind = () => {
    const expiredTime = new Date(currentTeam?.expiredAt ?? 0).getTime()
    setShowTips(false)
    localStorage.setItem('noReminder', expiredTime.toString())
    localStorage.removeItem(timeKey)
  }

  const { upgrade } = useVipDialog()
  return (
    <>
      {showTips && (
        <div
          className={'align-center flex justify-between bg-[#ffab001a] px-[20px] py-3 leading-10'}
        >
          <div className={'flex items-center'}>
            <CircleAlert size={20} fill={'#ffab00'} color={'#fff'} />
            <span className={'ml-1 text-sm'}>
              当前团队VIP还剩余
              <span className={'px-1 font-semibold text-[#DC0629]'}>{daysRemaining}</span>
              天过期，过期后将冻结全部账号、成员。
            </span>
            <a
              onClick={upgrade}
              className={'ml-2 cursor-pointer text-sm text-[#7A4100] hover:underline'}
            >
              去续费
            </a>
          </div>
          <div className={'flex items-center leading-10'}>
            <span className={'cursor-pointer text-sm text-[#757575]'} onClick={() => notRemind()}>
              不在提醒
            </span>
            <span className={'mx-4 ml-[16px] w-[1px] bg-[#a5a5a5] py-[8px]'}></span>
            <X
              className={'cursor-pointer'}
              color={'#757575'}
              size={20}
              onClick={() => closeTips()}
            />
          </div>
        </div>
      )}
      <CustomerServiceDialog
        open={customerDialog}
        openChange={setCustomerDialog}
      ></CustomerServiceDialog>
    </>
  )
}
