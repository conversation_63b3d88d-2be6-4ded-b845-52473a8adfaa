import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useVipStore } from '@renderer/store/vipStore'
import vipbanner from '@renderer/assets/vip/vipbanner.png'
import vipbannerfont from '@renderer/assets/vip/vipbannerfont.png'
import { Button } from '@/components/ui/button'

interface VipUpgradeDialogProps {
  open: boolean
  openChange: (open: boolean) => void
}

export function VipUpgradeDialog({ open, openChange }: VipUpgradeDialogProps) {
  const { accountCount, memberCount } = useVipStore(
    (state) => ({
      accountCount: state.accountCount,
      memberCount: state.memberCount,
    }),
  )

  return (
    <>
      <Dialog open={open} onOpenChange={openChange}>
        <DialogContent className="w-[500px] bg-white p-0">
          <DialogHeader>
            <DialogTitle
              className={'h-[99px] w-[500px]'}
              style={{
                backgroundImage: `url(${vipbanner})`,
                backgroundSize: '100% 100%',
                marginLeft: '-1px',
              }}
            >
              <div className={'h-full w-full pl-[42px] pt-[36px]'}>
                <img src={vipbannerfont} alt="vipbannerfont" className={'h-[30px] w-[270px]'} />
              </div>
            </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className={'pl-[42px] pt-[16px] text-sm leading-[24px] text-[#222222]'}>
            试用期间账号数提升至
            <span className={'text-base font-semibold text-[#4F46E5]'}>{accountCount}个，</span>
            团队成员提升至
            <span className={'text-base font-semibold text-[#4F46E5]'}>{memberCount}人</span>。
            <br />
            试用结束后将冻结全部账号、团队成员，重新开通后可恢复。
          </div>

          <div className={'px-[44px] pb-[44px] pt-[34px] text-center'}>
            <Button
              type="button"
              variant="default"
              className={'w-full bg-[#222222] hover:bg-[#222222]'}
              onClick={() => openChange(false)}
            >
              我知道了
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
