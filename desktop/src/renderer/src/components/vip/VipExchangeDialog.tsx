import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useState } from 'react'
import { LoadingButton } from '../LoadingButton'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { toast } from 'sonner'
import { DropdownMenuItem } from '@/components/dropdown-menu'

export function VipExchangeDialog({
  onClose,
  children,
}: {
  children: (render: React.ReactNode) => React.ReactNode
  onClose?: () => void
}) {
  const [open, setOpen] = useState(false)
  const [value, setValue] = useState('')
  const { mutate, isPending } = useApiMutation<
    string,
    {
      channelCode: string
      giftDays: number
    }
  >(
    (code) => ({
      url: `/orders/channel/gift`,
      method: 'POST',
      data: {
        channelCode: code,
      },
    }),
    {
      onSuccess: (data) => {
        setOpen(false)
        onClose?.()
        if (data.giftDays) {
          toast.success(`恭喜成功兑换${data.giftDays}天VIP`)
        }
      },
    },
  )
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {children(
        <DialogTrigger asChild>
          <DropdownMenuItem>兑换VIP</DropdownMenuItem>
        </DialogTrigger>,
      )}
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>兑换VIP</DialogTitle>
          <DialogDescription>请输入渠道的VIP兑换码</DialogDescription>
        </DialogHeader>
        <div className="flex items-center">
          <Input value={value} onChange={(e) => setValue(e.target.value)} />
        </div>
        <DialogFooter className="justify-end">
          <LoadingButton
            disabled={value === '' || isPending}
            isPending={isPending}
            onClick={() => mutate(value)}
            type="button"
          >
            兑换
          </LoadingButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
