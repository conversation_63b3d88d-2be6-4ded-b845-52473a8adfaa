import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import vipWaring from '@renderer/assets/vip/vip-warning.png'
import { useVipDialog } from '@renderer/pages/Vip/vipDialogProvider'
import { features } from '@renderer/infrastructure/model/features/features'
import { useFeatureManager } from '@renderer/infrastructure/services'

interface VipUpgradeDialogProps {
  open: boolean
  openChange: (open: boolean) => void
}

export function VipExpiredDialog({ open, openChange }: VipUpgradeDialogProps) {
  const { openFeature } = useFeatureManager()
  const { show } = useVipDialog()

  return (
    <>
      <Dialog open={open} onOpenChange={openChange}>
        <DialogContent className="w-[480px] bg-white">
          <DialogHeader>
            <DialogTitle>
              <div className={'flex items-center'}>
                <img src={vipWaring} alt={'vipWaring'} width={26} />
                <span className={'ml-2'}>VIP已到期</span>
              </div>
            </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className={'mt-5'}>账号、成员全部冻结，你可以重新开通VIP恢复全部账号。</div>
          <DialogFooter className={'mt-5'}>
            <Button
              variant="outline"
              type="button"
              onClick={() => {
                // contactChange(false)
                show()
              }}
            >
              重新开通
            </Button>
            <Button
              className={'ml-6'}
              type="button"
              variant="outline"
              onClick={() => {
                openFeature(features.账号)
                openChange(false)
              }}
            >
              查看已冻结账号
            </Button>
            <Button type="button" onClick={() => openChange(false)}>
              我知道了
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
