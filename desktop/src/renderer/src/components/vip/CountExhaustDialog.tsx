import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { createRef, forwardRef, useImperativeHandle, useState } from 'react'
import { useVipDialog } from '@renderer/pages/Vip/vipDialogProvider'

interface DialogProps {
  open: boolean
  openChange: (open: boolean) => void
}
export function CountExhaustDialog({ open, openChange }: DialogProps) {
  const { show } = useVipDialog()
  return (
    <>
      <Dialog open={open} onOpenChange={openChange}>
        <DialogContent onOpenAutoFocus={(e) => e.preventDefault()} className={'w-[640px] bg-white'}>
          <DialogHeader>
            <DialogTitle>今日发布次数已用完，请明日再来。</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>

          <div className="flex items-center">
            <Button
              variant="link"
              className="px-1"
              onClick={() => {
                openChange(false)
                show()
              }}
            >
              开通VIP
            </Button>
            可不限制发布数量哦
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => openChange(false)}>
              我知道了
            </Button>
            <Button
              type="button"
              onClick={() => {
                openChange(false)
                show()
              }}
            >
              去开通
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

type CountExhaustDialogHandle = {
  open: () => void
}

export const CountExhaustDialogWrapper = forwardRef<CountExhaustDialogHandle, NonNullable<unknown>>(
  (_, ref) => {
    const [open, setOpen] = useState(false)
    useImperativeHandle(ref, () => ({
      open() {
        setOpen(true)
      },
    }))
    return <CountExhaustDialog open={open} openChange={setOpen} />
  },
)

CountExhaustDialogWrapper.displayName = 'CountExhaustDialogWrapper'

const countExhaustDialogRef = createRef<CountExhaustDialogHandle>()

export const countExhaustDialogService = {
  open() {
    countExhaustDialogRef.current?.open()
  },
}

export const CountExhaustDialogWrapperRef = () => {
  return <CountExhaustDialogWrapper ref={countExhaustDialogRef} />
}
