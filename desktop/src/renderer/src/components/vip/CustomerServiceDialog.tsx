import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import qrCodeImage from '@renderer/assets/wait-list/qr-code.png'

interface DialogProps {
  open: boolean
  openChange: (open: boolean) => void
}

export function CustomerServiceDialog({ open, openChange }: DialogProps) {
  return (
    <>
      <Dialog open={open} onOpenChange={openChange}>
        <DialogContent className="w-[280px] bg-white">
          <DialogHeader>
            <DialogTitle></DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div
            className="mt-2 h-[228px] w-[228px]"
            style={{ backgroundImage: `url(${qrCodeImage})`, backgroundSize: 'contain' }}
          ></div>
          <DialogFooter></DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
