import type { ReactNode } from 'react'

export function FormItem({
  prepend,
  children,
  append,
  className,
}: {
  prepend?: ReactNode
  children: ReactNode
  append?: ReactNode
  className?: string
}) {
  return (
    <div className={`relative flex items-center ${className || ''}`}>
      {prepend && <div className="absolute left-2">{prepend}</div>}
      {children}
      {append && <div className="absolute right-2">{append}</div>}
    </div>
  )
}
