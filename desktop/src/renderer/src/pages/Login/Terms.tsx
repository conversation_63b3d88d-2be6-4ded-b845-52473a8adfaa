import type { ReactNode } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { Button } from '@/components/ui/button'

export const Head1 = ({ children }: { children: ReactNode }) => (
  <h1 className="flex w-full justify-center text-2xl">{children}</h1>
)

export const Head2 = ({ children }: { children: ReactNode }) => (
  <h2 className="mt-5 text-xl">{children}</h2>
)

export const Paragraph = ({ children }: { children: ReactNode }) => (
  <p className="indent-8 text-sm text-gray-500">{children}</p>
)

export function Terms({
  trigger,
  title,
  content,
  onAccept,
}: {
  trigger: ReactNode
  title: ReactNode
  content: ReactNode
  onAccept: () => void
}) {
  function accept() {
    onAccept()
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger>
        <span className="text-primary">{trigger}</span>
      </AlertDialogTrigger>
      <VisuallyHidden>
        <AlertDialogTitle>用户及隐私协议</AlertDialogTitle>
      </VisuallyHidden>
      <AlertDialogContent className="flex h-5/6 w-5/6 flex-col overflow-y-auto rounded-md px-1">
        <AlertDialogHeader>
          <Head1>{title}</Head1>
        </AlertDialogHeader>
        <div className="grow px-4">{content}</div>
        <AlertDialogAction asChild>
          <Button className="mx-10" onClick={accept}>
            我已阅读并接受以上协议
          </Button>
        </AlertDialogAction>
      </AlertDialogContent>
    </AlertDialog>
  )
}
