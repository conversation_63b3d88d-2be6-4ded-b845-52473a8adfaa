import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import PhoneIcon from '@renderer/assets/passport/phone.svg?react'
import VerifyIcon from '@renderer/assets/passport/verify.svg?react'
import PasswordIcon from '@renderer/assets/passport/password.svg?react'
import { Checkbox } from '@/components/ui/checkbox'
import { FormItem as LoginFormItem } from '@renderer/pages/Login/FormItem'
import { useNavigate } from 'react-router-dom'
import { animated, useSpring } from '@react-spring/web'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PasswordInput } from '@/components/PasswordInput'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'
import { VerificationCodeButton } from '@/components/VerificationCodeButton'
import { useLogin } from '@/hooks/login'
import { Form, FormControl, FormField, FormItem } from '@/components/ui'
import { LoadingButton } from '@/components/loadingButton'

export function RenderLoginForm() {
  const springs = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: { duration: 200 },
    delay: 300,
  })
  const navigate = useNavigate()

  const {
    form,
    isLoginButtonDisabled,
    handleSubmit,
    isAgree,
    setIsAgree,
    phone,
    loginType,
    isPending,
  } = useLogin(() => {
    navigate('/')
  })
  return (
    <animated.div style={springs}>
      <Form {...form}>
        <form onSubmit={handleSubmit}>
          <div className="mt-8 flex w-full flex-col items-center gap-4">
            <Tabs value={loginType.value} onValueChange={(value) => loginType.onChange(value)}>
              <TabsList>
                <TabsTrigger value="code">验证码登录</TabsTrigger>
                <TabsTrigger value="password">密码登录</TabsTrigger>
              </TabsList>
            </Tabs>
            <div className="grid w-[358px] grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <LoginFormItem prepend={<PhoneIcon />}>
                        <Input
                          type="text"
                          placeholder="手机号码"
                          className="h-[54px] bg-background/50 pl-10"
                          {...field}
                        />
                      </LoginFormItem>
                    </FormControl>
                  </FormItem>
                )}
              />

              {loginType.value === 'code' ? (
                <FormField
                  name="code"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <LoginFormItem
                          prepend={<VerifyIcon />}
                          append={
                            <VerificationCodeButton
                              sence="auth"
                              onSendSuccess={(code) => {
                                field.onChange(code)
                              }}
                              phone={phone}
                            />
                          }
                        >
                          <Input
                            type="text"
                            placeholder="验证码"
                            className="h-[54px] bg-background/50 pl-10 pr-28"
                            {...field}
                          />
                        </LoginFormItem>
                      </FormControl>
                    </FormItem>
                  )}
                />
              ) : (
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormControl>
                      <LoginFormItem prepend={<PasswordIcon />}>
                        <PasswordInput
                          asChild
                          placeholder="密码"
                          className="h-[54px] bg-background/50 pl-10"
                          {...field}
                        />
                      </LoginFormItem>
                    </FormControl>
                  )}
                />
              )}
              <div className="mb-[54px] flex h-5 items-center justify-between">
                <LoginFormItem className="gap-1">
                  <Checkbox
                    id="terms"
                    checked={isAgree}
                    onCheckedChange={(value) => setIsAgree(!!value)}
                  />
                  <label
                    htmlFor="terms"
                    className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    我已阅读并接受
                    <span className="text-primary">
                      《
                      <Button
                        variant="link"
                        onClick={() => {
                          window.open(
                            'https://lite-download.yixiaoer.cn/privacy/yixiaoeruseragreement.pdf',
                          )
                        }}
                        className="p-0"
                      >
                        用户服务协议
                      </Button>
                      》
                    </span>
                    和
                    <span className="text-primary">
                      《
                      <Button
                        variant="link"
                        onClick={() => {
                          window.open(
                            'https://lite-download.yixiaoer.cn/privacy/yixiaoerprivacypolicy.pdf',
                          )
                        }}
                        className="p-0"
                      >
                        隐私政策
                      </Button>
                      》
                    </span>
                    {/* <LoginTerms onAccept={() => setAccepted(true)} /> */}
                  </label>
                </LoginFormItem>
              </div>
            </div>

            <div className="flex-0 h-max w-full">
              <LoadingButton
                type="submit"
                className="h-12 w-full"
                disabled={isLoginButtonDisabled}
                isPending={isPending}
              >
                登录
              </LoadingButton>
            </div>

            {loginType.value === 'password' && (
              <Button
                variant="link"
                className="h-5 p-0 hover:no-underline"
                onClick={() => {
                  alertBaseManager.open({
                    title: '忘记密码',
                    description: '请先用验证码登录后，在个人设置中重置密码。',
                    buttons: [],
                    okText: '我知道了',
                  })
                }}
              >
                忘记密码
              </Button>
            )}
          </div>
        </form>
      </Form>
    </animated.div>
  )
}
