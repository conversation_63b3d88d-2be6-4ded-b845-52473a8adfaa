import { LoadingContainer } from '@/components/loading'
import { Button } from '@/components/ui'
import { useInitData } from '@/hooks/common/useInitData'
import type { ReactElement } from 'react'
import { Layout } from './Layout'

export function MainProvider(): ReactElement {
  const { isLoading, error, data, refetch } = useInitData()
  return (
    <>
      <div className="flex h-full items-center justify-center">
        {error ? (
          <div className="text-center">
            <p className="mb-4 text-destructive">初始化失败，请重试</p>
            <Button onClick={() => refetch()}>重试</Button>
          </div>
        ) : (
          <LoadingContainer />
        )}
      </div>
      {!isLoading && data && <Layout />}
    </>
  )
}
