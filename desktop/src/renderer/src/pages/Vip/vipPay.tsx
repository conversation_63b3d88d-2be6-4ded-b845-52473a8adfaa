import { Button } from '@/components/ui/button'
import type { ReactNode } from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import aliPay from '@renderer/assets/vip/aliPay.png'
import wechatPay from '@renderer/assets/vip/wechatPay.png'
import { Separator } from '@/components/ui/separator'
import { useQueryClient } from '@tanstack/react-query'
import { QRCodeCanvas } from 'qrcode.react'
import { LoadingContainer } from '@/components/loading'
import aliPayLogo from '@renderer/assets/vip/alipay-logo.png'
import wechatPayLogo from '@renderer/assets/vip/wechat-pay-logo.png'
import { CountDown } from './components/countDown'
import { useAppStore } from '@/stores'
import { DateUtils, formatPrice } from '@/utils'
import { cn } from '@/lib/utils'
import { useOrderPayInfoQuery, useOrderStatusQuery } from '@/hooks/order'

export function Row({ label, value }: { label: string; value: ReactNode }) {
  return (
    <li className="flex items-center gap-6 text-base">
      <div className="w-20 text-muted-foreground">{label}:</div>
      <span>{value}</span>
    </li>
  )
}

export const VipPay = ({ orderNO, onSuccess }: { orderNO: string; onSuccess: () => void }) => {
  const [activePay, setActivePay] = useState('alipay')
  const team = useAppStore((state) => state.team)
  const queryClient = useQueryClient()

  const payQuery = useOrderPayInfoQuery(orderNO, {
    enabled: !!orderNO,
  })

   const statusQuery = useOrderStatusQuery(orderNO, {
    enabled: !!orderNO && !!payQuery.data,
    refetchInterval: (query) => (query.state.data?.orderStatus === 'pending' ? 2000 : false),
  })

  useEffect(() => {
    if (statusQuery.data?.orderStatus === 'paid') {
      void queryClient.invalidateQueries({ queryKey: ['teamOurTeam'] })
      onSuccess()
    }
  }, [statusQuery.data, team, queryClient, onSuccess])

  const payList = useMemo(() => {
    if (!payQuery.data) return []
    const { alipayUrl, weixinUrl } = payQuery.data
    return [
      {
        type: 'alipay',
        name: '支付宝',
        payUrl: alipayUrl || '',
        icon: aliPay,
      },
      {
        type: 'wechat',
        name: '微信',
        payUrl: weixinUrl || '',
        icon: wechatPay,
      },
    ]
  }, [payQuery.data])
  const activePayInfo = useMemo(() => {
    if (!payQuery.data) return null
    return payList.find((item) => item.type === activePay)
  }, [payQuery.data, payList, activePay])
  const formatTime = useCallback(
    (time: number) => DateUtils.duration.formatSeconds(time, 'minimal'),
    [],
  )
  return (
    <div className="flex w-full flex-1 flex-col gap-6 p-6">
      {payQuery.isLoading || !payQuery.data ? (
        <LoadingContainer />
      ) : (
        <>
          <ul className="flex flex-col gap-4">
            <Row label="当前团队" value={team?.name} />
            <Row label="订单号" value={orderNO} />
            <Row
              label="应付金额"
              value={
                <span className="text-sm text-destructive">
                  ¥
                  <span className="text-2xl font-bold">
                    {formatPrice(payQuery.data?.dueAmount || 0)}
                  </span>
                </span>
              }
            />
            <Row
              label="支付方式"
              value={
                <ul className="flex flex-wrap gap-3">
                  {payList.map((item) => (
                    <li key={item.type}>
                      <Button
                        onClick={() => activePay !== item.type && setActivePay(item.type)}
                        className={cn('h-11 w-[137px] font-medium', {
                          'border-primary bg-accent text-background': item.type === activePay,
                        })}
                        variant="outline"
                      >
                        {item.icon ? (
                          <img className="h-[22px]" src={item.icon} alt={item.name} />
                        ) : (
                          item.name
                        )}
                      </Button>
                    </li>
                  ))}
                </ul>
              }
            />
          </ul>
          <Separator />
          <div className="mx-auto flex h-[230px] w-[200px] flex-col items-center justify-between rounded-md bg-[#F9F9FA] p-2.5">
            {activePay === 'corporateTransfer' ? (
              <img src={activePayInfo?.payUrl} className="w-full" />
            ) : (
              <div className="bg-background p-1.5">
                <QRCodeCanvas
                  level="Q"
                  size={168}
                  minVersion={2}
                  imageSettings={{
                    excavate: true,
                    width: 48,
                    height: 48,
                    src: activePay === 'wechat' ? wechatPayLogo : aliPayLogo,
                  }}
                  value={activePayInfo!.payUrl}
                />
              </div>
            )}
            <div className="text-xs">
              {activePay === 'corporateTransfer' ? (
                '微信扫一扫联系专员'
              ) : (
                <span>
                  剩余支付时间：
                  <span className="text-destructive">
                    <CountDown
                      seconds={payQuery.data.remainingTimeInSeconds / 1000}
                      render={formatTime}
                    />
                  </span>
                </span>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
