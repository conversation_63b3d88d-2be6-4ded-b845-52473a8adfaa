import { createContext, useContext } from 'react'

interface VipDialogContextType {
  show: () => void
  pay: (orderId: string, isUpgrade?: boolean) => void
  upgrade: () => void
}

export const VipDialogContext = createContext<VipDialogContextType | null>(null)

// 创建一个外部访问对象
export const dialogUtils: VipDialogContextType = {
  show: () => console.error('VipDialogProvider not mounted'),
  pay: () => console.error('VipDialogProvider not mounted'),
  upgrade: () => console.error('VipDialogProvider not mounted'),
}
// 组件内使用的 hook
export const useVipDialog = () => {
  const context = useContext(VipDialogContext)
  if (!context) {
    throw new Error('useVipDialog must be used within VipDialogProvider')
  }
  return context
}

// 导出外部调用方法
export const vipDialogManager = Object.freeze({
  show: () => dialogUtils.show(),
  pay: (orderId: string, isUpgrade?: boolean) => dialogUtils.pay(orderId, isUpgrade),
  upgrade: () => dialogUtils.upgrade(),
})
