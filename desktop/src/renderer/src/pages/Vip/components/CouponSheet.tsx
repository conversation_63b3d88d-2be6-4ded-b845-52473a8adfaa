import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet'
import { useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import type { Coupon } from '../types/coupon'
import { ChevronRight, CircleAlert } from 'lucide-react'
import { formatPrice } from '@renderer/lib/utils'
import { Button } from '@/components/ui/button'
import { CouponItem } from './CouponItem'
import { EmptyState } from './EmptyState'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import type { WarpPage } from '@renderer/hooks/useApiQuery'
import { useApiQuery } from '@renderer/hooks/useApiQuery'

interface VipSheetProps {
  onSelectCoupon: (coupon: Coupon | null) => void
  selectedCoupon: Coupon | null
  // 订单金额
  orderAmount?: number
}

export function CouponSheet({ onSelectCoupon, selectedCoupon, orderAmount }: VipSheetProps) {
  const [open, setOpen] = useState(false)

  const { data: coupons, isLoading } = useApiQuery<WarpPage<Coupon>>(
    {
      url: '/coupons',
      method: 'GET',
      params: { page: 1, size: 100, status: 0 },
    },
    ['coupons'],
  )

  const handleSelectCoupon = (coupon: Coupon | null) => {
    if (coupon?.id === selectedCoupon?.id) {
      onSelectCoupon(null)
    } else {
      onSelectCoupon(coupon)
    }
    setOpen(false)
  }

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <div className="flex h-5 cursor-pointer items-center pr-0">
          {selectedCoupon ? (
            <span className="text-sm text-destructive">
              -¥{formatPrice(selectedCoupon.discountAmount)}
            </span>
          ) : (
            '选择优惠券'
          )}
          <ChevronRight strokeWidth={2} className="h-3.5 w-3.5" />
        </div>
      </SheetTrigger>
      <SheetContent className="flex w-[450px] !max-w-none flex-col">
        <SheetHeader>
          <SheetTitle>请选择优惠券</SheetTitle>
        </SheetHeader>
        {isLoading ? (
          <LoadingContainer className="h-5 w-5" />
        ) : coupons?.data?.length ? (
          <ScrollArea className="mt-4 h-full">
            <div className="space-y-4">
              {coupons.data.map((coupon) => {
                const isDisabled = !!orderAmount && coupon.minimumSpendingAmount > orderAmount
                return (
                  <div key={coupon.id}>
                    <Button
                      disabled={isDisabled}
                      variant="ghost"
                      key={coupon.id}
                      className={'h-[90px] w-[400px] bg-transparent p-0 disabled:opacity-100'}
                      onClick={() => handleSelectCoupon(coupon)}
                    >
                      <CouponItem
                        coupon={coupon}
                        hasClick={true}
                        isSelected={coupon.id === selectedCoupon?.id}
                        isDisabled={isDisabled}
                      />
                    </Button>
                    {isDisabled && (
                      <div className="flex h-7 items-center px-2 text-xs text-[#F48B23]">
                        <CircleAlert className="mr-1 h-3 w-3" />
                        不满足门槛金额
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        ) : (
          <EmptyState message="暂无优惠券可用" />
        )}
      </SheetContent>
    </Sheet>
  )
}
