import { Button } from '@/components/ui/button'
import { cn, formatMount } from '@renderer/lib/utils'

interface VipMonth {
  mount: number
  present?: number
}

interface DurationSelectionProps {
  vipOften: VipMonth[]
  activeMonth: number
  setActiveMonth: (index: number) => void
  hasUpgrade?: boolean
  label?: string
  salesType?: number
}

export function DurationSelection({
  vipOften,
  activeMonth,
  setActiveMonth,
  label = '开通时长',
  salesType,
}: DurationSelectionProps) {
  return (
    <div className="mt-3 flex flex-col gap-3">
      <div className="font-medium">{label}</div>
      <ul className="flex flex-wrap gap-3">
        {vipOften.map((month, index) => (
          <li key={month.mount} className="relative">
            <Button
              onClick={() => activeMonth !== index && setActiveMonth(index)}
              className={cn('z-0 h-[84px] w-[203px] bg-inherit font-medium', {
                'text-background, hover:bg-blueMuted border-2 border-primary bg-accent':
                  index === activeMonth,
              })}
              variant="outline"
            >
              {month.mount
                ? month.present
                  ? `${month.mount}+${formatMount(month.present)}`
                  : formatMount(month.mount)
                : '不延长'}
            </Button>
            {!!month.present && (
              <span className="absolute right-0 top-0 flex h-[22px] items-center justify-center rounded-bl-md rounded-tr-md bg-gradient-to-tl from-[#FF3E3D] to-[#FF7057] px-1 text-xs font-medium text-background">
                送{month.present}个月
              </span>
            )}

            {(salesType === 0 || month.mount === 12) && (
              <span
                className={cn(
                  'absolute -top-2 right-0 z-10 h-[22px] rounded-md px-2 py-0 text-xs leading-[22px]',
                  index === activeMonth ? 'text-[#FFFFFFFF]' : 'text-[#FF6457FF]',
                )}
                style={{
                  background:
                    index === activeMonth
                      ? 'linear-gradient(90deg, #FF6457 0%, #FF814C 100%)'
                      : 'linear-gradient(90deg, #FFDBD8 100%, #FFEBE3 100%)',
                }}
              >
                {month.mount === 12 ? '年付优惠' : '首购优惠'}
              </span>
            )}
          </li>
        ))}
      </ul>
    </div>
  )
}
