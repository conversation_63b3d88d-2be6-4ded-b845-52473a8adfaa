import { InterestPackage } from './InterestPackage'
import { DurationSelection } from './DurationSelection'
import type { VipData } from '../types/vip'

interface BaseOrderFormProps {
  activeVip: number
  setActiveVip: (value: number) => void
  activeMonth: number
  setActiveMonth: (value: number) => void
  num: number
  setNum: (value: number) => void
  minInterestCount: number
  VipData: VipData
  salesType?: number
}

export function BaseOrderForm({
  activeVip,
  setActiveVip,
  activeMonth,
  setActiveMonth,
  num,
  setNum,
  minInterestCount,
  VipData,
  salesType,
}: BaseOrderFormProps) {
  return (
    <div className="flex w-[500px] flex-shrink-0 flex-col gap-3 p-6">
      <InterestPackage
        interests={VipData}
        activeVip={activeVip}
        setActiveVip={setActiveVip}
        num={num}
        setNum={setNum}
        minInterestCount={minInterestCount}
      />
      <DurationSelection
        vipOften={VipData.vipOften}
        activeMonth={activeMonth}
        setActiveMonth={setActiveMonth}
        salesType={salesType}
      />
    </div>
  )
}
