import { cn } from '@renderer/lib/utils'
import { CheckIcon } from 'lucide-react'
import type { Coupon } from '../types/coupon'
import { DateUtils } from '@renderer/utils/date-utils'
import { Badge } from '@/components/ui/badge'

interface CouponItemProps {
  coupon: Coupon
  // 是否可选
  hasClick?: boolean
  isSelected: boolean
  isDisabled: boolean
}

export function CouponItem({ coupon, isSelected, hasClick, isDisabled }: CouponItemProps) {
  const hasDisable = isDisabled || coupon.status !== 0
  return (
    <div
      className={cn(
        "flex h-[90px] w-[400px] items-center bg-[url('@/assets/vip/coupon-bg.png')] bg-cover bg-center bg-no-repeat",
        {
          'bg-[url("@/assets/vip/coupon-bg-disabled.png")]': hasDisable,
        },
      )}
    >
      <div className="flex w-[90px] flex-col items-center justify-between">
        <span
          className={cn(
            'text-2xl font-bold text-destructive',
            hasDisable ? 'text-muted-foreground' : '',
          )}
        >
          ¥{coupon.discountAmount}
        </span>
        <span className="text-xs text-muted-foreground">满¥{coupon.minimumSpendingAmount}可用</span>
      </div>
      <div className="flex flex-1 items-center justify-between px-5">
        <div className="flex flex-col items-start gap-2">
          <div
            className={cn('font-medium', {
              'text-muted-foreground': hasDisable,
            })}
          >
            {coupon.name}
          </div>
          <div className="text-xs text-muted-foreground">
            有效期至:
            <span>{DateUtils.formatDate(coupon.expireTime, 'yyyy-MM-dd')}</span>
          </div>
        </div>
        {hasClick ? (
          <div>
            {isSelected ? (
              <div className="flex h-4 w-4 items-center justify-center rounded-full bg-destructive">
                <CheckIcon className="h-3 w-3 text-white" />
              </div>
            ) : (
              <div className="h-4 w-4 rounded-full border" />
            )}
          </div>
        ) : (
          <div>
            {coupon.status === 1 && <Badge variant="outline">已使用</Badge>}
            {coupon.status === 2 && <Badge variant="outline">已过期</Badge>}
          </div>
        )}
      </div>
    </div>
  )
}
