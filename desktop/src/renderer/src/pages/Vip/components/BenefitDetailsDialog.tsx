import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
  DialogHeader,
} from '@/components/ui/dialog'
import { ChevronRight, CircleHelp, X } from 'lucide-react'
import { useState } from 'react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
export const BenefitDetailsDialog = () => {
  const [open, setOpen] = useState(false)
  const desc = [
    '账号数&网站数',
    '账号数&网站数',
    '发布次数',
    '素材库',
    '云端发布',
    '数据统计',
    '手机APP发布',
    '视频号不掉线',
  ]
  const free = ['1', '5', '每日3次', '500MB', 'x', '需每日手动刷新', 'x', 'x']
  const vip = [
    '2人/权益包',
    '20个/权益包',
    '不限制',
    '2GB/权益包',
    '30GB/月/权益包',
    '自动刷新',
    '支持,手机移动端发布',
    '支持,授权稳定在线类型视频号',
  ]
  return (
    <>
      <div
        className={'flex cursor-pointer items-center text-sm text-gray-500'}
        onClick={() => setOpen(true)}
      >
        权益详情
        <ChevronRight size={14} />
      </div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger hidden></DialogTrigger>
        <DialogContent
          className={'w-fit max-w-none'}
          onOpenAutoFocus={(e) => e.preventDefault()}
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>权益详情</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className={'rounded-lg border border-gray-200'}>
            <div className={'flex'}>
              <ul className={'w-[140px] bg-[#FFFFFF]'}>
                <li className={'bg-[#f3f4fa] py-[14px] text-center text-sm font-medium'}>权益</li>
                {desc.map((item, index) => {
                  return (
                    <li className={'py-[14px] text-center text-sm'} key={index}>
                      {item === '账号点数' ? (
                        <>
                          <span className={'mx-auto flex justify-center gap-1'}>
                            账号点数
                            <TooltipProvider delayDuration={0} disableHoverableContent>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <CircleHelp className="mx-1 h-4 w-4 cursor-pointer text-[#BDBDBD]" />
                                </TooltipTrigger>
                                <TooltipContent side="right" className="bg-[#000000b8]">
                                  <p>1个矩阵账号算1个点数</p>
                                  <p>1个 “不掉线视频号” 算2个点数</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </span>
                        </>
                      ) : (
                        item
                      )}
                    </li>
                  )
                })}
              </ul>

              <ul className={'w-[202px] bg-[#FCFCFE]'}>
                <li
                  className={'bg-[#FBFBFD] py-[14px] text-center text-sm font-semibold text-black'}
                >
                  免费版
                </li>
                {free.map((item, index) => {
                  return (
                    <li className={'py-[14px] text-center text-sm text-[#666666]'} key={index}>
                      {item === 'x' ? <X className={'mx-auto h-5'} color={'#666666'} /> : item}
                    </li>
                  )
                })}
              </ul>

              <ul className={'w-[202px] bg-[#FFFBF2]'}>
                <li
                  className={
                    'bg-[#FFF4D7] py-[14px] text-center text-sm font-semibold text-[#CD782C]'
                  }
                >
                  会员版
                </li>
                {vip.map((item, index) => {
                  return (
                    <li key={index} className={'py-[14px] text-center text-sm text-[#CD782C]'}>
                      {item}
                    </li>
                  )
                })}
              </ul>
            </div>
            <ul className={'flex'}>
              <li className={'min-w-[140px] py-[14px] text-center text-sm'}>通用权益</li>
              <li className={'px-6 py-[14px] text-sm'}>
                支持多视频多账号、单视频多账号、图文、文章、微信公众号发布支持账号管理、网站账户管理
              </li>
            </ul>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
