import { cn } from '@renderer/lib/utils'
import { StepInput } from './stepInput'
import { Check, X } from 'lucide-react'
import type { VipData } from '../types/vip'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { useVipDetail } from '@renderer/hooks/preload/use-vip'

import VIP from '@renderer/assets/vip/vipiicon.png'
type interest = Omit<VipData, 'vipOften'>
import { BenefitDetailsDialog } from '@renderer/pages/Vip/components/BenefitDetailsDialog'
interface InterestPackageProps {
  interests: interest
  activeVip: number
  setActiveVip: (index: number) => void
  num: number
  setNum: (value: number) => void
  minInterestCount?: number
  // 是否展示StepInput
  showStepInput?: boolean
  title?: string
  // 列表扩展
  extra?: React.ReactNode
}

export function InterestPackage({
  interests,
  activeVip,
  setActiveVip,
  num,
  setNum,
  minInterestCount = 1,
  showStepInput = true,
  title = '权益包',
  extra,
}: InterestPackageProps) {
  const { isVip } = useVipDetail()

  return (
    <div className="flex flex-col gap-3">
      <div className="align-center flex justify-between">
        <div className={'text-[16px] font-medium text-black'}>{title}</div>
        {!isVip && <BenefitDetailsDialog />}
      </div>
      {!isVip && (
        <div className={'flex flex-row bg-[#F8F8FA]'}>
          <div className={'min-w-[140px] overflow-hidden rounded-bl-lg rounded-tl-lg'}>
            <div
              className={'border-b py-4 text-center text-sm font-extrabold'}
              style={{ background: 'linear-gradient( 180deg, #F1F1F1 0%, #F8F8FA 100%)' }}
            >
              权益
            </div>
            <div className={'flex flex-col gap-3 bg-[#F8F8FA] p-4'}>
              <ul className="flex flex-col gap-4 text-sm text-muted-foreground">
                <li className="flex items-center justify-between">
                  <span className={'flex items-center gap-1'}>
                    账号数&网站数
                    {/*<TooltipProvider delayDuration={0} disableHoverableContent>*/}
                    {/*  <Tooltip>*/}
                    {/*    <TooltipTrigger asChild>*/}
                    {/*      <CircleHelp className="mx-1 h-4 w-4 cursor-pointer text-[#BDBDBD]" />*/}
                    {/*    </TooltipTrigger>*/}
                    {/*    <TooltipContent side="right" className="bg-[#000000b8]">*/}
                    {/*      <p>1个矩阵账号算1个点数</p>*/}
                    {/*      <p>1个 “不掉线视频号” 算2个点数</p>*/}
                    {/*    </TooltipContent>*/}
                    {/*  </Tooltip>*/}
                    {/*</TooltipProvider>*/}
                  </span>
                </li>
                <li className="flex items-center justify-between">
                  <span>成员数</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>素材库</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>云发布流量</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>手机APP发布</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>视频号不掉线</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>数据统计</span>
                </li>
              </ul>
            </div>
          </div>

          <div className={'relative border-l border-r border-[#695FEE]'}>
            <span
              className={
                'absolute left-[-1px] top-[-3%] z-[-1] h-[106%] w-[101%] rounded-lg border border-[#695FEE]'
              }
              style={{ background: 'linear-gradient( 180deg, #E9E7FF 0%, #F3F2FD 100%)' }}
            ></span>
            <div
              style={{ background: 'linear-gradient( 180deg, #E9E7FF 0%, #F3F2FD 100%)' }}
              className={'h-[53px] border-b py-4 text-center text-[#663309]'}
            >
              <img src={VIP} alt="vip" className="mx-auto h-[15px]" />
            </div>
            <div
              onClick={() => setActiveVip(0)}
              className={cn('flex flex-col gap-3 rounded-lg bg-[#f3f2fd] p-4')}
            >
              <ul className="flex flex-col gap-4 text-sm text-muted-foreground">
                <li className="flex items-center justify-center">
                  {/*<span>账号数&网站数</span>*/}
                  <span className="text-sm font-bold text-[#695FEE]">
                    {interests.platformAccountCount * num}
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  {/*<span>成员数</span>*/}
                  <span className="text-sm font-bold text-[#695FEE]">
                    {interests.memberCount * num}
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  {/*<span>素材库</span>*/}
                  <span className="text-sm font-bold text-[#695FEE]">
                    {ByteSize.fromB(interests.capacityLimit * num).toString()}
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  {/*<span>云发布流量</span>*/}
                  <span className="text-sm font-bold text-[#695FEE]">
                    {ByteSize.fromB(interests.networkTrafficLimit * num).toString()}/月
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  {/*<span>手机APP发布</span>*/}
                  {interests.appPublish && <Check className="h-5 text-[#695FEE] text-foreground" />}
                </li>
                <li className="flex items-center justify-center">
                  {/*<span>视频号不掉线</span>*/}
                  <Check className="h-5 text-[#695FEE] text-foreground" />
                </li>
                <li className="flex items-center justify-center">
                  <span className="text-sm font-bold text-[#695FEE]">自动刷新</span>
                </li>
                {extra}
              </ul>
              {showStepInput && (
                <StepInput num={num} setNum={setNum} min={minInterestCount} max={9999} />
              )}
            </div>
          </div>

          <div className={'min-w-[146px] overflow-hidden rounded-br-lg rounded-tr-lg'}>
            <div
              className={'border-b py-4 text-center text-sm font-extrabold'}
              style={{ background: 'linear-gradient( 180deg, #F1F1F1 0%, #F8F8FA 100%)' }}
            >
              免费版
            </div>

            <div className={'flex flex-col gap-3 rounded-lg bg-[#F8F8FA] p-4'}>
              <ul className="flex flex-col gap-4 text-sm text-muted-foreground">
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold text-black'}>5</span>
                </li>
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold text-black'}>1</span>
                </li>
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold text-black'}>500MB</span>
                </li>
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold'}>
                    <X className={'h-5'} />
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold'}>
                    <X className={'h-5'} />
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold'}>
                    <X className={'h-5'} />
                  </span>
                </li>
                <li className="flex items-center justify-center">
                  <span className={'text-sm font-bold text-black'}>手动刷新</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {isVip && (
        <div className={'flex w-[240px] flex-row rounded-lg bg-[#F8F8FA] p-4'}>
          <ul className="flex w-full flex-col gap-2 text-sm text-muted-foreground">
            <li className="flex items-center justify-between">
              <span className={'flex items-center gap-1'}>
                账号数&网站数
                {/*<TooltipProvider delayDuration={0} disableHoverableContent>*/}
                {/*  <Tooltip>*/}
                {/*    <TooltipTrigger asChild>*/}
                {/*      <CircleHelp className="mx-1 h-4 w-4 cursor-pointer text-[#BDBDBD]" />*/}
                {/*    </TooltipTrigger>*/}
                {/*    <TooltipContent side="right" className="bg-[#000000b8]">*/}
                {/*      <p>1个矩阵账号算1个点数</p>*/}
                {/*      <p>1个 “不掉线视频号” 算2个点数</p>*/}
                {/*    </TooltipContent>*/}
                {/*  </Tooltip>*/}
                {/*</TooltipProvider>*/}
              </span>
              <span className="text-sm font-bold text-[#222]">
                {interests.platformAccountCount * num}
              </span>
            </li>
            <li className="flex items-center justify-between">
              <span>成员数</span>
              <span className="text-sm font-bold text-[#222]">{interests.memberCount * num}</span>
            </li>
            <li className="flex items-center justify-between">
              <span>素材库</span>
              <span className="text-sm font-bold text-[#222]">
                {ByteSize.fromB(interests.capacityLimit * num).toString()}
              </span>
            </li>
            <li className="flex items-center justify-between">
              <span>云发布流量</span>
              <span className="text-sm font-bold text-[#222]">
                {ByteSize.fromB(interests.networkTrafficLimit * num).toString()}/月
              </span>
            </li>
            <li className="flex items-center justify-between">
              <span>手机APP发布</span>
              {interests.appPublish && <Check className="h-5 text-[#222] text-foreground" />}
            </li>
            <li className="justify-betweencenter flex justify-between">
              <span>视频号不掉线</span>
              <Check className="h-5 text-[#222] text-foreground" />
            </li>
            {extra}
          </ul>
          {showStepInput && (
            <StepInput num={num} setNum={setNum} min={minInterestCount} max={9999} />
          )}
        </div>
      )}
    </div>
  )
}
