import type { ReactNode } from 'react'
import { cn } from '@renderer/lib/utils' // 假设您使用了 cn 函数来合并类名

interface RowProps {
  label: string
  value: ReactNode
  labelClassName?: string
  className?: string
}

export function Row({ label, value, labelClassName, className }: RowProps) {
  return (
    <li className={cn('flex items-start gap-3 text-sm', className)}>
      <div className={cn('min-w-20 text-muted-foreground', labelClassName)}>{label}:</div>
      <span className={'line-clamp-1 flex-1'}>{value}</span>
    </li>
  )
}

export function RowBetween({ label, value }: { label: string; value: ReactNode }) {
  return (
    <li className="flex items-center justify-between">
      <span>{label}:</span>
      <span>{value}</span>
    </li>
  )
}
