import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Minus, Plus } from 'lucide-react'

export function StepInput(props: {
  min: number
  num: number
  setNum: (num: number) => void
  max: number
}) {
  return (
    <div className="flex items-center gap-1">
      <Button
        size="icon"
        className="h-8 flex-shrink-0"
        variant="outline"
        disabled={props.num <= props.min}
        onClick={() => props.setNum(props.num - 1)}
      >
        <Minus className="h-4 w-4" />
      </Button>
      <Input
        min={1}
        step={1}
        max={9999}
        className="h-8 flex-1 bg-background text-center"
        type="text"
        value={props.num}
        onChange={(e) => {
          let inputValue = e.target.value
          // 去掉小数点
          inputValue = inputValue.replace('.', '')
          if (/^[1-9]\d*$/.test(inputValue)) {
            const numericValue = Number(inputValue)
            if (numericValue < props.min) {
              props.setNum(props.min)
            } else if (numericValue > props.max) {
              props.setNum(props.max)
            } else {
              props.setNum(numericValue)
            }
          } else {
            props.setNum(props.min) // 如果输入不合法，则默认为1
          }
        }}
      />
      <Button
        disabled={props.num >= props.max}
        onClick={() => props.setNum(props.num + 1)}
        size="icon"
        className="h-8 flex-shrink-0"
        variant="outline"
      >
        <Plus className="h-4 w-4" />
      </Button>
    </div>
  )
}
