/**
 * OrderInterestData
 */
export interface VipData {
  /**
   * 是否app发布
   */
  appPublish: boolean
  /**
   * 素材库容量大小
   */
  capacityLimit: number
  /**
   * 云发布流量大小
   */
  networkTrafficLimit: number
  /**
   * 权益包ID
   */
  id: string
  memberCount: number
  platformAccountCount: number
  price: number
  vipOften: OrderInterestVipOftenDTO[]
}

/**
 * OrderInterestVipOftenDTO
 */
export interface OrderInterestVipOftenDTO {
  mount: number
  present: number
}

export interface Order {
  dueAmount: number
  totalAmount: number
  expireTime: number
  createTime: number
  creatorName: string
  id: number
  orderNo: string
  isUpgrade: boolean
  orderStatus: OrderStatus
  payAmount: number
  payTime: number
  diffPrice: number
  payType: 'wechatPay' | 'alipay' | 'corporateTransfer' | 'other'
  price: number
  remainingTimeInSeconds: number
  teamName: string
  code: string
  orderType: 'create' | 'upgrade' | 'renew' | 'gift'
}

export interface OrderDetail {
  orderInfo: Omit<Order, 'remainingTimeInSeconds'> & {
    teamName: string
    createdAt: number
  }
  vipInfo: VipInfo
}

/**
 * VipInfo
 */
export interface VipInfo {
  expirationTime: number
  freeMonth: number
  interestCount: number
  capacityLimit: number
  month: number
  platformAccountCount: number
  teamMemberCount: number
}

export type OrderStatus = 'pending' | 'paid' | 'cancelled' | 'refunded'
