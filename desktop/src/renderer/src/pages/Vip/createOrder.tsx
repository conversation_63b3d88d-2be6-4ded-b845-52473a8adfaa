import { useOrderState } from './hooks/useOrderState'
import { BaseOrderForm } from './components/BaseOrderForm'
import { useHasPendingOrder } from './hooks/useHasPendingOrder'
import { LoadingButton } from '@renderer/components/LoadingButton'
import type { VipData } from './types/vip'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { useVipInterest } from './hooks/useVipInterest'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { OrderSummary } from '@renderer/pages/Vip/orderSummary'

export function CreateOrder({
  onSuccess,
  onClose,
}: {
  onSuccess: (orderId: string, isTransfer: boolean) => void
  onClose: () => void
}) {
  const {
    activeMonth,
    setActiveMonth,
    activeVip,
    setActiveVip,
    num,
    setNum,
    team,
    calculateTotalPrice,
    selectedCoupon,
  } = useOrderState()
  const { data: vipInterestData, isLoading } = useVipInterest()

  useHasPendingOrder(onClose)

  const createMutation = useApiMutation<
    {
      interestId: string
      interestCount: number
      month: number
      couponId?: number
      isCorporateTransfer: boolean // 是否为对公转账
    },
    { orderNo: string }
  >(
    (data) => ({
      url: '/orders',
      method: 'POST',
      data,
    }),
    {
      onSuccess: (data, { isCorporateTransfer }) => {
        onSuccess(data.orderNo, isCorporateTransfer)
      },
    },
  )

  const submitOrder = (vipInterestData: VipData, month: number, isCorporateTransfer: boolean) => {
    createMutation.mutate({
      interestCount: num,
      interestId: vipInterestData.id,
      month: month,
      couponId: selectedCoupon?.id,
      isCorporateTransfer: isCorporateTransfer,
    })
  }

  if (isLoading) {
    return <LoadingContainer />
  }

  if (!vipInterestData) {
    return <div>No data available</div>
  }

  const totalPrice = calculateTotalPrice(vipInterestData)

  return (
    <div className="flex w-full flex-1">
      <BaseOrderForm
        activeVip={activeVip}
        setActiveVip={setActiveVip}
        activeMonth={activeMonth}
        setActiveMonth={setActiveMonth}
        num={num}
        setNum={setNum}
        minInterestCount={1}
        VipData={vipInterestData}
        salesType={team?.salesType}
      />
      <OrderSummary
        vipInterestData={vipInterestData}
        totalPrice={totalPrice}
        teamName={team?.name || ''}
        priceQuery={{
          orderType: 'create',
          interestId: vipInterestData?.id,
          month: vipInterestData?.vipOften[activeMonth].mount,
          interestCount: num,
        }}
        renderSubmit={(isTransfer, termsAccepted) => (
          <LoadingButton
            isPending={createMutation.isPending}
            disabled={createMutation.isPending || !termsAccepted}
            className="h-10 w-full"
            variant="default"
            onClick={() => {
              submitOrder(vipInterestData, totalPrice.month, isTransfer)
            }}
          >
            提交订单
          </LoadingButton>
        )}
      />
    </div>
  )
}
