import {
  Dialog,
  DialogClose,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { DialogContent } from '@/components/dialog'
import { Cross2Icon } from '@radix-ui/react-icons'
import { CreateOrder } from './createOrder'
import { useRef, useState } from 'react'
import { VipPay } from './vipPay'
import { VipSuccess } from './vipSuccess'
import { CorporateTransfer } from './corporateTransfer'
import { cn } from '@/lib/utils'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/Tabs'
import { RenewOrder } from './renewOrder'
import { UpgradeOrder } from './upgradeOrder'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { VipDialogContext } from './vipDialogProvider'
import exchangeVIP from '@renderer/assets/exchange-VIP.svg'
import { VipExchangeDialog } from '@renderer/components/vip/VipExchangeDialog'
interface VipDialogProviderProps {
  children: React.ReactNode
}
export const VipDialogProvider: React.FC<VipDialogProviderProps> = ({ children }) => {
  const [open, setOpen] = useState(false)
  const [type, setType] = useState<'create' | 'pay' | 'success' | 'corporateTransfer'>('create')
  const orderType = useRef<'create' | 'upgrade'>('create')
  const orderNO = useRef<string>('')
  const [tabValue, setTabValue] = useState('renew')

  function show() {
    orderType.current = 'create'
    setOpen(true)
  }
  function pay(orderId: string, isUpgrade?: boolean) {
    orderNO.current = orderId
    orderType.current = isUpgrade ? 'upgrade' : 'create'
    setType('pay')
    setOpen(true)
  }
  function upgrade() {
    orderType.current = 'upgrade'
    setOpen(true)
  }

  const methods = {
    show,
    pay,
    upgrade,
  }

  // // 更新外部访问对象
  // dialogUtils = methods

  const successOrder = (isTransfer: boolean) => {
    if (isTransfer) {
      setType('corporateTransfer')
    } else {
      setType('pay')
    }
  }

  return (
    <VipDialogContext.Provider value={methods}>
      {children}
      <Dialog open={open} onOpenChange={setOpen}>
        <VisuallyHidden>
          <DialogTitle>开通VIP</DialogTitle>
          <DialogDescription></DialogDescription>
        </VisuallyHidden>
        <DialogContent
          isHideCloseBtn
          onOpenAutoFocus={(e) => e.preventDefault()}
          onCloseAutoFocus={(e) => e.preventDefault()}
          onAnimationEndCapture={(e) => {
            if (
              (e.target as HTMLElement).classList.contains('vip-dialog-content') &&
              e.animationName === 'exit'
            ) {
              setType('create')
              setTabValue('renew')
            }
          }}
          className={cn(
            'vip-dialog-content flex h-fit w-[860px] max-w-none flex-col gap-0 overflow-hidden border-none bg-white p-0',
            {
              'h-[568px]': orderType.current === 'upgrade',
            },
          )}
        >
          <Tabs
            value={tabValue}
            onValueChange={(value: string) => setTabValue(value)}
            className="flex h-full w-full flex-col overflow-hidden"
          >
            {type !== 'create' || orderType.current === 'create' ? (
              <div className="flex h-[67px] flex-shrink-0 items-center justify-between bg-[url('@renderer/assets/vip/bg-title.png')] bg-cover bg-center bg-no-repeat px-6">
                <span className="text-lg font-medium text-background">
                  {type === 'create' ? '开通VIP' : '订单支付'}
                </span>
                <div className="flex">
                  {type === 'create' && (
                    <VipExchangeDialog onClose={() => setOpen(false)}>
                      {() => (
                        <DialogTrigger asChild>
                          <div className="flex cursor-pointer pr-6">
                            <img src={exchangeVIP} alt="" />
                            <span className="text-[#ffffff]">兑换VIP</span>
                          </div>
                        </DialogTrigger>
                      )}
                    </VipExchangeDialog>
                  )}
                  <DialogClose
                    className={
                      'rounded-sm text-background opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground'
                    }
                  >
                    <Cross2Icon className="h-5 w-5" />
                    <span className="sr-only">Close</span>
                  </DialogClose>
                </div>
              </div>
            ) : (
              <div className="h-[68px] w-full flex-shrink-0 bg-[url('@renderer/assets/vip/bg-title.png')] bg-cover bg-center bg-no-repeat px-6">
                <div className="flex h-max w-full items-center justify-between">
                  <TabsList className="h-[68px] gap-[30px] px-0">
                    <TabsTrigger
                      value="renew"
                      className="text-base font-normal text-[#FFFFFF99] data-[state=active]:font-medium data-[state=active]:text-[#FFFFFF] data-[state=active]:after:bg-[#FFFFFF]"
                    >
                      续费
                    </TabsTrigger>
                    <TabsTrigger
                      value="upgrade"
                      className="text-base font-normal text-[#FFFFFF99] data-[state=active]:font-medium data-[state=active]:text-[#FFFFFF] data-[state=active]:after:bg-[#FFFFFF]"
                    >
                      升级
                    </TabsTrigger>
                  </TabsList>

                  <DialogClose
                    className={
                      'rounded-sm text-background opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground'
                    }
                  >
                    <Cross2Icon className="h-5 w-5" />
                    <span className="sr-only">Close</span>
                  </DialogClose>
                </div>
              </div>
            )}
            {open && (
              <>
                {type === 'create' && open && (
                  <>
                    {orderType.current === 'create' ? (
                      <CreateOrder
                        onSuccess={(id: string, isTransfer: boolean) => {
                          orderNO.current = id
                          successOrder(isTransfer)
                        }}
                        onClose={() => setOpen(false)}
                      />
                    ) : (
                      <div className="flex-1 overflow-hidden">
                        <TabsContent className="mt-0 h-full w-full" value="renew">
                          <RenewOrder
                            onSuccess={(id: string, isTransfer: boolean) => {
                              orderNO.current = id
                              successOrder(isTransfer)
                            }}
                            onClose={() => setOpen(false)}
                          />
                        </TabsContent>
                        <TabsContent className="mt-0 h-full w-full" value="upgrade">
                          <UpgradeOrder
                            onSuccess={(id: string, isTransfer: boolean) => {
                              orderNO.current = id
                              successOrder(isTransfer)
                            }}
                            onClose={() => setOpen(false)}
                          />
                        </TabsContent>
                      </div>
                    )}
                  </>
                )}
                {type === 'pay' && (
                  <VipPay orderNO={orderNO.current} onSuccess={() => setType('success')} />
                )}
                {type === 'success' && <VipSuccess orderType={orderType.current} />}
                {type === 'corporateTransfer' && <CorporateTransfer ok={() => setOpen(false)} />}
              </>
            )}
          </Tabs>
        </DialogContent>
      </Dialog>
    </VipDialogContext.Provider>
  )
}
