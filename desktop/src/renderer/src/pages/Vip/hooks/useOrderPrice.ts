import { useApiQuery } from '@renderer/hooks/useApiQuery'

export type OrderPriceReq = {
  orderType: string
  interestId?: string
  interestCount?: number
  month?: number
  couponId?: number
}

export const useOrderPriceQuery = (data: OrderPriceReq, enabled?: boolean) =>
  useApiQuery<{
    orderAmount: number
    discountAmount: number
    expiredTime: number
    tips: string
    tipsCn: string
  }>(
    {
      url: '/orders/price',
      method: 'POST',
      data,
    },
    ['orderPrice', data],
    {
      enabled,
    },
  )
