import { useState, useEffect, useRef } from 'react'

function useCountdown(seconds: number, onEnd?: () => void) {
  const [timeLeft, setTimeLeft] = useState(seconds)
  const intervalRef = useRef<number | null>(null)

  useEffect(() => {
    if (timeLeft > 0) {
      // 启动定时器
      intervalRef.current = window.setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!)
            onEnd?.()
            return 0 // 防止负数
          }
          return prev - 1
        })
      }, 1000)
    }

    // 清理定时器
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onEnd]) // 仅在组件挂载时执行

  return timeLeft // 返回剩余时间
}

export default useCountdown
