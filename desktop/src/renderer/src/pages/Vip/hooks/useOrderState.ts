import { useState, useMemo } from 'react'
import type { Coupon } from '../types/coupon'
import type { VipData } from '../types/vip'
import { useContextStore } from '@renderer/store/contextStore'

export function useOrderState(initialMonth = 0, initialVip = 0) {
  const [activeMonth, setActiveMonth] = useState(initialMonth)
  const [activeVip, setActiveVip] = useState(initialVip)
  const [num, setNum] = useState(1)
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null)
  const team = useContextStore((state) => state.currentTeam)

  const oldVip = useMemo(() => {
    setNum(team?.interestCount || 1)
    return team
  }, [team])

  const calculateTotalPrice = (vipInterestData: VipData, interestCount?: number) => {
    if (vipInterestData) {
      const interests = vipInterestData
      const month = vipInterestData.vipOften[activeMonth]
      const account = interests.platformAccountCount || 0
      const capacity = interests.capacityLimit || 0
      const traffic = interests.networkTrafficLimit || 0
      const count = interestCount || num

      return {
        account: account * count,
        capacity: capacity * count,
        traffic: traffic * count,
        month: month.mount,
        present: month.present,
        member: interests.memberCount * count,
      }
    }
    return {
      account: 0,
      capacity: 0,
      traffic: 0,
      month: 0,
      present: 0,
      member: 0,
    }
  }

  return {
    activeMonth,
    setActiveMonth,
    activeVip,
    setActiveVip,
    num,
    setNum,
    selectedCoupon,
    setSelectedCoupon,
    team,
    oldVip,
    calculateTotalPrice,
  }
}
