import { alertBaseManager } from '@renderer/components/alertBase'
import { useApiQueryWithMakeRequest } from '@renderer/hooks/useApiQuery'
import { useQueryClient } from '@tanstack/react-query'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { features } from '@renderer/infrastructure/model/features/features'

export function useHasPendingOrder(onClose: () => void) {
  const queryclient = useQueryClient()
  const { isActiveFeature, openFeature } = useFeatureManager()
  useApiQueryWithMakeRequest(
    (request) =>
      request<{ hasPendingOrder: boolean; count: number }>({
        url: '/orders/pending',
        method: 'GET',
      }).then((res) => {
        if (res.hasPendingOrder) {
          alertBaseManager.open({
            okText: '我知道了',
            cancelText: '查看订单',
            onSubmit: () => {
              onClose()
            },
            onCancel: () => {
              onClose()
              // 当前是否打开了我的订单
              if (isActiveFeature(features.团队订单)) {
                void queryclient.invalidateQueries({ queryKey: ['vipOrders'] })
              } else {
                openFeature(features.团队订单)
              }
            },
            title: '你有一个未完成的订单，请先支付/取消后再提交。',
          })
        }
        return res
      }),
    ['vipOrdersPending'],
  )
}
