import type { OrderPriceReq } from '@renderer/pages/Vip/hooks/useOrderPrice'
import { useOrderPriceQuery } from '@renderer/pages/Vip/hooks/useOrderPrice'
import type { VipData } from '@renderer/pages/Vip/types/vip'
import type { useOrderState } from '@renderer/pages/Vip/hooks/useOrderState'
import { useMemo, useState } from 'react'
import { Row } from '@renderer/pages/Vip/components/Row'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { cn, formatMount, formatPrice } from '@renderer/lib/utils'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { Check } from 'lucide-react'
import { DateUtils } from '@renderer/utils/date-utils'
import { Loading } from '@renderer/components/LoadingContainer'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Checkbox } from '@/components/ui/checkbox'
import { Head2, Paragraph, Terms } from '@renderer/pages/Login/Terms'

export const OrderSummary = ({
  vipInterestData,
  totalPrice,
  teamName,
  priceQuery,
  renderSubmit,
}: {
  priceQuery: OrderPriceReq
  teamName: string
  vipInterestData: VipData
  totalPrice: ReturnType<ReturnType<typeof useOrderState>['calculateTotalPrice']>
  renderSubmit?: (isTransfer: boolean, termsAccepted: boolean) => React.ReactNode | null
}) => {
  const [activePay, setActivePay] = useState('alipay/wechat')
  const orderPriceQuery = useOrderPriceQuery(priceQuery, !!vipInterestData)

  const [termsAccepted, setTermsAccepted] = useState(false)

  const [orderAmount, discountAmount] = useMemo(() => {
    if (!orderPriceQuery.data) {
      return [0, 0]
    }
    return [
      orderPriceQuery.data.orderAmount,
      orderPriceQuery.data.orderAmount - orderPriceQuery.data.discountAmount,
    ]
  }, [orderPriceQuery.data])
  const typeTitle =
    priceQuery.orderType === 'create'
      ? '开通'
      : priceQuery.orderType === 'upgrade'
        ? '升级'
        : '续费'
  return (
    <div className="flex h-full flex-1 flex-col justify-between border-l p-6">
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-3">
          <div className="font-medium">订单详情</div>
          <ul className="flex flex-col gap-3.5">
            <Row
              label={`${typeTitle}团队`}
              value={<span className="flex items-center gap-1">{teamName}</span>}
            />
            {priceQuery.orderType !== 'upgrade' ? (
              <Row
                label={`${typeTitle}时长`}
                value={`${formatMount(totalPrice.month)}${totalPrice.present ? `（送${formatMount(totalPrice.present)}）` : ''}`}
              />
            ) : (
              <>
                <Row label={`升级权益包`} value={priceQuery.interestCount} />
                <Row
                  label={`升级后权益`}
                  value={
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <span className="flex items-center gap-1">
                          <span>账号数</span>
                          <span className="font-medium">{totalPrice.account}</span>
                        </span>
                        <span className="flex items-center gap-1">
                          <span>成员数</span>{' '}
                          <span className="font-medium">{totalPrice.member}</span>
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="flex items-center gap-1">
                          <span>素材库</span>
                          <span className="font-medium">
                            {ByteSize.fromB(totalPrice.capacity).toString()}
                          </span>
                        </span>
                        <span className="flex items-center gap-1">
                          <span>云发布流量</span>
                          <span className="font-medium">
                            {ByteSize.fromB(totalPrice.traffic).toString()}
                          </span>
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        {/*<span className="flex items-center gap-1">*/}
                        {/*  <span>素材库</span>*/}
                        {/*  <span className="font-medium">*/}
                        {/*    {ByteSize.fromB(totalPrice.capacity).toString()}*/}
                        {/*  </span>*/}
                        {/*</span>*/}
                        <span className="flex items-center gap-1">
                          <span>手机APP发布</span>
                          <Check className="w-4 font-medium text-foreground" />
                        </span>
                      </div>
                    </div>
                  }
                />
              </>
            )}
            <Row
              label={`${typeTitle}到期`}
              value={
                !orderPriceQuery.isSuccess ? (
                  <Loading />
                ) : (
                  DateUtils.formatDate(orderPriceQuery.data!.expiredTime, 'yyyy-MM-dd')
                )
              }
            />
            <Row
              label="订单金额"
              value={
                !orderPriceQuery.isSuccess ? (
                  <Loading />
                ) : (
                  <div className="flex items-center gap-1">
                    <span className="text-destructive">¥{formatPrice(orderAmount)}</span>
                    {priceQuery.orderType === 'upgrade' && (
                      <HelpTooltop
                        title={
                          <span className="flex flex-col gap-1">
                            <span>{orderPriceQuery.data?.tipsCn}</span>
                            <span>{orderPriceQuery.data?.tips}</span>
                          </span>
                        }
                      />
                    )}
                  </div>
                )
              }
            />
            <Row
              label="付款方式"
              value={
                <div className="flex items-center gap-2">
                  {[
                    {
                      name: '支付宝/微信',
                      key: 'alipay/wechat',
                    },
                    {
                      name: '对公转账',
                      key: 'corporateTransfer',
                    },
                  ].map((item) => (
                    <Button
                      key={item.key}
                      onClick={() => {
                        setActivePay(item.key)
                      }}
                      className={cn('h-8 px-2 font-medium text-[#757575]', {
                        'text-background, border-primary bg-accent text-[#222222] hover:bg-accent':
                          activePay === item.key,
                      })}
                      variant="outline"
                    >
                      {item.name}
                    </Button>
                  ))}
                </div>
              }
            />
          </ul>
        </div>
        <Separator />
        <div className="flex h-16 flex-col items-center justify-center rounded-lg bg-[#F9F9FA] px-4">
          <div className="flex w-full justify-between">
            <span className="text-sm">应付金额</span>
            <span className="text-sm text-destructive">
              {!orderPriceQuery.isSuccess ? (
                <Loading />
              ) : (
                <>
                  ¥<span className="text-2xl font-bold">{formatPrice(discountAmount)}</span>
                </>
              )}
            </span>
          </div>
          {discountAmount < orderAmount && (
            <div className="flex w-full justify-end gap-0.5 text-xs text-[#969697]">
              <span>原价</span>
              <span className="line-through">{formatPrice(orderAmount)}</span>
            </div>
          )}
        </div>
      </div>
      <div className="flex flex-col gap-2">
        {renderSubmit && renderSubmit(activePay === 'corporateTransfer', termsAccepted)}
        <div
          className="flex cursor-pointer items-center space-x-2"
          onClick={(e) => {
            e.stopPropagation()
            setTermsAccepted(!termsAccepted)
          }}
        >
          <Checkbox checked={termsAccepted} />
          <div className="flex items-center text-sm font-medium leading-none">
            <div className="peer-disabled:cursor-not-allowed peer-disabled:opacity-70">同意</div>

            <div
              className="inline-block cursor-pointer text-primary"
              onClick={(e) => e.stopPropagation()}
            >
              <BuyTerms
                onAccept={() => {
                  setTermsAccepted(true)
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function BuyTerms(props: { onAccept: () => void }) {
  return (
    <Terms
      trigger="《购买服务协议》"
      title="购买服务协议"
      content={
        <>
          <Paragraph>
            本《购买服务协议》是甲方（下称“用户”）使用长沙草儿绽放科技有限公司开发提供的互联网服务产品——“蚁小二”的网站、客户端、APP、微信公众号、微信小程序等产品的各项功能及服务，在注册用户（又名“账号”，下统称“账号”）和使用“蚁小二”产品时与长沙草儿绽放科技有限公司所签署的协议。
          </Paragraph>
          <Head2>一、重要须知——在签署本协议之前，蚁小二正式提醒用户：</Head2>
          <Paragraph>
            1.1
            用户应认真阅读（未成年人应当在监护人陪同下阅读）、充分理解本《购买服务协议》中各条款，特别是免除或者限制蚁小二责任的免责条款，用户的权利限制条款，约定争议解决方式、司法管辖、法律适用的条款。
          </Paragraph>
          <Paragraph>
            1.2
            除非用户接受本协议，否则用户无权也无必要继续接受蚁小二的服务，可以退出本次服务。用户点击接受并继续使用蚁小二的服务，视为用户已完全的接受本协议。
          </Paragraph>
          <Paragraph>
            1.3
            本协议在用户开始使用蚁小二的服务，注册成为蚁小二产品的用户时即产生法律效力，请用户慎重考虑是否接受本协议，如不接受本协议的任一条款，请自动退出并不再接受蚁小二的任何服务。
          </Paragraph>
          <Paragraph>
            1.4
            在用户签署本协议之后，此文本可能因国家政策、产品以及履行本协议的环境发生变化而进行修改，我们会将修改后的协议发布在本网站上，若用户对修改后的协议有异议的，请立即停止登录、使用蚁小二产品及服务，若用户登录或继续使用蚁小二产品，视为认可修改后的协议。
          </Paragraph>
          <Head2>二、关于“账号”及“付费会员”资格</Head2>
          <Paragraph>
            2.1
            蚁小二在旗下业务平台（包括但不限于蚁小二）提供用户注册通道，用户在认可并接受本协议之后，有权选择未被其他用户使用过的手机号码或字母符号组合作为用户的账号，并自行设置符合安全要求的密码。用户设置的账号、密码是用户用以登录蚁小二产品，接受蚁小二服务的凭证。
          </Paragraph>
          <Paragraph>1)用户可通过各种已有和未来新增的渠道注册蚁小二账号及付费成为会员。</Paragraph>
          <Paragraph>2)用户在加入付费会员时，须仔细阅读并确认相关的用户协议和使用方法。</Paragraph>
          <Paragraph>
            3)用户通过网络填写并提交注册表，表中所填写的个人资料与内容必须真实有效，否则蚁小二有权拒绝其申请，有权撤销其账号或付费会员资格，并不予任何赔偿或退还会员费。用户的个人资料发生变化，应及时修改相关资料，否则由此造成的会员权力不能全面有效行使的责任由会员自己承担，蚁小二有权因此取消其会员资格，并不予任何赔偿或退还会员费。
          </Paragraph>
          <Paragraph>
            4)成为付费会员后，会员有权利不接受蚁小二的产品或服务，可申请取消会员服务，但不得向蚁小二主张退还任何服务费用。
          </Paragraph>
          <Paragraph>
            2.2
            用户在注册了蚁小二账号并不意味获得全部蚁小二产品服务的授权，仅是取得了接受蚁小二服务的身份，用户在登录相关网页、加载应用、下载安装软件时将按需要另行签署单个产品的授权协议。
          </Paragraph>
          <Paragraph>
            2.3 蚁小二账户仅限于在蚁小二网站上注册用户本人使用，禁止赠与、借用、租用、转让或售卖。
            如果蚁小二发现或者有理由怀疑使用者并非账号初始注册人，有权在未经通知的情况下，暂停或终止向用户提供服务，并有权注销该账号，而无需向该账号使用人承担任何法律责任，由此带来的包括但不限于用户通讯中断、用户资料和信息等清空等损失由用户自行承担。
          </Paragraph>
          <Paragraph>
            2.4
            用户有责任维护其个人账号、密码的安全性与保密性，用户就其账号及密码项下之一切活动负全部责任，包括用户数据的修改发表的言论以及其他所有的损失。用户应重视蚁小二账号密码保护。用户如发现他人未经许可使用其账号或发生其他任何安全漏洞问题时应立即通知蚁小二。如果用户在使用蚁小二服务时违反上述规则而产生任何损失或损害，蚁小二不承担任何责任。
          </Paragraph>
          <Paragraph>
            2.5
            用户账号在丢失或遗忘密码后，可遵照蚁小二的申诉途径及时申诉请求找回账号。用户应提供能增加账号安全性的个人密码保护资料。用户可以凭初始注册资料及个人密码保护资料填写申诉单向蚁小二申请找回账号，蚁小二的密码找回机制仅负责识别申诉单上所填资料与系统记录资料的正确性，而无法识别申诉人是否系真正账号注册使用人。对用户因被他人冒名申诉而致的任何损失，蚁小二不承担任何责任，用户知晓账号及密码保管责任在于用户，蚁小二并无义务保证账号丢失或遗忘密码后用户一定能通过申诉找回账号。
          </Paragraph>
          <Head2>
            三、用户在使用蚁小二产品或服务时，应当遵守《中华人民共和国宪法》、《中华人民共和国刑法》、《中华人民共和国民法典》、《中华人民共和国著作权法》、《中华人民共和国电信条例》、《互联网信息服务管理办法》、《计算机信息网络国际联网安全保护管理办法》等相关法律法规。用户不得利用蚁小二的服务产品从事违反法律法规、政策以及侵犯他人合法权益的行为，包括但不限于下列行为：
          </Head2>
          <Paragraph>
            3.1
            利用蚁小二服务产品发表、传送、传播、储存反对宪法所确定的基本原则、危害国家安全、国家统一、社会稳定、煽动民族仇恨、民族歧视、破坏民族团结的内容，或侮辱诽谤、色情、暴力、引起他人不安及任何违反国家法律法规政策的内容或者设置含有上述内容的网名、角色名。
          </Paragraph>
          <Paragraph>
            3.2
            利用蚁小二服务发表、传送、传播、储存侵害他人知识产权、商业机密、肖像权、隐私权等合法权利或其他道德上令人反感的内容。
          </Paragraph>
          <Paragraph>
            3.3
            进行任何危害计算机网络安全的行为，包括但不限于：使用未经许可的数据或进入未经许可的服务器/账户；未经允许进入公众计算机网络或者他人计算机系统删除、修改、增加存储信息；未经许可，企图探查、扫描、测试本蚁小二产品或服务软件系统或网络的弱点或其它实施破坏网络安全的行为；企图干涉、破坏蚁小二产品或服务软件系统或网站的正常运行，故意传播恶意程序或病毒以及其他破坏干扰正常网络信息服务的行为；伪造TCP/IP数据包名称或部分名称；自行或利用其他软件对蚁小二提供产品进行反向破解等行为。
          </Paragraph>
          <Paragraph>
            3.4 进行诸如发布广告、推广信息、销售商品的行为，或者进行任何非法侵害蚁小二利益的行为。
          </Paragraph>
          <Paragraph>
            3.5
            进行任何违法以及侵犯他人、公司、社会团体、组织的合法权益的行为或者法律、行政法规、规章、条例以及任何具有法律效力之规范所限制或禁止的行为。
          </Paragraph>
          <Paragraph>
            3.6在任何情况下，如果蚁小二有理由认为用户的行为，包括但不限于用户的言论或其它行为，违反或可能违反法律法规、国家政策以及本协议的任何规定，蚁小二可在不经事先通知的情形下，有权终止向用户提供服务。同时，蚁小二有权依合理判断对违反有关法律法规或本协议规定的行为追究相关用户的法律责任，并有权依据法律法规向有关部门报告等，由此而产生的一切法律责任由用户承担。
          </Paragraph>
          <Head2>四、蚁小二声明</Head2>
          <Paragraph>
            4.1
            用户须知，为了向用户提供有效的服务，使用蚁小二提供的产品或服务会使用用户终端设备的处理器和带宽等资源，在使用过程中可能产生数据流量的费用，用户需自行向电信运营商了解相关资费信息，并自行承担相关费用。
          </Paragraph>
          <Paragraph>
            4.2
            用户须知，在使用蚁小二服务时，可能存在有来自任何他人的包括威胁性的、诽谤性的、令人反感的或非法的内容或行为或对他人权利的侵犯（包括知识产权）的匿名或冒名的信息的风险，用户须承担以上风险。蚁小二对服务不作担保，不论是明确的或隐含的，包括所有有关信息真实性、适当性、适于某一特定用途、所有权和非侵权性的默示担保和条件，对因此导致任何因用户不正当或非法使用服务产生的直接、间接、偶然、特殊及后续的损害，蚁小二不承担任何责任。
          </Paragraph>
          <Paragraph>
            4.3
            使用蚁小二服务必须遵守国家有关法律法规和政策等，维护国家利益，保护国家安全，并遵守本协议，对于用户违法行为或违反本协议的使用（包括但不限于言论发表、传送等）而引起的一切责任，由用户全部承担。
          </Paragraph>
          <Paragraph>
            4.4
            蚁小二提供的所有信息、资讯、内容和服务均来自互联网，并不代表蚁小二的观点，蚁小二对其真实性、合法性概不负责，亦不承担任何法律责任。
          </Paragraph>
          <Paragraph>
            4.5 蚁小二所提供的产品和服务也属于互联网范畴，也易受到各种安全问题的困扰，包括但不限于：
          </Paragraph>
          <Paragraph>1)个人资料被不法分子利用，造成现实生活中的骚扰；</Paragraph>
          <Paragraph>2)哄骗、破译密码；</Paragraph>
          <Paragraph>
            3)下载安装的其它软件中含有“特洛伊木马”等病毒程序，威胁到个人计算机上信息和数据的安全，继而威胁对蚁小二产品或服务的使用；
          </Paragraph>
          <Paragraph>4)发布的内容被他人转发、分享，因此等传播可能带来的风险和责任；</Paragraph>
          <Paragraph>
            5)由于网络信号不稳定、网络带宽小等网络原因，所引起的账号登录失败、资料同步不完整、页面打开速度慢等风险；
          </Paragraph>
          <Paragraph>6)以及其他类网络安全困扰问题</Paragraph>
          <Paragraph>对于发生上述情况的，用户应当自行承担责任。</Paragraph>
          <Paragraph>
            维护软件安全与正常使用是蚁小二和用户的共同责任，蚁小二将按照行业标准合理审慎地采取必要技术措施保护用户的终端设备信息和数据安全，但是蚁小二并不能就此提供完全保证。
          </Paragraph>
          <Paragraph>
            4.6
            用户须明白，蚁小二为了整体运营的需要，有权在公告通知后，在不事先通知用户的情况下修改、中断、中止或终止服务，而无须向用户或第三方负责，蚁小二不承担任何赔偿责任。
          </Paragraph>
          <Paragraph>
            4.7
            为了改善用户体验、完善服务内容，蚁小二将不断努力开发新的服务，并为用户不时提供软件更新（这些更新可能会采取软件替换、修改、功能强化、版本升级等形式）。为了保证产品及服务的安全性和功能的一致性，蚁小二有权不经向用户特别通知而对软件进行更新，或者对软件的部分功能效果进行改变或限制，用户对此没有异议。
          </Paragraph>
          <Paragraph>
            4.8
            用户应理解，互联网技术存在不稳定性，可能导致政府管制、政策限制、病毒入侵、黑客攻击、服务器系统崩溃或者其他现今技术无法解决的风险发生。由以上原因可能导致蚁小二服务中断或账号信息损失，对此非人为因素引起的用户损失由用户自行承担责任。
          </Paragraph>
          <Paragraph>
            4.9
            关于服务退款说明：用户购买的商品为虚拟数据服务，购买后不支持退订、转让、退换，请斟酌确认。一经开通账号会员使用权限，权限生效后，即代表用户已享受到蚁小二的数据服务，蚁小二不接受用户任何借口为理由的退款请求。
          </Paragraph>
          <Paragraph>
            4.10
            关于蚁小二服务促销活动的说明：用户须明白，蚁小二为了整体运营的需要，有权在节假日/重大节日发起相关运营或促销活动，蚁小二无需提前事先通知已付费用户且不承担服务促销活动的任何损失，促销活动解释所有权归属蚁小二。
          </Paragraph>
          <Head2>五、知识产权</Head2>
          <Paragraph>
            5.1
            蚁小二对其旗下运营的网页、应用、软件等产品和服务享有知识产权（包括著作权、商标权、专利权、商业秘密等知识产权）。受中国法律法规和相应的国际条约的保护。未经蚁小二或相关权利人书面同意，甲方不得为任何商业或非商业目的自行或许可任何第三方实施、利用、转让上述知识产权。
          </Paragraph>
          <Paragraph>
            5.2用户不得对蚁小二服务涉及的相关网页、应用、软件等产品进行反向工程、反向汇编、反向编译等。
          </Paragraph>
          <Paragraph>
            5.3
            用户只能在本《购买服务协议》以及相应的授权许可协议授权的范围内使用蚁小二知识产权，未经授权超范围使用的，构成对蚁小二的侵权。蚁小二有权追究相关侵权的人法律责任。
          </Paragraph>
          <Paragraph>
            5.4
            用户在使用蚁小二产品服务时发表上传的文字、图片、视频、软件以及表演等信息，用户的发表、上传行为是对蚁小二服务平台的授权，为非独占性、永久性的授权，该授权可转授权。蚁小二可将前述信息在蚁小二旗下的所有服务平台上使用，可再次编辑后使用，也可以由蚁小二授权给合作方使用。
          </Paragraph>
          <Paragraph>
            5.5
            用户应保证，在使用蚁小二产品服务时上传的文字、图片、视频、软件以及表演等的信息不侵犯任何第三方知识产权，包括但不限于商标权、著作权等。若用户在使用蚁小二产品服务时上传的文字、图片、视频、软件以及表演等的信息中侵犯第三方知识产权，蚁小二有权移除该侵权产品，并对此不负任何责任。用户应当负责处理前述第三方的权利主张，承担由此产生的全部费用，包括但不限于侵权赔偿、律师费、诉讼费、保全费、保全担保费等及其他合理费用，并保证蚁小二不会因此而遭受任何损失。
          </Paragraph>
          <Paragraph>
            5.6
            任何单位或个人认为通过蚁小二提供服务的内容可能涉嫌侵犯其知识产权或信息网络传播权，应该及时向蚁小二提出书面权利通知投诉，并提供身份证明、权属证明及详细侵权情况证明。蚁小二在收到上述法律文件后，将会依法尽快断开相关链接内容。蚁小二提供投诉通道：<EMAIL>。如投诉中未向蚁小二提供合法有效的证明材料，蚁小二有权不采取任何措施。
          </Paragraph>
          <Head2>六、隐私保护</Head2>
          <Paragraph>
            蚁小二非常重视用户的隐私权，用户在享受蚁小二提供的服务时可能涉及用户的隐私，因此请用户仔细阅读本隐私保护条款。
          </Paragraph>
          <Paragraph>
            6.1
            请用户注意勿在使用蚁小二服务中透露自己的各类财产账户、银行卡、信用卡、第三方支付账户及对应密码等重要信息资料，否则由此带来的任何损失由用户自行承担。
          </Paragraph>
          <Paragraph>
            6.2
            用户的账号、密码属于保密信息，蚁小二会努力采取积极的措施保护用户账号、密码的安全，但是，蚁小二并不能就此提供完全保证。
          </Paragraph>
          <Paragraph>
            6.3
            互联网的开放性以及技术更新速度快，因非蚁小二可控制的因素导致用户信息泄漏的，蚁小二不承担任何责任。
          </Paragraph>
          <Paragraph>
            6.4
            用户在使用蚁小二服务时不应将自认为隐私的信息发表、上传至蚁小二，也不应将该等信息通过蚁小二的服务传播给其他人，由于用户的行为引起的隐私泄漏，由用户自行承担责任。
          </Paragraph>
          <Head2>七、免责声明</Head2>
          <Paragraph>用户充分了解并同意:</Paragraph>
          <Paragraph>
            7.1
            关于产品和服务，蚁小二不提供任何种类的明示或暗示担保或条件，包括但不限于商业适售性、特定用途适用性等。用户对账号的使用行为必须自行承担相应风险。
          </Paragraph>
          <Paragraph>
            7.2
            用户应对本服务所提供的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容的正确性、完整性或实用性的依赖而产生的风险。是否使用本网站任何文章或资料应由用户自行考虑且自负风险，因任何文章或资料使用而导致的用户微信公众平台账号之任何损失或数据流失等后果，由用户自行承担，蚁小二无法且不会对因前述风险而导致的任何损失或损害承担责任。
          </Paragraph>
          <Paragraph>
            7.3
            用户自蚁小二提供的产品或服务所取得的任何建议或信息，无论是书面或口头形式，除非本协议有明确规定，将不构成本协议以外之任何保证。
          </Paragraph>
          <Paragraph>
            7.4
            因用户使用蚁小二提供的产品或服务时，本软件可能会调用第三方系统或者通过第三方支持用户的使用或访问，使用或访问的结果由该第三方提供，蚁小二不保证通过第三方提供服务及内容的安全性、准确性、有效性及其他不确定的风险，由此若引发的任何争议及损害，与蚁小二无关，蚁小二不承担任何责任。
          </Paragraph>
          <Paragraph>
            7.5
            蚁小二的免费版和权益包（vip）在账号数、网站数、成员人数、素材库容量、高权益账号的不同，其他功能完全一样。蚁小二只是提供增加效率、便捷的工具，部分功能无法与平台做到一模一样，请悉知，这不构成退款理由。根据《中华人民共和国消费者权益保护法》第二十五条规定，在线下载或者消费者拆封的音像制品、计算机软件等数字化商品不适用七天无理由退货。
          </Paragraph>
          <Head2>八、其他</Head2>
          <Paragraph>
            8.1 用户注册并使用了蚁小二的商品或服务，即表示用户已阅读并同意受本协议的约束。
          </Paragraph>
          <Paragraph>8.2 本协议签订地为中华人民共和国湖南省长沙市岳麓区。</Paragraph>
          <Paragraph>
            8.3
            本协议的成立、生效、履行、解释及纠纷解决，适用中华人民共和国大陆地区法律（不包括冲突法）。
          </Paragraph>
          <Paragraph>
            8.4
            若用户和蚁小二之间发生任何纠纷或争议，首先应友好协商解决；协商不成的，用户同意将纠纷或争议提交本协议签订地有管辖权的人民法院管辖。
          </Paragraph>
          <Paragraph>
            8.5本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。
          </Paragraph>
          <Paragraph>
            8.6 本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。
          </Paragraph>
        </>
      }
      onAccept={props.onAccept}
    ></Terms>
  )
}
