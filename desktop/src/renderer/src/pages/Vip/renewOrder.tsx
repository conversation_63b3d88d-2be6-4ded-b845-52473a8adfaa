import { LoadingButton } from '@renderer/components/LoadingButton'
import { useOrderState } from './hooks/useOrderState'
import { useMemo } from 'react'
import { useVipInterest } from './hooks/useVipInterest'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { useHasPendingOrder } from './hooks/useHasPendingOrder'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { DurationSelection } from './components/DurationSelection'
import { InterestPackage } from './components/InterestPackage'
import { OrderSummary } from '@renderer/pages/Vip/orderSummary'

// 续费订单
export function RenewOrder({
  onSuccess,
  onClose,
}: {
  onSuccess: (orderId: string, isTransfer: boolean) => void
  onClose: () => void
}) {
  const {
    selectedCoupon,
    team,
    calculateTotalPrice,
    activeMonth: activeMouthIndex,
    setActiveMonth: setActiveMouthIndex,
  } = useOrderState(0, 1)

  useHasPendingOrder(onClose)

  const { data: vipInterestData, isLoading } = useVipInterest()
  const activeMonth = useMemo(() => {
    return (
      vipInterestData?.vipOften[activeMouthIndex] ?? {
        mount: 1,
        present: 0,
      }
    )
  }, [activeMouthIndex, vipInterestData?.vipOften])

  const createMutation = useApiMutation<
    {
      month: number
      interestId: string
      couponId?: number
      isCorporateTransfer: boolean // 是否为对公转账
    },
    { orderNo: string }
  >(
    (data) => ({
      url: '/orders/renew',
      method: 'POST',
      data,
    }),
    {
      onSuccess: (data, { isCorporateTransfer }) => {
        onSuccess(data.orderNo, isCorporateTransfer)
      },
    },
  )

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingContainer />
      </div>
    )
  }

  if (!vipInterestData) {
    return <div>No data available</div>
  }

  /**
   * 提交订单
   */
  const submitOrder = (isTransfer: boolean) => {
    createMutation.mutate({
      month: activeMonth?.mount,
      interestId: vipInterestData?.id,
      couponId: selectedCoupon?.id,
      isCorporateTransfer: isTransfer,
    })
  }
  return (
    <div className="flex h-full w-full">
      <div className="flex w-[467px] flex-col gap-9 px-6 pb-[34px] pt-[28px]">
        {/* 当前权益包 */}
        <InterestPackage
          title={`当前权益包: ${team?.interestCount || 1}`}
          interests={vipInterestData}
          activeVip={1}
          setActiveVip={() => {}}
          num={team?.interestCount || 1}
          setNum={() => {}}
          showStepInput={false}
          extra={
            <li className="flex items-center justify-between">
              <span>剩余时长(天)</span>
              <span>
                <span className="text-base font-medium text-foreground">{team?.remainingDay}</span>
              </span>
            </li>
          }
        />

        <DurationSelection
          label="续费时长"
          vipOften={vipInterestData.vipOften}
          activeMonth={activeMouthIndex}
          setActiveMonth={setActiveMouthIndex}
        />
      </div>

      {/* 订单详情 */}
      <OrderSummary
        teamName={team?.name || ''}
        vipInterestData={vipInterestData}
        totalPrice={calculateTotalPrice(vipInterestData)}
        priceQuery={{
          orderType: 'renew',
          interestId: vipInterestData?.id,
          month: activeMonth?.mount,
        }}
        renderSubmit={(value, termsAccepted) => (
          <LoadingButton
            isPending={createMutation.isPending}
            disabled={createMutation.isPending || !termsAccepted}
            className="h-10 w-full"
            variant="default"
            onClick={() => {
              submitOrder(value)
            }}
          >
            提交订单
          </LoadingButton>
        )}
      />
    </div>
  )
}
