import { Button } from '@/components/ui/button'
import vipOk from '@renderer/assets/vip/vip-ok.png'
import { useContextStore } from '@renderer/store/contextStore'
// import serviceQrCode from '@renderer/assets/vip/service-qr-code.jpeg'

export const CorporateTransfer = ({ ok }: { ok: () => void }) => {
  const serviceQrCode = useContextStore((s) => s.currentTeam)?.corporateTransfer
  return (
    <div className="flex h-full w-full flex-1 flex-col items-center justify-center p-6">
      <img className="h-auto w-[130px]" src={vipOk} alt="vipOk" />
      <p className="text-lg font-semibold leading-8 text-[rgba(0,0,0,0.85)]">订单提交成功</p>
      <p className="text-sm font-normal text-[#8A8A8A]">请扫码添加客服经理微信为你开通</p>
      <img className="mb-8 mt-7 h-[200px] w-[200px]" src={serviceQrCode} alt="客服二维码" />
      <Button variant="outline" className="h-9 w-[200px]" onClick={ok}>
        好 的
      </Button>
    </div>
  )
}
