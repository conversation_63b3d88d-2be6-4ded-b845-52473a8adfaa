import { useState } from 'react'
import { useHasPendingOrder } from './hooks/useHasPendingOrder'
import { LoadingButton } from '@/components/loadingButton'
import { add } from 'lodash'
import { useOrderState } from './hooks/useOrderState'
import { StepInput } from './components/stepInput'
import { useVipInterest } from './hooks/useVipInterest'
import { useApiMutation } from '@/hooks/useApiQuery'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { InterestPackage } from './components/InterestPackage'
import { OrderSummary } from '@renderer/pages/Vip/orderSummary'

// 升级订单
export function UpgradeOrder({
  onSuccess,
  onClose,
}: {
  onSuccess: (orderId: string, isTransfer: boolean) => void
  onClose: () => void
}) {
  const { team, oldVip, calculateTotalPrice } = useOrderState(0)

  useHasPendingOrder(onClose)

  // 已有权益包数量
  const oldInterestCount = oldVip?.interestCount ?? 1
  // 最小权益包数量
  const minInterestCount = add(oldInterestCount, 1)
  const [interestCount, setInterestCount] = useState(minInterestCount)

  const createMutation = useApiMutation<
    {
      interestId?: string
      interestCount: number
      couponId?: number
      isCorporateTransfer: boolean // 是否为对公转账
    },
    { orderNo: string }
  >(
    (data) => ({
      url: '/orders/upgrade',
      method: 'POST',
      data,
    }),
    {
      onSuccess: (data, { isCorporateTransfer }) => {
        onSuccess(data.orderNo, isCorporateTransfer)
      },
    },
  )

  const { data: vipInterestData, isLoading } = useVipInterest()

  /**
   * 提交订单
   */
  const submitOrder = (isTransfer: boolean) => {
    createMutation.mutate({
      interestId: vipInterestData?.id,
      interestCount: interestCount,
      isCorporateTransfer: isTransfer,
    })
  }

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingContainer />
      </div>
    )
  }

  if (!vipInterestData) {
    return <div>No data available</div>
  }

  return (
    <div className="flex h-full w-full">
      <div className="flex w-[450px] flex-shrink-0 flex-col gap-9 px-6 py-7">
        {/* 当前权益包 */}
        <InterestPackage
          title={`当前权益包: ${team?.interestCount || 1}`}
          interests={vipInterestData}
          activeVip={1}
          setActiveVip={() => {}}
          num={team?.interestCount || 1}
          setNum={() => {}}
          showStepInput={false}
          extra={
            <li className="flex items-center justify-between">
              <span>剩余时长(天)</span>
              <span>
                <span className="text-base font-medium text-foreground">{team?.remainingDay}</span>
              </span>
            </li>
          }
        />

        {/* 选择权益包数量 */}
        <div className="mt-3 flex flex-col gap-3">
          <div className="font-medium">选择权益包数量</div>
          <div className="w-[168px]">
            <StepInput
              num={interestCount}
              setNum={setInterestCount}
              min={minInterestCount}
              max={9999}
            />
          </div>
        </div>
      </div>

      <OrderSummary
        vipInterestData={vipInterestData}
        priceQuery={{
          orderType: 'upgrade',
          interestId: vipInterestData?.id,
          interestCount: interestCount,
        }}
        teamName={team?.name || ''}
        totalPrice={calculateTotalPrice(vipInterestData, interestCount)}
        renderSubmit={(value, termsAccepted) => (
          <LoadingButton
            isPending={createMutation.isPending}
            disabled={createMutation.isPending || !termsAccepted}
            className="h-10 w-full"
            variant="default"
            onClick={() => {
              submitOrder(value)
            }}
          >
            提交订单
          </LoadingButton>
        )}
      />
    </div>
  )
}
