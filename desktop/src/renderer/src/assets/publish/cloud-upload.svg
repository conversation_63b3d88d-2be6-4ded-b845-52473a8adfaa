<?xml version="1.0" encoding="UTF-8"?>
<svg width="112px" height="64px" viewBox="0 0 112 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
        <linearGradient x1="1.23521959%" y1="71.3164807%" x2="100%" y2="28.1435694%" id="linearGradient-1">
            <stop stop-color="#F4F4F8" offset="0%"></stop>
            <stop stop-color="#E0DFE7" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="视频发布流程优化" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="发布详情" transform="translate(-494.000000, -285.000000)">
            <g id="上传" transform="translate(494.000000, 285.000000)">
                <rect id="矩形" x="0" y="0" width="112" height="64"></rect>
                <path d="M88.2666667,32 C88.2666667,14.326 73.8212833,0 56,0 C40.8790333,0 28.22445,10.33 24.7255333,24.252 C15.03545,25.708 7.6,33.982 7.6,44 C7.6,55.046 16.6286167,64 27.7666667,64 L43.9,64 C46.1284167,64 47.9333333,62.21 47.9333333,60 L47.9333333,49 C47.9333333,47.344 46.5781333,46 44.9083333,46 L38.7353167,46 C35.7688,46 34.9379333,43.98 36.8920833,41.51 L52.4466333,21.852 C54.4007833,19.382 57.5992167,19.382 59.5533667,21.852 L75.1079167,41.51 C77.0620667,43.98 76.2332167,46 73.2646833,46 L67.0916667,46 C65.4218667,46 64.0666667,47.344 64.0666667,49 L64.0666667,60 C64.0666667,62.21 65.8715833,64 68.1,64 L88.2666667,64 C97.1763,64 104.4,56.836 104.4,48 C104.4,39.164 97.1763,32 88.2666667,32 Z" id="路径" fill="url(#linearGradient-1)" fill-rule="nonzero" opacity="0.903683685"></path>
            </g>
        </g>
    </g>
</svg>
