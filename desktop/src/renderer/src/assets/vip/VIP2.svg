<?xml version="1.0" encoding="UTF-8"?>
<svg width="51px" height="26px" viewBox="0 0 51 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>VIP</title>
    <defs>
        <linearGradient x1="0%" y1="43.9176324%" x2="97.2941016%" y2="55.5597743%" id="linearGradient-1">
            <stop stop-color="#F9D8AC" offset="0%"></stop>
            <stop stop-color="#F0C48B" offset="100%"></stop>
        </linearGradient>
        <filter x="-38.4%" y="-40.6%" width="176.7%" height="181.2%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.349019608   0 0 0 0 0.176470588   0 0 0 0 0.031372549  0 0 0 0.10470389 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FADAB0" offset="0%"></stop>
            <stop stop-color="#DC9A45" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-4" points="6.67352907 6.94351644 5.36185391 6.94351644 7.5600928 12.8101831 8.92700991 12.8101831 11.6455209 6.94351644 10.2953048 6.94351644 8.32981911 10.792742"></polygon>
        <filter x="-15.9%" y="-17.0%" width="131.8%" height="134.1%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.247058824   0 0 0 0 0.129411765   0 0 0 0 0.0274509804  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M1.98287071,0.352941176 L0.233970491,0.352941176 L1.20862693,8.11764706 L3.03118309,8.11764706 L7.11994394,0.352941176 L5.31965586,0.352941176 L2.56697941,5.41121968 L1.98287071,0.352941176 Z M9.99937712,0.352941176 L8.22649589,0.352941176 L6.8373107,8.11764706 L8.61875657,8.11764706 L9.99937712,0.352941176 Z M14.9668704,0.352941176 L11.5136061,0.352941176 L10.1346985,8.11764706 L11.9058668,8.11764706 L12.3460895,5.65616849 L13.9990657,5.65616849 C14.3776229,5.65616849 14.6893759,5.63047456 14.9240472,5.57223498 C15.1501538,5.51570833 15.3505664,5.43348776 15.5150076,5.31529567 C15.7578815,5.1478499 15.9499086,4.91670614 16.0699965,4.64725347 C16.2070308,4.36462024 16.3115194,4.00661813 16.3920271,3.57838595 L16.5667458,2.65169152 C16.659244,2.17207148 16.707206,1.78666251 16.707206,1.50745513 C16.707206,1.20940554 16.6301242,0.942188656 16.4382762,0.750340639 C16.2674657,0.570934313 16.0399711,0.455976928 15.794215,0.424884183 C15.6135345,0.389127162 15.4301867,0.368500537 15.2460778,0.363218749 C15.1530924,0.357805553 15.0600021,0.354378914 14.9668704,0.352941176 Z M12.6115934,4.1659205 L13.0192704,1.84318916 L14.3896134,1.84318916 C14.9925643,1.84318916 14.8880757,2.05901818 14.809281,2.57118387 C14.6996535,3.21181921 14.6893759,4.1659205 13.8431892,4.1659205 L12.6115934,4.1659205 Z" id="path-6"></path>
        <filter x="-3.0%" y="-6.4%" width="106.1%" height="112.9%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.247058824   0 0 0 0 0.129411765   0 0 0 0 0.0274509804  0 0 0 0.75 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="底部菜单优化" transform="translate(-202.000000, -145.000000)">
            <g id="VIP" transform="translate(205.000000, 148.000000)">
                <rect id="矩形" x="0" y="0" width="48" height="20"></rect>
                <g id="编组-3" transform="translate(2.661765, 2.000000)">
                    <path d="M35.9299992,2.47960615 C37.759957,2.47960615 39.4166729,3.22134312 40.6158993,4.42056956 C41.8151258,5.61979599 42.5568627,7.27651189 42.5568627,9.10646974 C42.5568627,10.9364276 41.8151258,12.5931435 40.6158993,13.7923699 C39.4166729,14.9915964 37.759957,15.7333333 35.9299992,15.7333333 L35.9299992,15.7333333 L9.41176482,15.7333333 C7.79606117,15.7333333 6.33331604,15.07844 5.27449683,14.0196208 C4.21567761,12.9608016 3.56078431,11.4980565 3.56078431,9.88235283 L3.56078431,9.88235283 L3.56078431,9.10646974 C3.56078431,7.59333511 4.1741033,6.2234488 5.16570595,5.23184615 C6.1573086,4.2402435 7.52719491,3.62692451 9.04032954,3.62692451 C9.37123746,3.62692451 9.70146518,3.65689952 10.0269648,3.71648209 L10.0269648,3.71648209 L12.5370117,4.17594524 L16.0859608,2.47960615 Z" id="矩形备份-3" stroke-opacity="0.1" stroke="#885430" stroke-width="0.533333333" fill="url(#linearGradient-1)"></path>
                    <g id="编组-7" filter="url(#filter-2)">
                        <path d="M0.124795605,3.25689292 L1.0093706,14.0410682 C1.1001619,15.1479378 2.02497628,16 3.13556326,16 L13.8493059,16 C14.962786,16 15.8889899,15.1436312 15.9761008,14.0335638 L16.8216185,3.25900833 C16.8647935,2.70882357 16.4537809,2.22781048 15.9035961,2.18463552 C15.7660922,2.1738451 15.6278434,2.19163753 15.4975526,2.23689277 L13.2541684,3.01611 C12.9413686,3.12475791 12.5950609,3.0713942 12.3294784,2.87362144 L9.07179391,0.447703204 C8.71553773,0.182407913 8.22692512,0.184122751 7.87253986,0.451912114 L4.67291356,2.86969292 C4.40802697,3.06985308 4.06102307,3.12537955 3.74690415,3.01787002 L1.44428815,2.22978265 C0.922147097,2.05107601 0.353997781,2.32948461 0.17529114,2.85162567 C0.130738729,2.98179791 0.113547903,3.1197681 0.124795605,3.25689292 Z" id="路径-6" fill="url(#linearGradient-3)"></path>
                        <path d="M15.7661663,2.18333123 L15.9035961,2.18463551 C16.4537809,2.22781047 16.8647935,2.70882355 16.8216186,3.2590083 L16.8216186,3.2590083 L16.6358569,5.61457279 L12.2478569,2.83405348 L12.2648569,2.82557279 L12.3294785,2.87362145 C12.5618631,3.04667261 12.856053,3.10916059 13.1355209,3.04929306 L13.2541684,3.01611001 L15.4975526,2.23689276 C15.6278435,2.19163753 15.7660923,2.17384509 15.9035961,2.18463551 Z" id="形状结合" fill="#DD9B48"></path>
                        <g id="路径" fill-rule="nonzero">
                            <use fill="#663409" xlink:href="#path-4"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                        </g>
                    </g>
                    <g id="VIP" transform="translate(20.660286, 5.176471)">
                        <rect id="矩形" x="0" y="0" width="16.9411765" height="8.47058824"></rect>
                        <g id="形状结合" fill-rule="nonzero">
                            <use fill="#663309" xlink:href="#path-6"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>