<?xml version="1.0" encoding="UTF-8"?>
<svg width="29px" height="12px" viewBox="0 0 29 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>VIP</title>
    <defs>
        <linearGradient x1="0%" y1="41.4387634%" x2="97.2941016%" y2="57.8256604%" id="linearGradient-1">
            <stop stop-color="#F9D8AC" offset="0%"></stop>
            <stop stop-color="#F0C48B" offset="100%"></stop>
        </linearGradient>
        <path d="M1.85894129,0.330882353 L0.219347335,0.330882353 L1.13308775,7.61029412 L2.84173415,7.61029412 L6.67494744,0.330882353 L4.98717737,0.330882353 L2.40654319,5.07301845 L1.85894129,0.330882353 Z M9.37441605,0.330882353 L7.7123399,0.330882353 L6.40997878,7.61029412 L8.08008428,7.61029412 L9.37441605,0.330882353 Z M14.031441,0.330882353 L10.7940057,0.330882353 L9.50127983,7.61029412 L11.1617501,7.61029412 L11.5744589,5.30265796 L13.1241241,5.30265796 C13.4790215,5.30265796 13.77129,5.2785699 13.9912942,5.2239703 C14.2032692,5.17097656 14.391156,5.09389477 14.5453196,4.9830897 C14.7730139,4.82610928 14.9530394,4.60941201 15.0656217,4.35680013 C15.1940914,4.09183147 15.2920495,3.7562045 15.3675254,3.35473683 L15.5313242,2.4859608 C15.6180412,2.03631701 15.6630056,1.67499611 15.6630056,1.41323919 C15.6630056,1.13381769 15.5907414,0.883301865 15.4108839,0.703444349 C15.2507491,0.535250918 15.0374729,0.42747837 14.8070765,0.398328921 C14.6376886,0.364806715 14.4658,0.345469253 14.2931979,0.340517577 C14.2060242,0.335442706 14.118752,0.332230232 14.031441,0.330882353 Z M11.8233688,3.90555047 L12.205566,1.72798984 L13.4902626,1.72798984 C14.0555291,1.72798984 13.957571,1.93032954 13.8837009,2.41048488 C13.7809252,3.01108051 13.77129,3.90555047 12.9779898,3.90555047 L11.8233688,3.90555047 Z" id="path-2"></path>
        <filter x="-3.2%" y="-6.9%" width="106.5%" height="113.7%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.247058824   0 0 0 0 0.129411765   0 0 0 0 0.0274509804  0 0 0 0.75 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g  stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g  transform="translate(-469.000000, -162.000000)">
            <g  transform="translate(469.000000, 162.000000)">
                <rect  stroke-opacity="0.233801355" stroke="#885430" stroke-width="0.5" fill="url(#linearGradient-1)" x="0.25" y="0.25" width="28.5" height="11.5" rx="5.75"></rect>
                <g  transform="translate(6.558824, 2.029412)">
                    <rect  x="0" y="0" width="15.8823529" height="7.94117647"></rect>
                    <g  fill-rule="nonzero">
                        <use fill="#663309" xlink:href="#path-2"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
