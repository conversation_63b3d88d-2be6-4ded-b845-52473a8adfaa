<?xml version="1.0" encoding="UTF-8"?>
<svg width="134px" height="104px" viewBox="0 0 134 104" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="49.9684134%" y1="-2.3164557%" x2="49.9684134%" y2="101.075949%" id="linearGradient-1">
            <stop stop-color="#FDFEFF" offset="0%"></stop>
            <stop stop-color="#ECF0F5" offset="99.64%"></stop>
        </linearGradient>
        <path d="M56.9093644,7.58931929 C65.2019556,9.50733358 71.3787138,16.9311771 71.3787138,25.8066942 L71.3787138,28.1835276 C71.3787138,48.2286574 86.4678451,61.3463708 86.4678451,61.3463708 L18.773223,61.3463708 C18.773223,61.3463708 33.9654005,48.2286574 33.9654005,28.1835276 L33.9654005,25.8066942 C33.9654005,16.9311771 40.1421587,9.50733358 48.4347499,7.58931929 L48.4347499,6.7995487 C48.4347499,4.42271531 50.2948477,2.55735239 52.6724332,2.55735239 C55.0492666,2.55735239 56.9093644,4.42271531 56.9093644,6.7995487 L56.9093644,7.58931929 Z" id="path-2"></path>
        <filter x="-41.4%" y="-34.0%" width="182.7%" height="195.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="8" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.396078431   0 0 0 0 0.478431373   0 0 0 0 0.576470588  0 0 0 0.27 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="1.1.0团队版" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="交互细节" transform="translate(-717.000000, -222.000000)">
            <g id="编组-4备份" transform="translate(574.000000, 110.000000)">
                <g id="暂无消息通知" transform="translate(143.000000, 112.000000)">
                    <rect id="矩形" x="0" y="0" width="134" height="104"></rect>
                    <g id="56" transform="translate(14.000000, 13.790147)" fill-rule="nonzero">
                        <path d="M52.8770214,76.4197067 C73.9375705,76.4197067 91.0116585,59.3456187 91.0116585,38.2098533 C91.0116585,17.074088 73.8623543,0 52.8770214,0 C31.8164724,0 14.7423844,17.074088 14.7423844,38.2098533 C14.7423844,59.3456187 31.8164724,76.4197067 52.8770214,76.4197067 Z" id="Path" fill="#EAEEF9"></path>
                        <path d="M93.3809703,62.865739 C95.0838661,62.865739 96.4648364,61.4817601 96.4648364,59.7818729 C96.4648364,58.0819857 95.0838661,56.6980068 93.3809703,56.6980068 C91.6780745,56.6980068 90.2971042,58.0819857 90.2971042,59.7818729 C90.2971042,61.4817601 91.6780745,62.865739 93.3809703,62.865739 Z" id="Path" fill="#EAEEF9"></path>
                        <path d="M97.8939451,50.8311395 C99.0567883,50.8311395 100,49.8909364 100,48.7250846 C100,47.5592328 99.0567883,46.6190297 97.8939451,46.6190297 C96.7311019,46.6190297 95.7878902,47.5592328 95.7878902,48.7250846 C95.7878902,49.8909364 96.7311019,50.8311395 97.8939451,50.8311395 Z" id="Path" fill="#EAEEF9"></path>
                        <path d="M16.773223,13.0876269 C17.9360662,13.0876269 18.8792779,12.1474238 18.8792779,10.981572 C18.8792779,9.82324182 17.9360662,8.87551711 16.773223,8.87551711 C15.6103798,8.87551711 14.6671681,9.82324182 14.6671681,10.981572 C14.6671681,12.1474238 15.6103798,13.0876269 16.773223,13.0876269 Z" id="Path" fill="#EAEEF9"></path>
                        <path d="M3.91124483,53.7044002 C6.07145543,53.7044002 7.82248966,51.9593832 7.82248966,49.7931553 C7.82248966,47.634449 6.07145543,45.8819105 3.91124483,45.8819105 C1.75103422,45.8819105 0,47.634449 0,49.7931553 C0,51.9593832 1.75103422,53.7044002 3.91124483,53.7044002 Z" id="Path" fill="#EAEEF9"></path>
                        <path d="M57.6615269,52.064686 L49.1869124,52.064686 C47.2230162,52.064686 45.5697631,53.8773975 45.5697631,56.0285822 L45.5697631,60.6694246 C45.5697631,65.4230914 49.0838661,69.2741632 53.4245957,69.2741632 C57.7653253,69.2741632 61.2786762,65.4230914 61.2786762,60.6694246 L61.2786762,56.0285822 C61.2786762,53.7645731 59.6254231,52.064686 57.6615269,52.064686 Z" id="Path" fill="#FFFFFF"></path>
                        <g id="Path">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
