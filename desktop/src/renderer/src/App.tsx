import type { ReactElement } from 'react'
import { ThemeProvider } from '@/components/theme-provider'
import { RouterProvider } from 'react-router-dom'
import { router } from '@renderer/router/router'
import { AlertBaseWrapper } from '@/components/alertBase/alertBaseWrapper'
import { QueryClientProvider } from '@/lib/query'

export default function App(): ReactElement {
  return (
    <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
      <QueryClientProvider>
        <RouterProvider router={router} />
        <AlertBaseWrapper />
      </QueryClientProvider>
    </ThemeProvider>
  )
}
