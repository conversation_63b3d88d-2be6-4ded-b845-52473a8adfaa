(()=>{var e={2:(e,t,i)=>{const r=i(94);const n={};for(const e of Object.keys(r)){n[r[e]]=e}const o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=o;for(const e of Object.keys(o)){if(!("channels"in o[e])){throw new Error("missing channels property: "+e)}if(!("labels"in o[e])){throw new Error("missing channel labels property: "+e)}if(o[e].labels.length!==o[e].channels){throw new Error("channel and label counts mismatch: "+e)}const{channels:t,labels:i}=o[e];delete o[e].channels;delete o[e].labels;Object.defineProperty(o[e],"channels",{value:t});Object.defineProperty(o[e],"labels",{value:i})}o.rgb.hsl=function(e){const t=e[0]/255;const i=e[1]/255;const r=e[2]/255;const n=Math.min(t,i,r);const o=Math.max(t,i,r);const s=o-n;let a;let l;if(o===n){a=0}else if(t===o){a=(i-r)/s}else if(i===o){a=2+(r-t)/s}else if(r===o){a=4+(t-i)/s}a=Math.min(a*60,360);if(a<0){a+=360}const c=(n+o)/2;if(o===n){l=0}else if(c<=.5){l=s/(o+n)}else{l=s/(2-o-n)}return[a,l*100,c*100]};o.rgb.hsv=function(e){let t;let i;let r;let n;let o;const s=e[0]/255;const a=e[1]/255;const l=e[2]/255;const c=Math.max(s,a,l);const h=c-Math.min(s,a,l);const diffc=function(e){return(c-e)/6/h+1/2};if(h===0){n=0;o=0}else{o=h/c;t=diffc(s);i=diffc(a);r=diffc(l);if(s===c){n=r-i}else if(a===c){n=1/3+t-r}else if(l===c){n=2/3+i-t}if(n<0){n+=1}else if(n>1){n-=1}}return[n*360,o*100,c*100]};o.rgb.hwb=function(e){const t=e[0];const i=e[1];let r=e[2];const n=o.rgb.hsl(e)[0];const s=1/255*Math.min(t,Math.min(i,r));r=1-1/255*Math.max(t,Math.max(i,r));return[n,s*100,r*100]};o.rgb.cmyk=function(e){const t=e[0]/255;const i=e[1]/255;const r=e[2]/255;const n=Math.min(1-t,1-i,1-r);const o=(1-t-n)/(1-n)||0;const s=(1-i-n)/(1-n)||0;const a=(1-r-n)/(1-n)||0;return[o*100,s*100,a*100,n*100]};function comparativeDistance(e,t){return(e[0]-t[0])**2+(e[1]-t[1])**2+(e[2]-t[2])**2}o.rgb.keyword=function(e){const t=n[e];if(t){return t}let i=Infinity;let o;for(const t of Object.keys(r)){const n=r[t];const s=comparativeDistance(e,n);if(s<i){i=s;o=t}}return o};o.keyword.rgb=function(e){return r[e]};o.rgb.xyz=function(e){let t=e[0]/255;let i=e[1]/255;let r=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92;i=i>.04045?((i+.055)/1.055)**2.4:i/12.92;r=r>.04045?((r+.055)/1.055)**2.4:r/12.92;const n=t*.4124+i*.3576+r*.1805;const o=t*.2126+i*.7152+r*.0722;const s=t*.0193+i*.1192+r*.9505;return[n*100,o*100,s*100]};o.rgb.lab=function(e){const t=o.rgb.xyz(e);let i=t[0];let r=t[1];let n=t[2];i/=95.047;r/=100;n/=108.883;i=i>.008856?i**(1/3):7.787*i+16/116;r=r>.008856?r**(1/3):7.787*r+16/116;n=n>.008856?n**(1/3):7.787*n+16/116;const s=116*r-16;const a=500*(i-r);const l=200*(r-n);return[s,a,l]};o.hsl.rgb=function(e){const t=e[0]/360;const i=e[1]/100;const r=e[2]/100;let n;let o;let s;if(i===0){s=r*255;return[s,s,s]}if(r<.5){n=r*(1+i)}else{n=r+i-r*i}const a=2*r-n;const l=[0,0,0];for(let e=0;e<3;e++){o=t+1/3*-(e-1);if(o<0){o++}if(o>1){o--}if(6*o<1){s=a+(n-a)*6*o}else if(2*o<1){s=n}else if(3*o<2){s=a+(n-a)*(2/3-o)*6}else{s=a}l[e]=s*255}return l};o.hsl.hsv=function(e){const t=e[0];let i=e[1]/100;let r=e[2]/100;let n=i;const o=Math.max(r,.01);r*=2;i*=r<=1?r:2-r;n*=o<=1?o:2-o;const s=(r+i)/2;const a=r===0?2*n/(o+n):2*i/(r+i);return[t,a*100,s*100]};o.hsv.rgb=function(e){const t=e[0]/60;const i=e[1]/100;let r=e[2]/100;const n=Math.floor(t)%6;const o=t-Math.floor(t);const s=255*r*(1-i);const a=255*r*(1-i*o);const l=255*r*(1-i*(1-o));r*=255;switch(n){case 0:return[r,l,s];case 1:return[a,r,s];case 2:return[s,r,l];case 3:return[s,a,r];case 4:return[l,s,r];case 5:return[r,s,a]}};o.hsv.hsl=function(e){const t=e[0];const i=e[1]/100;const r=e[2]/100;const n=Math.max(r,.01);let o;let s;s=(2-i)*r;const a=(2-i)*n;o=i*n;o/=a<=1?a:2-a;o=o||0;s/=2;return[t,o*100,s*100]};o.hwb.rgb=function(e){const t=e[0]/360;let i=e[1]/100;let r=e[2]/100;const n=i+r;let o;if(n>1){i/=n;r/=n}const s=Math.floor(6*t);const a=1-r;o=6*t-s;if((s&1)!==0){o=1-o}const l=i+o*(a-i);let c;let h;let f;switch(s){default:case 6:case 0:c=a;h=l;f=i;break;case 1:c=l;h=a;f=i;break;case 2:c=i;h=a;f=l;break;case 3:c=i;h=l;f=a;break;case 4:c=l;h=i;f=a;break;case 5:c=a;h=i;f=l;break}return[c*255,h*255,f*255]};o.cmyk.rgb=function(e){const t=e[0]/100;const i=e[1]/100;const r=e[2]/100;const n=e[3]/100;const o=1-Math.min(1,t*(1-n)+n);const s=1-Math.min(1,i*(1-n)+n);const a=1-Math.min(1,r*(1-n)+n);return[o*255,s*255,a*255]};o.xyz.rgb=function(e){const t=e[0]/100;const i=e[1]/100;const r=e[2]/100;let n;let o;let s;n=t*3.2406+i*-1.5372+r*-.4986;o=t*-.9689+i*1.8758+r*.0415;s=t*.0557+i*-.204+r*1.057;n=n>.0031308?1.055*n**(1/2.4)-.055:n*12.92;o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92;s=s>.0031308?1.055*s**(1/2.4)-.055:s*12.92;n=Math.min(Math.max(0,n),1);o=Math.min(Math.max(0,o),1);s=Math.min(Math.max(0,s),1);return[n*255,o*255,s*255]};o.xyz.lab=function(e){let t=e[0];let i=e[1];let r=e[2];t/=95.047;i/=100;r/=108.883;t=t>.008856?t**(1/3):7.787*t+16/116;i=i>.008856?i**(1/3):7.787*i+16/116;r=r>.008856?r**(1/3):7.787*r+16/116;const n=116*i-16;const o=500*(t-i);const s=200*(i-r);return[n,o,s]};o.lab.xyz=function(e){const t=e[0];const i=e[1];const r=e[2];let n;let o;let s;o=(t+16)/116;n=i/500+o;s=o-r/200;const a=o**3;const l=n**3;const c=s**3;o=a>.008856?a:(o-16/116)/7.787;n=l>.008856?l:(n-16/116)/7.787;s=c>.008856?c:(s-16/116)/7.787;n*=95.047;o*=100;s*=108.883;return[n,o,s]};o.lab.lch=function(e){const t=e[0];const i=e[1];const r=e[2];let n;const o=Math.atan2(r,i);n=o*360/2/Math.PI;if(n<0){n+=360}const s=Math.sqrt(i*i+r*r);return[t,s,n]};o.lch.lab=function(e){const t=e[0];const i=e[1];const r=e[2];const n=r/360*2*Math.PI;const o=i*Math.cos(n);const s=i*Math.sin(n);return[t,o,s]};o.rgb.ansi16=function(e,t=null){const[i,r,n]=e;let s=t===null?o.rgb.hsv(e)[2]:t;s=Math.round(s/50);if(s===0){return 30}let a=30+(Math.round(n/255)<<2|Math.round(r/255)<<1|Math.round(i/255));if(s===2){a+=60}return a};o.hsv.ansi16=function(e){return o.rgb.ansi16(o.hsv.rgb(e),e[2])};o.rgb.ansi256=function(e){const t=e[0];const i=e[1];const r=e[2];if(t===i&&i===r){if(t<8){return 16}if(t>248){return 231}return Math.round((t-8)/247*24)+232}const n=16+36*Math.round(t/255*5)+6*Math.round(i/255*5)+Math.round(r/255*5);return n};o.ansi16.rgb=function(e){let t=e%10;if(t===0||t===7){if(e>50){t+=3.5}t=t/10.5*255;return[t,t,t]}const i=(~~(e>50)+1)*.5;const r=(t&1)*i*255;const n=(t>>1&1)*i*255;const o=(t>>2&1)*i*255;return[r,n,o]};o.ansi256.rgb=function(e){if(e>=232){const t=(e-232)*10+8;return[t,t,t]}e-=16;let t;const i=Math.floor(e/36)/5*255;const r=Math.floor((t=e%36)/6)/5*255;const n=t%6/5*255;return[i,r,n]};o.rgb.hex=function(e){const t=((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255);const i=t.toString(16).toUpperCase();return"000000".substring(i.length)+i};o.hex.rgb=function(e){const t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t){return[0,0,0]}let i=t[0];if(t[0].length===3){i=i.split("").map((e=>e+e)).join("")}const r=parseInt(i,16);const n=r>>16&255;const o=r>>8&255;const s=r&255;return[n,o,s]};o.rgb.hcg=function(e){const t=e[0]/255;const i=e[1]/255;const r=e[2]/255;const n=Math.max(Math.max(t,i),r);const o=Math.min(Math.min(t,i),r);const s=n-o;let a;let l;if(s<1){a=o/(1-s)}else{a=0}if(s<=0){l=0}else if(n===t){l=(i-r)/s%6}else if(n===i){l=2+(r-t)/s}else{l=4+(t-i)/s}l/=6;l%=1;return[l*360,s*100,a*100]};o.hsl.hcg=function(e){const t=e[1]/100;const i=e[2]/100;const r=i<.5?2*t*i:2*t*(1-i);let n=0;if(r<1){n=(i-.5*r)/(1-r)}return[e[0],r*100,n*100]};o.hsv.hcg=function(e){const t=e[1]/100;const i=e[2]/100;const r=t*i;let n=0;if(r<1){n=(i-r)/(1-r)}return[e[0],r*100,n*100]};o.hcg.rgb=function(e){const t=e[0]/360;const i=e[1]/100;const r=e[2]/100;if(i===0){return[r*255,r*255,r*255]}const n=[0,0,0];const o=t%1*6;const s=o%1;const a=1-s;let l=0;switch(Math.floor(o)){case 0:n[0]=1;n[1]=s;n[2]=0;break;case 1:n[0]=a;n[1]=1;n[2]=0;break;case 2:n[0]=0;n[1]=1;n[2]=s;break;case 3:n[0]=0;n[1]=a;n[2]=1;break;case 4:n[0]=s;n[1]=0;n[2]=1;break;default:n[0]=1;n[1]=0;n[2]=a}l=(1-i)*r;return[(i*n[0]+l)*255,(i*n[1]+l)*255,(i*n[2]+l)*255]};o.hcg.hsv=function(e){const t=e[1]/100;const i=e[2]/100;const r=t+i*(1-t);let n=0;if(r>0){n=t/r}return[e[0],n*100,r*100]};o.hcg.hsl=function(e){const t=e[1]/100;const i=e[2]/100;const r=i*(1-t)+.5*t;let n=0;if(r>0&&r<.5){n=t/(2*r)}else if(r>=.5&&r<1){n=t/(2*(1-r))}return[e[0],n*100,r*100]};o.hcg.hwb=function(e){const t=e[1]/100;const i=e[2]/100;const r=t+i*(1-t);return[e[0],(r-t)*100,(1-r)*100]};o.hwb.hcg=function(e){const t=e[1]/100;const i=e[2]/100;const r=1-i;const n=r-t;let o=0;if(n<1){o=(r-n)/(1-n)}return[e[0],n*100,o*100]};o.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};o.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};o.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};o.gray.hsl=function(e){return[0,0,e[0]]};o.gray.hsv=o.gray.hsl;o.gray.hwb=function(e){return[0,100,e[0]]};o.gray.cmyk=function(e){return[0,0,0,e[0]]};o.gray.lab=function(e){return[e[0],0,0]};o.gray.hex=function(e){const t=Math.round(e[0]/100*255)&255;const i=(t<<16)+(t<<8)+t;const r=i.toString(16).toUpperCase();return"000000".substring(r.length)+r};o.rgb.gray=function(e){const t=(e[0]+e[1]+e[2])/3;return[t/255*100]}},291:(e,t,i)=>{const r=i(2);const n=i(150);const o={};const s=Object.keys(r);function wrapRaw(e){const wrappedFn=function(...t){const i=t[0];if(i===undefined||i===null){return i}if(i.length>1){t=i}return e(t)};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}function wrapRounded(e){const wrappedFn=function(...t){const i=t[0];if(i===undefined||i===null){return i}if(i.length>1){t=i}const r=e(t);if(typeof r==="object"){for(let e=r.length,t=0;t<e;t++){r[t]=Math.round(r[t])}}return r};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}s.forEach((e=>{o[e]={};Object.defineProperty(o[e],"channels",{value:r[e].channels});Object.defineProperty(o[e],"labels",{value:r[e].labels});const t=n(e);const i=Object.keys(t);i.forEach((i=>{const r=t[i];o[e][i]=wrapRounded(r);o[e][i].raw=wrapRaw(r)}))}));e.exports=o},150:(e,t,i)=>{const r=i(2);function buildGraph(){const e={};const t=Object.keys(r);for(let i=t.length,r=0;r<i;r++){e[t[r]]={distance:-1,parent:null}}return e}function deriveBFS(e){const t=buildGraph();const i=[e];t[e].distance=0;while(i.length){const e=i.pop();const n=Object.keys(r[e]);for(let r=n.length,o=0;o<r;o++){const r=n[o];const s=t[r];if(s.distance===-1){s.distance=t[e].distance+1;s.parent=e;i.unshift(r)}}}return t}function link(e,t){return function(i){return t(e(i))}}function wrapConversion(e,t){const i=[t[e].parent,e];let n=r[t[e].parent][e];let o=t[e].parent;while(t[o].parent){i.unshift(t[o].parent);n=link(r[t[o].parent][o],n);o=t[o].parent}n.conversion=i;return n}e.exports=function(e){const t=deriveBFS(e);const i={};const r=Object.keys(t);for(let e=r.length,n=0;n<e;n++){const e=r[n];const o=t[e];if(o.parent===null){continue}i[e]=wrapConversion(e,t)}return i}},94:e=>{"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},993:(e,t,i)=>{var r=i(94);var n=i(110);var o=Object.hasOwnProperty;var s=Object.create(null);for(var a in r){if(o.call(r,a)){s[r[a]]=a}}var l=e.exports={to:{},get:{}};l.get=function(e){var t=e.substring(0,3).toLowerCase();var i;var r;switch(t){case"hsl":i=l.get.hsl(e);r="hsl";break;case"hwb":i=l.get.hwb(e);r="hwb";break;default:i=l.get.rgb(e);r="rgb";break}if(!i){return null}return{model:r,value:i}};l.get.rgb=function(e){if(!e){return null}var t=/^#([a-f0-9]{3,4})$/i;var i=/^#([a-f0-9]{6})([a-f0-9]{2})?$/i;var n=/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/;var s=/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/;var a=/^(\w+)$/;var l=[0,0,0,1];var c;var h;var f;if(c=e.match(i)){f=c[2];c=c[1];for(h=0;h<3;h++){var u=h*2;l[h]=parseInt(c.slice(u,u+2),16)}if(f){l[3]=parseInt(f,16)/255}}else if(c=e.match(t)){c=c[1];f=c[3];for(h=0;h<3;h++){l[h]=parseInt(c[h]+c[h],16)}if(f){l[3]=parseInt(f+f,16)/255}}else if(c=e.match(n)){for(h=0;h<3;h++){l[h]=parseInt(c[h+1],0)}if(c[4]){if(c[5]){l[3]=parseFloat(c[4])*.01}else{l[3]=parseFloat(c[4])}}}else if(c=e.match(s)){for(h=0;h<3;h++){l[h]=Math.round(parseFloat(c[h+1])*2.55)}if(c[4]){if(c[5]){l[3]=parseFloat(c[4])*.01}else{l[3]=parseFloat(c[4])}}}else if(c=e.match(a)){if(c[1]==="transparent"){return[0,0,0,0]}if(!o.call(r,c[1])){return null}l=r[c[1]];l[3]=1;return l}else{return null}for(h=0;h<3;h++){l[h]=clamp(l[h],0,255)}l[3]=clamp(l[3],0,1);return l};l.get.hsl=function(e){if(!e){return null}var t=/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/;var i=e.match(t);if(i){var r=parseFloat(i[4]);var n=(parseFloat(i[1])%360+360)%360;var o=clamp(parseFloat(i[2]),0,100);var s=clamp(parseFloat(i[3]),0,100);var a=clamp(isNaN(r)?1:r,0,1);return[n,o,s,a]}return null};l.get.hwb=function(e){if(!e){return null}var t=/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/;var i=e.match(t);if(i){var r=parseFloat(i[4]);var n=(parseFloat(i[1])%360+360)%360;var o=clamp(parseFloat(i[2]),0,100);var s=clamp(parseFloat(i[3]),0,100);var a=clamp(isNaN(r)?1:r,0,1);return[n,o,s,a]}return null};l.to.hex=function(){var e=n(arguments);return"#"+hexDouble(e[0])+hexDouble(e[1])+hexDouble(e[2])+(e[3]<1?hexDouble(Math.round(e[3]*255)):"")};l.to.rgb=function(){var e=n(arguments);return e.length<4||e[3]===1?"rgb("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+")":"rgba("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+", "+e[3]+")"};l.to.rgb.percent=function(){var e=n(arguments);var t=Math.round(e[0]/255*100);var i=Math.round(e[1]/255*100);var r=Math.round(e[2]/255*100);return e.length<4||e[3]===1?"rgb("+t+"%, "+i+"%, "+r+"%)":"rgba("+t+"%, "+i+"%, "+r+"%, "+e[3]+")"};l.to.hsl=function(){var e=n(arguments);return e.length<4||e[3]===1?"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)":"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+e[3]+")"};l.to.hwb=function(){var e=n(arguments);var t="";if(e.length>=4&&e[3]!==1){t=", "+e[3]}return"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+t+")"};l.to.keyword=function(e){return s[e.slice(0,3)]};function clamp(e,t,i){return Math.min(Math.max(t,e),i)}function hexDouble(e){var t=Math.round(e).toString(16).toUpperCase();return t.length<2?"0"+t:t}},665:(e,t,i)=>{const r=i(993);const n=i(291);const o=["keyword","gray","hex"];const s={};for(const e of Object.keys(n)){s[[...n[e].labels].sort().join("")]=e}const a={};function Color(e,t){if(!(this instanceof Color)){return new Color(e,t)}if(t&&t in o){t=null}if(t&&!(t in n)){throw new Error("Unknown model: "+t)}let i;let l;if(e==null){this.model="rgb";this.color=[0,0,0];this.valpha=1}else if(e instanceof Color){this.model=e.model;this.color=[...e.color];this.valpha=e.valpha}else if(typeof e==="string"){const t=r.get(e);if(t===null){throw new Error("Unable to parse color from string: "+e)}this.model=t.model;l=n[this.model].channels;this.color=t.value.slice(0,l);this.valpha=typeof t.value[l]==="number"?t.value[l]:1}else if(e.length>0){this.model=t||"rgb";l=n[this.model].channels;const i=Array.prototype.slice.call(e,0,l);this.color=zeroArray(i,l);this.valpha=typeof e[l]==="number"?e[l]:1}else if(typeof e==="number"){this.model="rgb";this.color=[e>>16&255,e>>8&255,e&255];this.valpha=1}else{this.valpha=1;const t=Object.keys(e);if("alpha"in e){t.splice(t.indexOf("alpha"),1);this.valpha=typeof e.alpha==="number"?e.alpha:0}const r=t.sort().join("");if(!(r in s)){throw new Error("Unable to parse color from object: "+JSON.stringify(e))}this.model=s[r];const{labels:o}=n[this.model];const a=[];for(i=0;i<o.length;i++){a.push(e[o[i]])}this.color=zeroArray(a)}if(a[this.model]){l=n[this.model].channels;for(i=0;i<l;i++){const e=a[this.model][i];if(e){this.color[i]=e(this.color[i])}}}this.valpha=Math.max(0,Math.min(1,this.valpha));if(Object.freeze){Object.freeze(this)}}Color.prototype={toString(){return this.string()},toJSON(){return this[this.model]()},string(e){let t=this.model in r.to?this:this.rgb();t=t.round(typeof e==="number"?e:1);const i=t.valpha===1?t.color:[...t.color,this.valpha];return r.to[t.model](i)},percentString(e){const t=this.rgb().round(typeof e==="number"?e:1);const i=t.valpha===1?t.color:[...t.color,this.valpha];return r.to.rgb.percent(i)},array(){return this.valpha===1?[...this.color]:[...this.color,this.valpha]},object(){const e={};const{channels:t}=n[this.model];const{labels:i}=n[this.model];for(let r=0;r<t;r++){e[i[r]]=this.color[r]}if(this.valpha!==1){e.alpha=this.valpha}return e},unitArray(){const e=this.rgb().color;e[0]/=255;e[1]/=255;e[2]/=255;if(this.valpha!==1){e.push(this.valpha)}return e},unitObject(){const e=this.rgb().object();e.r/=255;e.g/=255;e.b/=255;if(this.valpha!==1){e.alpha=this.valpha}return e},round(e){e=Math.max(e||0,0);return new Color([...this.color.map(roundToPlace(e)),this.valpha],this.model)},alpha(e){if(e!==undefined){return new Color([...this.color,Math.max(0,Math.min(1,e))],this.model)}return this.valpha},red:getset("rgb",0,maxfn(255)),green:getset("rgb",1,maxfn(255)),blue:getset("rgb",2,maxfn(255)),hue:getset(["hsl","hsv","hsl","hwb","hcg"],0,(e=>(e%360+360)%360)),saturationl:getset("hsl",1,maxfn(100)),lightness:getset("hsl",2,maxfn(100)),saturationv:getset("hsv",1,maxfn(100)),value:getset("hsv",2,maxfn(100)),chroma:getset("hcg",1,maxfn(100)),gray:getset("hcg",2,maxfn(100)),white:getset("hwb",1,maxfn(100)),wblack:getset("hwb",2,maxfn(100)),cyan:getset("cmyk",0,maxfn(100)),magenta:getset("cmyk",1,maxfn(100)),yellow:getset("cmyk",2,maxfn(100)),black:getset("cmyk",3,maxfn(100)),x:getset("xyz",0,maxfn(95.047)),y:getset("xyz",1,maxfn(100)),z:getset("xyz",2,maxfn(108.833)),l:getset("lab",0,maxfn(100)),a:getset("lab",1),b:getset("lab",2),keyword(e){if(e!==undefined){return new Color(e)}return n[this.model].keyword(this.color)},hex(e){if(e!==undefined){return new Color(e)}return r.to.hex(this.rgb().round().color)},hexa(e){if(e!==undefined){return new Color(e)}const t=this.rgb().round().color;let i=Math.round(this.valpha*255).toString(16).toUpperCase();if(i.length===1){i="0"+i}return r.to.hex(t)+i},rgbNumber(){const e=this.rgb().color;return(e[0]&255)<<16|(e[1]&255)<<8|e[2]&255},luminosity(){const e=this.rgb().color;const t=[];for(const[i,r]of e.entries()){const e=r/255;t[i]=e<=.04045?e/12.92:((e+.055)/1.055)**2.4}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast(e){const t=this.luminosity();const i=e.luminosity();if(t>i){return(t+.05)/(i+.05)}return(i+.05)/(t+.05)},level(e){const t=this.contrast(e);if(t>=7){return"AAA"}return t>=4.5?"AA":""},isDark(){const e=this.rgb().color;const t=(e[0]*2126+e[1]*7152+e[2]*722)/1e4;return t<128},isLight(){return!this.isDark()},negate(){const e=this.rgb();for(let t=0;t<3;t++){e.color[t]=255-e.color[t]}return e},lighten(e){const t=this.hsl();t.color[2]+=t.color[2]*e;return t},darken(e){const t=this.hsl();t.color[2]-=t.color[2]*e;return t},saturate(e){const t=this.hsl();t.color[1]+=t.color[1]*e;return t},desaturate(e){const t=this.hsl();t.color[1]-=t.color[1]*e;return t},whiten(e){const t=this.hwb();t.color[1]+=t.color[1]*e;return t},blacken(e){const t=this.hwb();t.color[2]+=t.color[2]*e;return t},grayscale(){const e=this.rgb().color;const t=e[0]*.3+e[1]*.59+e[2]*.11;return Color.rgb(t,t,t)},fade(e){return this.alpha(this.valpha-this.valpha*e)},opaquer(e){return this.alpha(this.valpha+this.valpha*e)},rotate(e){const t=this.hsl();let i=t.color[0];i=(i+e)%360;i=i<0?360+i:i;t.color[0]=i;return t},mix(e,t){if(!e||!e.rgb){throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof e)}const i=e.rgb();const r=this.rgb();const n=t===undefined?.5:t;const o=2*n-1;const s=i.alpha()-r.alpha();const a=((o*s===-1?o:(o+s)/(1+o*s))+1)/2;const l=1-a;return Color.rgb(a*i.red()+l*r.red(),a*i.green()+l*r.green(),a*i.blue()+l*r.blue(),i.alpha()*n+r.alpha()*(1-n))}};for(const e of Object.keys(n)){if(o.includes(e)){continue}const{channels:t}=n[e];Color.prototype[e]=function(...t){if(this.model===e){return new Color(this)}if(t.length>0){return new Color(t,e)}return new Color([...assertArray(n[this.model][e].raw(this.color)),this.valpha],e)};Color[e]=function(...i){let r=i[0];if(typeof r==="number"){r=zeroArray(i,t)}return new Color(r,e)}}function roundTo(e,t){return Number(e.toFixed(t))}function roundToPlace(e){return function(t){return roundTo(t,e)}}function getset(e,t,i){e=Array.isArray(e)?e:[e];for(const r of e){(a[r]||(a[r]=[]))[t]=i}e=e[0];return function(r){let n;if(r!==undefined){if(i){r=i(r)}n=this[e]();n.color[t]=r;return n}n=this[e]().color[t];if(i){n=i(n)}return n}}function maxfn(e){return function(t){return Math.max(0,Math.min(e,t))}}function assertArray(e){return Array.isArray(e)?e:[e]}function zeroArray(e,t){for(let i=0;i<t;i++){if(typeof e[i]!=="number"){e[i]=0}}return e}e.exports=Color},513:(e,t,i)=>{"use strict";const r=i(317);const{isLinux:n,getReport:o}=i(658);const{LDD_PATH:s,readFile:a,readFileSync:l}=i(520);let c;let h;const f="getconf GNU_LIBC_VERSION 2>&1 || true; ldd --version 2>&1 || true";let u="";const safeCommand=()=>{if(!u){return new Promise((e=>{r.exec(f,((t,i)=>{u=t?" ":i;e(u)}))}))}return u};const safeCommandSync=()=>{if(!u){try{u=r.execSync(f,{encoding:"utf8"})}catch(e){u=" "}}return u};const p="glibc";const d=/LIBC[a-z0-9 \-).]*?(\d+\.\d+)/i;const m="musl";const isFileMusl=e=>e.includes("libc.musl-")||e.includes("ld-musl-");const familyFromReport=()=>{const e=o();if(e.header&&e.header.glibcVersionRuntime){return p}if(Array.isArray(e.sharedObjects)){if(e.sharedObjects.some(isFileMusl)){return m}}return null};const familyFromCommand=e=>{const[t,i]=e.split(/[\r\n]+/);if(t&&t.includes(p)){return p}if(i&&i.includes(m)){return m}return null};const getFamilyFromLddContent=e=>{if(e.includes("musl")){return m}if(e.includes("GNU C Library")){return p}return null};const familyFromFilesystem=async()=>{if(c!==undefined){return c}c=null;try{const e=await a(s);c=getFamilyFromLddContent(e)}catch(e){}return c};const familyFromFilesystemSync=()=>{if(c!==undefined){return c}c=null;try{const e=l(s);c=getFamilyFromLddContent(e)}catch(e){}return c};const family=async()=>{let e=null;if(n()){e=await familyFromFilesystem();if(!e){e=familyFromReport()}if(!e){const t=await safeCommand();e=familyFromCommand(t)}}return e};const familySync=()=>{let e=null;if(n()){e=familyFromFilesystemSync();if(!e){e=familyFromReport()}if(!e){const t=safeCommandSync();e=familyFromCommand(t)}}return e};const isNonGlibcLinux=async()=>n()&&await family()!==p;const isNonGlibcLinuxSync=()=>n()&&familySync()!==p;const versionFromFilesystem=async()=>{if(h!==undefined){return h}h=null;try{const e=await a(s);const t=e.match(d);if(t){h=t[1]}}catch(e){}return h};const versionFromFilesystemSync=()=>{if(h!==undefined){return h}h=null;try{const e=l(s);const t=e.match(d);if(t){h=t[1]}}catch(e){}return h};const versionFromReport=()=>{const e=o();if(e.header&&e.header.glibcVersionRuntime){return e.header.glibcVersionRuntime}return null};const versionSuffix=e=>e.trim().split(/\s+/)[1];const versionFromCommand=e=>{const[t,i,r]=e.split(/[\r\n]+/);if(t&&t.includes(p)){return versionSuffix(t)}if(i&&r&&i.includes(m)){return versionSuffix(r)}return null};const version=async()=>{let e=null;if(n()){e=await versionFromFilesystem();if(!e){e=versionFromReport()}if(!e){const t=await safeCommand();e=versionFromCommand(t)}}return e};const versionSync=()=>{let e=null;if(n()){e=versionFromFilesystemSync();if(!e){e=versionFromReport()}if(!e){const t=safeCommandSync();e=versionFromCommand(t)}}return e};e.exports={GLIBC:p,MUSL:m,family:family,familySync:familySync,isNonGlibcLinux:isNonGlibcLinux,isNonGlibcLinuxSync:isNonGlibcLinuxSync,version:version,versionSync:versionSync}},520:(e,t,i)=>{"use strict";const r=i(896);const n="/usr/bin/ldd";const readFileSync=e=>r.readFileSync(e,"utf-8");const readFile=e=>new Promise(((t,i)=>{r.readFile(e,"utf-8",((e,r)=>{if(e){i(e)}else{t(r)}}))}));e.exports={LDD_PATH:n,readFileSync:readFileSync,readFile:readFile}},658:e=>{"use strict";const isLinux=()=>process.platform==="linux";let t=null;const getReport=()=>{if(!t){if(isLinux()&&process.report){const e=process.report.excludeNetwork;process.report.excludeNetwork=true;t=process.report.getReport();process.report.excludeNetwork=e}else{t={}}}return t};e.exports={isLinux:isLinux,getReport:getReport}},199:e=>{e.exports=function isArrayish(e){if(!e||typeof e==="string"){return false}return e instanceof Array||Array.isArray(e)||e.length>=0&&(e.splice instanceof Function||Object.getOwnPropertyDescriptor(e,e.length-1)&&e.constructor.name!=="String")}},87:(e,t,i)=>{const r=i(819);const{MAX_LENGTH:n,MAX_SAFE_INTEGER:o}=i(929);const{safeRe:s,t:a}=i(235);const l=i(448);const{compareIdentifiers:c}=i(872);class SemVer{constructor(e,t){t=l(t);if(e instanceof SemVer){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease){return e}else{e=e.version}}else if(typeof e!=="string"){throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`)}if(e.length>n){throw new TypeError(`version is longer than ${n} characters`)}r("SemVer",e,t);this.options=t;this.loose=!!t.loose;this.includePrerelease=!!t.includePrerelease;const i=e.trim().match(t.loose?s[a.LOOSE]:s[a.FULL]);if(!i){throw new TypeError(`Invalid Version: ${e}`)}this.raw=e;this.major=+i[1];this.minor=+i[2];this.patch=+i[3];if(this.major>o||this.major<0){throw new TypeError("Invalid major version")}if(this.minor>o||this.minor<0){throw new TypeError("Invalid minor version")}if(this.patch>o||this.patch<0){throw new TypeError("Invalid patch version")}if(!i[4]){this.prerelease=[]}else{this.prerelease=i[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<o){return t}}return e}))}this.build=i[5]?i[5].split("."):[];this.format()}format(){this.version=`${this.major}.${this.minor}.${this.patch}`;if(this.prerelease.length){this.version+=`-${this.prerelease.join(".")}`}return this.version}toString(){return this.version}compare(e){r("SemVer.compare",this.version,this.options,e);if(!(e instanceof SemVer)){if(typeof e==="string"&&e===this.version){return 0}e=new SemVer(e,this.options)}if(e.version===this.version){return 0}return this.compareMain(e)||this.comparePre(e)}compareMain(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}return c(this.major,e.major)||c(this.minor,e.minor)||c(this.patch,e.patch)}comparePre(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}if(this.prerelease.length&&!e.prerelease.length){return-1}else if(!this.prerelease.length&&e.prerelease.length){return 1}else if(!this.prerelease.length&&!e.prerelease.length){return 0}let t=0;do{const i=this.prerelease[t];const n=e.prerelease[t];r("prerelease compare",t,i,n);if(i===undefined&&n===undefined){return 0}else if(n===undefined){return 1}else if(i===undefined){return-1}else if(i===n){continue}else{return c(i,n)}}while(++t)}compareBuild(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}let t=0;do{const i=this.build[t];const n=e.build[t];r("build compare",t,i,n);if(i===undefined&&n===undefined){return 0}else if(n===undefined){return 1}else if(i===undefined){return-1}else if(i===n){continue}else{return c(i,n)}}while(++t)}inc(e,t,i){switch(e){case"premajor":this.prerelease.length=0;this.patch=0;this.minor=0;this.major++;this.inc("pre",t,i);break;case"preminor":this.prerelease.length=0;this.patch=0;this.minor++;this.inc("pre",t,i);break;case"prepatch":this.prerelease.length=0;this.inc("patch",t,i);this.inc("pre",t,i);break;case"prerelease":if(this.prerelease.length===0){this.inc("patch",t,i)}this.inc("pre",t,i);break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0){this.major++}this.minor=0;this.patch=0;this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0){this.minor++}this.patch=0;this.prerelease=[];break;case"patch":if(this.prerelease.length===0){this.patch++}this.prerelease=[];break;case"pre":{const e=Number(i)?1:0;if(!t&&i===false){throw new Error("invalid increment argument: identifier is empty")}if(this.prerelease.length===0){this.prerelease=[e]}else{let r=this.prerelease.length;while(--r>=0){if(typeof this.prerelease[r]==="number"){this.prerelease[r]++;r=-2}}if(r===-1){if(t===this.prerelease.join(".")&&i===false){throw new Error("invalid increment argument: identifier already exists")}this.prerelease.push(e)}}if(t){let r=[t,e];if(i===false){r=[t]}if(c(this.prerelease[0],t)===0){if(isNaN(this.prerelease[1])){this.prerelease=r}}else{this.prerelease=r}}break}default:throw new Error(`invalid increment argument: ${e}`)}this.raw=this.format();if(this.build.length){this.raw+=`+${this.build.join(".")}`}return this}}e.exports=SemVer},93:(e,t,i)=>{const r=i(87);const n=i(741);const{safeRe:o,t:s}=i(235);const coerce=(e,t)=>{if(e instanceof r){return e}if(typeof e==="number"){e=String(e)}if(typeof e!=="string"){return null}t=t||{};let i=null;if(!t.rtl){i=e.match(t.includePrerelease?o[s.COERCEFULL]:o[s.COERCE])}else{const r=t.includePrerelease?o[s.COERCERTLFULL]:o[s.COERCERTL];let n;while((n=r.exec(e))&&(!i||i.index+i[0].length!==e.length)){if(!i||n.index+n[0].length!==i.index+i[0].length){i=n}r.lastIndex=n.index+n[1].length+n[2].length}r.lastIndex=-1}if(i===null){return null}const a=i[2];const l=i[3]||"0";const c=i[4]||"0";const h=t.includePrerelease&&i[5]?`-${i[5]}`:"";const f=t.includePrerelease&&i[6]?`+${i[6]}`:"";return n(`${a}.${l}.${c}${h}${f}`,t)};e.exports=coerce},89:(e,t,i)=>{const r=i(87);const compare=(e,t,i)=>new r(e,i).compare(new r(t,i));e.exports=compare},168:(e,t,i)=>{const r=i(89);const gte=(e,t,i)=>r(e,t,i)>=0;e.exports=gte},741:(e,t,i)=>{const r=i(87);const parse=(e,t,i=false)=>{if(e instanceof r){return e}try{return new r(e,t)}catch(e){if(!i){return null}throw e}};e.exports=parse},929:e=>{const t="2.0.0";const i=256;const r=Number.MAX_SAFE_INTEGER||9007199254740991;const n=16;const o=i-6;const s=["major","premajor","minor","preminor","patch","prepatch","prerelease"];e.exports={MAX_LENGTH:i,MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:o,MAX_SAFE_INTEGER:r,RELEASE_TYPES:s,SEMVER_SPEC_VERSION:t,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},819:e=>{const t=typeof process==="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},872:e=>{const t=/^[0-9]+$/;const compareIdentifiers=(e,i)=>{const r=t.test(e);const n=t.test(i);if(r&&n){e=+e;i=+i}return e===i?0:r&&!n?-1:n&&!r?1:e<i?-1:1};const rcompareIdentifiers=(e,t)=>compareIdentifiers(t,e);e.exports={compareIdentifiers:compareIdentifiers,rcompareIdentifiers:rcompareIdentifiers}},448:e=>{const t=Object.freeze({loose:true});const i=Object.freeze({});const parseOptions=e=>{if(!e){return i}if(typeof e!=="object"){return t}return e};e.exports=parseOptions},235:(e,t,i)=>{const{MAX_SAFE_COMPONENT_LENGTH:r,MAX_SAFE_BUILD_LENGTH:n,MAX_LENGTH:o}=i(929);const s=i(819);t=e.exports={};const a=t.re=[];const l=t.safeRe=[];const c=t.src=[];const h=t.t={};let f=0;const u="[a-zA-Z0-9-]";const p=[["\\s",1],["\\d",o],[u,n]];const makeSafeRegex=e=>{for(const[t,i]of p){e=e.split(`${t}*`).join(`${t}{0,${i}}`).split(`${t}+`).join(`${t}{1,${i}}`)}return e};const createToken=(e,t,i)=>{const r=makeSafeRegex(t);const n=f++;s(e,n,t);h[e]=n;c[n]=t;a[n]=new RegExp(t,i?"g":undefined);l[n]=new RegExp(r,i?"g":undefined)};createToken("NUMERICIDENTIFIER","0|[1-9]\\d*");createToken("NUMERICIDENTIFIERLOOSE","\\d+");createToken("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${u}*`);createToken("MAINVERSION",`(${c[h.NUMERICIDENTIFIER]})\\.`+`(${c[h.NUMERICIDENTIFIER]})\\.`+`(${c[h.NUMERICIDENTIFIER]})`);createToken("MAINVERSIONLOOSE",`(${c[h.NUMERICIDENTIFIERLOOSE]})\\.`+`(${c[h.NUMERICIDENTIFIERLOOSE]})\\.`+`(${c[h.NUMERICIDENTIFIERLOOSE]})`);createToken("PRERELEASEIDENTIFIER",`(?:${c[h.NUMERICIDENTIFIER]}|${c[h.NONNUMERICIDENTIFIER]})`);createToken("PRERELEASEIDENTIFIERLOOSE",`(?:${c[h.NUMERICIDENTIFIERLOOSE]}|${c[h.NONNUMERICIDENTIFIER]})`);createToken("PRERELEASE",`(?:-(${c[h.PRERELEASEIDENTIFIER]}(?:\\.${c[h.PRERELEASEIDENTIFIER]})*))`);createToken("PRERELEASELOOSE",`(?:-?(${c[h.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[h.PRERELEASEIDENTIFIERLOOSE]})*))`);createToken("BUILDIDENTIFIER",`${u}+`);createToken("BUILD",`(?:\\+(${c[h.BUILDIDENTIFIER]}(?:\\.${c[h.BUILDIDENTIFIER]})*))`);createToken("FULLPLAIN",`v?${c[h.MAINVERSION]}${c[h.PRERELEASE]}?${c[h.BUILD]}?`);createToken("FULL",`^${c[h.FULLPLAIN]}$`);createToken("LOOSEPLAIN",`[v=\\s]*${c[h.MAINVERSIONLOOSE]}${c[h.PRERELEASELOOSE]}?${c[h.BUILD]}?`);createToken("LOOSE",`^${c[h.LOOSEPLAIN]}$`);createToken("GTLT","((?:<|>)?=?)");createToken("XRANGEIDENTIFIERLOOSE",`${c[h.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);createToken("XRANGEIDENTIFIER",`${c[h.NUMERICIDENTIFIER]}|x|X|\\*`);createToken("XRANGEPLAIN",`[v=\\s]*(${c[h.XRANGEIDENTIFIER]})`+`(?:\\.(${c[h.XRANGEIDENTIFIER]})`+`(?:\\.(${c[h.XRANGEIDENTIFIER]})`+`(?:${c[h.PRERELEASE]})?${c[h.BUILD]}?`+`)?)?`);createToken("XRANGEPLAINLOOSE",`[v=\\s]*(${c[h.XRANGEIDENTIFIERLOOSE]})`+`(?:\\.(${c[h.XRANGEIDENTIFIERLOOSE]})`+`(?:\\.(${c[h.XRANGEIDENTIFIERLOOSE]})`+`(?:${c[h.PRERELEASELOOSE]})?${c[h.BUILD]}?`+`)?)?`);createToken("XRANGE",`^${c[h.GTLT]}\\s*${c[h.XRANGEPLAIN]}$`);createToken("XRANGELOOSE",`^${c[h.GTLT]}\\s*${c[h.XRANGEPLAINLOOSE]}$`);createToken("COERCEPLAIN",`${"(^|[^\\d])"+"(\\d{1,"}${r}})`+`(?:\\.(\\d{1,${r}}))?`+`(?:\\.(\\d{1,${r}}))?`);createToken("COERCE",`${c[h.COERCEPLAIN]}(?:$|[^\\d])`);createToken("COERCEFULL",c[h.COERCEPLAIN]+`(?:${c[h.PRERELEASE]})?`+`(?:${c[h.BUILD]})?`+`(?:$|[^\\d])`);createToken("COERCERTL",c[h.COERCE],true);createToken("COERCERTLFULL",c[h.COERCEFULL],true);createToken("LONETILDE","(?:~>?)");createToken("TILDETRIM",`(\\s*)${c[h.LONETILDE]}\\s+`,true);t.tildeTrimReplace="$1~";createToken("TILDE",`^${c[h.LONETILDE]}${c[h.XRANGEPLAIN]}$`);createToken("TILDELOOSE",`^${c[h.LONETILDE]}${c[h.XRANGEPLAINLOOSE]}$`);createToken("LONECARET","(?:\\^)");createToken("CARETTRIM",`(\\s*)${c[h.LONECARET]}\\s+`,true);t.caretTrimReplace="$1^";createToken("CARET",`^${c[h.LONECARET]}${c[h.XRANGEPLAIN]}$`);createToken("CARETLOOSE",`^${c[h.LONECARET]}${c[h.XRANGEPLAINLOOSE]}$`);createToken("COMPARATORLOOSE",`^${c[h.GTLT]}\\s*(${c[h.LOOSEPLAIN]})$|^$`);createToken("COMPARATOR",`^${c[h.GTLT]}\\s*(${c[h.FULLPLAIN]})$|^$`);createToken("COMPARATORTRIM",`(\\s*)${c[h.GTLT]}\\s*(${c[h.LOOSEPLAIN]}|${c[h.XRANGEPLAIN]})`,true);t.comparatorTrimReplace="$1$2$3";createToken("HYPHENRANGE",`^\\s*(${c[h.XRANGEPLAIN]})`+`\\s+-\\s+`+`(${c[h.XRANGEPLAIN]})`+`\\s*$`);createToken("HYPHENRANGELOOSE",`^\\s*(${c[h.XRANGEPLAINLOOSE]})`+`\\s+-\\s+`+`(${c[h.XRANGEPLAINLOOSE]})`+`\\s*$`);createToken("STAR","(<|>)?=?\\s*\\*");createToken("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");createToken("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},848:(e,t,i)=>{"use strict";const r=i(127);const n={and:"and",or:"or",eor:"eor"};function removeAlpha(){this.options.removeAlpha=true;return this}function ensureAlpha(e){if(r.defined(e)){if(r.number(e)&&r.inRange(e,0,1)){this.options.ensureAlpha=e}else{throw r.invalidParameterError("alpha","number between 0 and 1",e)}}else{this.options.ensureAlpha=1}return this}function extractChannel(e){const t={red:0,green:1,blue:2,alpha:3};if(Object.keys(t).includes(e)){e=t[e]}if(r.integer(e)&&r.inRange(e,0,4)){this.options.extractChannel=e}else{throw r.invalidParameterError("channel","integer or one of: red, green, blue, alpha",e)}return this}function joinChannel(e,t){if(Array.isArray(e)){e.forEach((function(e){this.options.joinChannelIn.push(this._createInputDescriptor(e,t))}),this)}else{this.options.joinChannelIn.push(this._createInputDescriptor(e,t))}return this}function bandbool(e){if(r.string(e)&&r.inArray(e,["and","or","eor"])){this.options.bandBoolOp=e}else{throw r.invalidParameterError("boolOp","one of: and, or, eor",e)}return this}e.exports=function(e){Object.assign(e.prototype,{removeAlpha:removeAlpha,ensureAlpha:ensureAlpha,extractChannel:extractChannel,joinChannel:joinChannel,bandbool:bandbool});e.bool=n}},707:(e,t,i)=>{"use strict";const r=i(665);const n=i(127);const o={multiband:"multiband","b-w":"b-w",bw:"b-w",cmyk:"cmyk",srgb:"srgb"};function tint(e){const t=r(e);this.options.tintA=t.a();this.options.tintB=t.b();return this}function greyscale(e){this.options.greyscale=n.bool(e)?e:true;return this}function grayscale(e){return this.greyscale(e)}function pipelineColourspace(e){if(!n.string(e)){throw n.invalidParameterError("colourspace","string",e)}this.options.colourspaceInput=e;return this}function pipelineColorspace(e){return this.pipelineColourspace(e)}function toColourspace(e){if(!n.string(e)){throw n.invalidParameterError("colourspace","string",e)}this.options.colourspace=e;return this}function toColorspace(e){return this.toColourspace(e)}function _setBackgroundColourOption(e,t){if(n.defined(t)){if(n.object(t)||n.string(t)){const i=r(t);this.options[e]=[i.red(),i.green(),i.blue(),Math.round(i.alpha()*255)]}else{throw n.invalidParameterError("background","object or string",t)}}}e.exports=function(e){Object.assign(e.prototype,{tint:tint,greyscale:greyscale,grayscale:grayscale,pipelineColourspace:pipelineColourspace,pipelineColorspace:pipelineColorspace,toColourspace:toColourspace,toColorspace:toColorspace,_setBackgroundColourOption:_setBackgroundColourOption});e.colourspace=o;e.colorspace=o}},8:(e,t,i)=>{"use strict";const r=i(127);const n={clear:"clear",source:"source",over:"over",in:"in",out:"out",atop:"atop",dest:"dest","dest-over":"dest-over","dest-in":"dest-in","dest-out":"dest-out","dest-atop":"dest-atop",xor:"xor",add:"add",saturate:"saturate",multiply:"multiply",screen:"screen",overlay:"overlay",darken:"darken",lighten:"lighten","colour-dodge":"colour-dodge","color-dodge":"colour-dodge","colour-burn":"colour-burn","color-burn":"colour-burn","hard-light":"hard-light","soft-light":"soft-light",difference:"difference",exclusion:"exclusion"};function composite(e){if(!Array.isArray(e)){throw r.invalidParameterError("images to composite","array",e)}this.options.composite=e.map((e=>{if(!r.object(e)){throw r.invalidParameterError("image to composite","object",e)}const t=this._inputOptionsFromObject(e);const i={input:this._createInputDescriptor(e.input,t,{allowStream:false}),blend:"over",tile:false,left:0,top:0,hasOffset:false,gravity:0,premultiplied:false};if(r.defined(e.blend)){if(r.string(n[e.blend])){i.blend=n[e.blend]}else{throw r.invalidParameterError("blend","valid blend name",e.blend)}}if(r.defined(e.tile)){if(r.bool(e.tile)){i.tile=e.tile}else{throw r.invalidParameterError("tile","boolean",e.tile)}}if(r.defined(e.left)){if(r.integer(e.left)){i.left=e.left}else{throw r.invalidParameterError("left","integer",e.left)}}if(r.defined(e.top)){if(r.integer(e.top)){i.top=e.top}else{throw r.invalidParameterError("top","integer",e.top)}}if(r.defined(e.top)!==r.defined(e.left)){throw new Error("Expected both left and top to be set")}else{i.hasOffset=r.integer(e.top)&&r.integer(e.left)}if(r.defined(e.gravity)){if(r.integer(e.gravity)&&r.inRange(e.gravity,0,8)){i.gravity=e.gravity}else if(r.string(e.gravity)&&r.integer(this.constructor.gravity[e.gravity])){i.gravity=this.constructor.gravity[e.gravity]}else{throw r.invalidParameterError("gravity","valid gravity",e.gravity)}}if(r.defined(e.premultiplied)){if(r.bool(e.premultiplied)){i.premultiplied=e.premultiplied}else{throw r.invalidParameterError("premultiplied","boolean",e.premultiplied)}}return i}));return this}e.exports=function(e){e.prototype.composite=composite;e.blend=n}},123:(e,t,i)=>{"use strict";const r=i(23);const n=i(203);const o=i(127);i(712).hasVendoredLibvips();i(379);const s=r.debuglog("sharp");const Sharp=function(e,t){if(arguments.length===1&&!o.defined(e)){throw new Error("Invalid input")}if(!(this instanceof Sharp)){return new Sharp(e,t)}n.Duplex.call(this);this.options={topOffsetPre:-1,leftOffsetPre:-1,widthPre:-1,heightPre:-1,topOffsetPost:-1,leftOffsetPost:-1,widthPost:-1,heightPost:-1,width:-1,height:-1,canvas:"crop",position:0,resizeBackground:[0,0,0,255],useExifOrientation:false,angle:0,rotationAngle:0,rotationBackground:[0,0,0,255],rotateBeforePreExtract:false,flip:false,flop:false,extendTop:0,extendBottom:0,extendLeft:0,extendRight:0,extendBackground:[0,0,0,255],extendWith:"background",withoutEnlargement:false,withoutReduction:false,affineMatrix:[],affineBackground:[0,0,0,255],affineIdx:0,affineIdy:0,affineOdx:0,affineOdy:0,affineInterpolator:this.constructor.interpolators.bilinear,kernel:"lanczos3",fastShrinkOnLoad:true,tintA:128,tintB:128,flatten:false,flattenBackground:[0,0,0],unflatten:false,negate:false,negateAlpha:true,medianSize:0,blurSigma:0,sharpenSigma:0,sharpenM1:1,sharpenM2:2,sharpenX1:2,sharpenY2:10,sharpenY3:20,threshold:0,thresholdGrayscale:true,trimBackground:[],trimThreshold:0,gamma:0,gammaOut:0,greyscale:false,normalise:false,normaliseLower:1,normaliseUpper:99,claheWidth:0,claheHeight:0,claheMaxSlope:3,brightness:1,saturation:1,hue:0,lightness:0,booleanBufferIn:null,booleanFileIn:"",joinChannelIn:[],extractChannel:-1,removeAlpha:false,ensureAlpha:-1,colourspace:"srgb",colourspaceInput:"last",composite:[],fileOut:"",formatOut:"input",streamOut:false,withMetadata:false,withMetadataOrientation:-1,withMetadataDensity:0,withMetadataIcc:"",withMetadataStrs:{},resolveWithObject:false,jpegQuality:80,jpegProgressive:false,jpegChromaSubsampling:"4:2:0",jpegTrellisQuantisation:false,jpegOvershootDeringing:false,jpegOptimiseScans:false,jpegOptimiseCoding:true,jpegQuantisationTable:0,pngProgressive:false,pngCompressionLevel:6,pngAdaptiveFiltering:false,pngPalette:false,pngQuality:100,pngEffort:7,pngBitdepth:8,pngDither:1,jp2Quality:80,jp2TileHeight:512,jp2TileWidth:512,jp2Lossless:false,jp2ChromaSubsampling:"4:4:4",webpQuality:80,webpAlphaQuality:100,webpLossless:false,webpNearLossless:false,webpSmartSubsample:false,webpPreset:"default",webpEffort:4,webpMinSize:false,webpMixed:false,gifBitdepth:8,gifEffort:7,gifDither:1,gifInterFrameMaxError:0,gifInterPaletteMaxError:3,gifReuse:true,gifProgressive:false,tiffQuality:80,tiffCompression:"jpeg",tiffPredictor:"horizontal",tiffPyramid:false,tiffBitdepth:8,tiffTile:false,tiffTileHeight:256,tiffTileWidth:256,tiffXres:1,tiffYres:1,tiffResolutionUnit:"inch",heifQuality:50,heifLossless:false,heifCompression:"av1",heifEffort:4,heifChromaSubsampling:"4:4:4",jxlDistance:1,jxlDecodingTier:0,jxlEffort:7,jxlLossless:false,rawDepth:"uchar",tileSize:256,tileOverlap:0,tileContainer:"fs",tileLayout:"dz",tileFormat:"last",tileDepth:"last",tileAngle:0,tileSkipBlanks:-1,tileBackground:[255,255,255,255],tileCentre:false,tileId:"https://example.com/iiif",tileBasename:"",timeoutSeconds:0,linearA:[],linearB:[],debuglog:e=>{this.emit("warning",e);s(e)},queueListener:function(e){Sharp.queue.emit("change",e)}};this.options.input=this._createInputDescriptor(e,t,{allowStream:true});return this};Object.setPrototypeOf(Sharp.prototype,n.Duplex.prototype);Object.setPrototypeOf(Sharp,n.Duplex);function clone(){const e=this.constructor.call();e.options=Object.assign({},this.options);if(this._isStreamInput()){this.on("finish",(()=>{this._flattenBufferIn();e.options.bufferIn=this.options.bufferIn;e.emit("finish")}))}return e}Object.assign(Sharp.prototype,{clone:clone});e.exports=Sharp},539:(e,t,i)=>{"use strict";const r=i(123);i(923)(r);i(487)(r);i(8)(r);i(898)(r);i(707)(r);i(848)(r);i(886)(r);i(231)(r);e.exports=r},923:(e,t,i)=>{"use strict";const r=i(665);const n=i(127);const o=i(379);const s={left:"low",center:"centre",centre:"centre",right:"high"};function _inputOptionsFromObject(e){const{raw:t,density:i,limitInputPixels:r,ignoreIcc:o,unlimited:s,sequentialRead:a,failOn:l,failOnError:c,animated:h,page:f,pages:u,subifd:p}=e;return[t,i,r,o,s,a,l,c,h,f,u,p].some(n.defined)?{raw:t,density:i,limitInputPixels:r,ignoreIcc:o,unlimited:s,sequentialRead:a,failOn:l,failOnError:c,animated:h,page:f,pages:u,subifd:p}:undefined}function _createInputDescriptor(e,t,i){const o={failOn:"warning",limitInputPixels:Math.pow(16383,2),ignoreIcc:false,unlimited:false,sequentialRead:true};if(n.string(e)){o.file=e}else if(n.buffer(e)){if(e.length===0){throw Error("Input Buffer is empty")}o.buffer=e}else if(n.arrayBuffer(e)){if(e.byteLength===0){throw Error("Input bit Array is empty")}o.buffer=Buffer.from(e,0,e.byteLength)}else if(n.typedArray(e)){if(e.length===0){throw Error("Input Bit Array is empty")}o.buffer=Buffer.from(e.buffer,e.byteOffset,e.byteLength)}else if(n.plainObject(e)&&!n.defined(t)){t=e;if(_inputOptionsFromObject(t)){o.buffer=[]}}else if(!n.defined(e)&&!n.defined(t)&&n.object(i)&&i.allowStream){o.buffer=[]}else{throw new Error(`Unsupported input '${e}' of type ${typeof e}${n.defined(t)?` when also providing options of type ${typeof t}`:""}`)}if(n.object(t)){if(n.defined(t.failOnError)){if(n.bool(t.failOnError)){o.failOn=t.failOnError?"warning":"none"}else{throw n.invalidParameterError("failOnError","boolean",t.failOnError)}}if(n.defined(t.failOn)){if(n.string(t.failOn)&&n.inArray(t.failOn,["none","truncated","error","warning"])){o.failOn=t.failOn}else{throw n.invalidParameterError("failOn","one of: none, truncated, error, warning",t.failOn)}}if(n.defined(t.density)){if(n.inRange(t.density,1,1e5)){o.density=t.density}else{throw n.invalidParameterError("density","number between 1 and 100000",t.density)}}if(n.defined(t.ignoreIcc)){if(n.bool(t.ignoreIcc)){o.ignoreIcc=t.ignoreIcc}else{throw n.invalidParameterError("ignoreIcc","boolean",t.ignoreIcc)}}if(n.defined(t.limitInputPixels)){if(n.bool(t.limitInputPixels)){o.limitInputPixels=t.limitInputPixels?Math.pow(16383,2):0}else if(n.integer(t.limitInputPixels)&&n.inRange(t.limitInputPixels,0,Number.MAX_SAFE_INTEGER)){o.limitInputPixels=t.limitInputPixels}else{throw n.invalidParameterError("limitInputPixels","positive integer",t.limitInputPixels)}}if(n.defined(t.unlimited)){if(n.bool(t.unlimited)){o.unlimited=t.unlimited}else{throw n.invalidParameterError("unlimited","boolean",t.unlimited)}}if(n.defined(t.sequentialRead)){if(n.bool(t.sequentialRead)){o.sequentialRead=t.sequentialRead}else{throw n.invalidParameterError("sequentialRead","boolean",t.sequentialRead)}}if(n.defined(t.raw)){if(n.object(t.raw)&&n.integer(t.raw.width)&&t.raw.width>0&&n.integer(t.raw.height)&&t.raw.height>0&&n.integer(t.raw.channels)&&n.inRange(t.raw.channels,1,4)){o.rawWidth=t.raw.width;o.rawHeight=t.raw.height;o.rawChannels=t.raw.channels;o.rawPremultiplied=!!t.raw.premultiplied;switch(e.constructor){case Uint8Array:case Uint8ClampedArray:o.rawDepth="uchar";break;case Int8Array:o.rawDepth="char";break;case Uint16Array:o.rawDepth="ushort";break;case Int16Array:o.rawDepth="short";break;case Uint32Array:o.rawDepth="uint";break;case Int32Array:o.rawDepth="int";break;case Float32Array:o.rawDepth="float";break;case Float64Array:o.rawDepth="double";break;default:o.rawDepth="uchar";break}}else{throw new Error("Expected width, height and channels for raw pixel input")}}if(n.defined(t.animated)){if(n.bool(t.animated)){o.pages=t.animated?-1:1}else{throw n.invalidParameterError("animated","boolean",t.animated)}}if(n.defined(t.pages)){if(n.integer(t.pages)&&n.inRange(t.pages,-1,1e5)){o.pages=t.pages}else{throw n.invalidParameterError("pages","integer between -1 and 100000",t.pages)}}if(n.defined(t.page)){if(n.integer(t.page)&&n.inRange(t.page,0,1e5)){o.page=t.page}else{throw n.invalidParameterError("page","integer between 0 and 100000",t.page)}}if(n.defined(t.level)){if(n.integer(t.level)&&n.inRange(t.level,0,256)){o.level=t.level}else{throw n.invalidParameterError("level","integer between 0 and 256",t.level)}}if(n.defined(t.subifd)){if(n.integer(t.subifd)&&n.inRange(t.subifd,-1,1e5)){o.subifd=t.subifd}else{throw n.invalidParameterError("subifd","integer between -1 and 100000",t.subifd)}}if(n.defined(t.create)){if(n.object(t.create)&&n.integer(t.create.width)&&t.create.width>0&&n.integer(t.create.height)&&t.create.height>0&&n.integer(t.create.channels)){o.createWidth=t.create.width;o.createHeight=t.create.height;o.createChannels=t.create.channels;if(n.defined(t.create.noise)){if(!n.object(t.create.noise)){throw new Error("Expected noise to be an object")}if(!n.inArray(t.create.noise.type,["gaussian"])){throw new Error("Only gaussian noise is supported at the moment")}if(!n.inRange(t.create.channels,1,4)){throw n.invalidParameterError("create.channels","number between 1 and 4",t.create.channels)}o.createNoiseType=t.create.noise.type;if(n.number(t.create.noise.mean)&&n.inRange(t.create.noise.mean,0,1e4)){o.createNoiseMean=t.create.noise.mean}else{throw n.invalidParameterError("create.noise.mean","number between 0 and 10000",t.create.noise.mean)}if(n.number(t.create.noise.sigma)&&n.inRange(t.create.noise.sigma,0,1e4)){o.createNoiseSigma=t.create.noise.sigma}else{throw n.invalidParameterError("create.noise.sigma","number between 0 and 10000",t.create.noise.sigma)}}else if(n.defined(t.create.background)){if(!n.inRange(t.create.channels,3,4)){throw n.invalidParameterError("create.channels","number between 3 and 4",t.create.channels)}const e=r(t.create.background);o.createBackground=[e.red(),e.green(),e.blue(),Math.round(e.alpha()*255)]}else{throw new Error("Expected valid noise or background to create a new input image")}delete o.buffer}else{throw new Error("Expected valid width, height and channels to create a new input image")}}if(n.defined(t.text)){if(n.object(t.text)&&n.string(t.text.text)){o.textValue=t.text.text;if(n.defined(t.text.height)&&n.defined(t.text.dpi)){throw new Error("Expected only one of dpi or height")}if(n.defined(t.text.font)){if(n.string(t.text.font)){o.textFont=t.text.font}else{throw n.invalidParameterError("text.font","string",t.text.font)}}if(n.defined(t.text.fontfile)){if(n.string(t.text.fontfile)){o.textFontfile=t.text.fontfile}else{throw n.invalidParameterError("text.fontfile","string",t.text.fontfile)}}if(n.defined(t.text.width)){if(n.number(t.text.width)){o.textWidth=t.text.width}else{throw n.invalidParameterError("text.textWidth","number",t.text.width)}}if(n.defined(t.text.height)){if(n.number(t.text.height)){o.textHeight=t.text.height}else{throw n.invalidParameterError("text.height","number",t.text.height)}}if(n.defined(t.text.align)){if(n.string(t.text.align)&&n.string(this.constructor.align[t.text.align])){o.textAlign=this.constructor.align[t.text.align]}else{throw n.invalidParameterError("text.align","valid alignment",t.text.align)}}if(n.defined(t.text.justify)){if(n.bool(t.text.justify)){o.textJustify=t.text.justify}else{throw n.invalidParameterError("text.justify","boolean",t.text.justify)}}if(n.defined(t.text.dpi)){if(n.number(t.text.dpi)&&n.inRange(t.text.dpi,1,1e5)){o.textDpi=t.text.dpi}else{throw n.invalidParameterError("text.dpi","number between 1 and 100000",t.text.dpi)}}if(n.defined(t.text.rgba)){if(n.bool(t.text.rgba)){o.textRgba=t.text.rgba}else{throw n.invalidParameterError("text.rgba","bool",t.text.rgba)}}if(n.defined(t.text.spacing)){if(n.number(t.text.spacing)){o.textSpacing=t.text.spacing}else{throw n.invalidParameterError("text.spacing","number",t.text.spacing)}}if(n.defined(t.text.wrap)){if(n.string(t.text.wrap)&&n.inArray(t.text.wrap,["word","char","wordChar","none"])){o.textWrap=t.text.wrap}else{throw n.invalidParameterError("text.wrap","one of: word, char, wordChar, none",t.text.wrap)}}delete o.buffer}else{throw new Error("Expected a valid string to create an image with text.")}}}else if(n.defined(t)){throw new Error("Invalid input options "+t)}return o}function _write(e,t,i){if(Array.isArray(this.options.input.buffer)){if(n.buffer(e)){if(this.options.input.buffer.length===0){this.on("finish",(()=>{this.streamInFinished=true}))}this.options.input.buffer.push(e);i()}else{i(new Error("Non-Buffer data on Writable Stream"))}}else{i(new Error("Unexpected data on Writable Stream"))}}function _flattenBufferIn(){if(this._isStreamInput()){this.options.input.buffer=Buffer.concat(this.options.input.buffer)}}function _isStreamInput(){return Array.isArray(this.options.input.buffer)}function metadata(e){if(n.fn(e)){if(this._isStreamInput()){this.on("finish",(()=>{this._flattenBufferIn();o.metadata(this.options,e)}))}else{o.metadata(this.options,e)}return this}else{if(this._isStreamInput()){return new Promise(((e,t)=>{const finished=()=>{this._flattenBufferIn();o.metadata(this.options,((i,r)=>{if(i){t(i)}else{e(r)}}))};if(this.writableFinished){finished()}else{this.once("finish",finished)}}))}else{return new Promise(((e,t)=>{o.metadata(this.options,((i,r)=>{if(i){t(i)}else{e(r)}}))}))}}}function stats(e){if(n.fn(e)){if(this._isStreamInput()){this.on("finish",(()=>{this._flattenBufferIn();o.stats(this.options,e)}))}else{o.stats(this.options,e)}return this}else{if(this._isStreamInput()){return new Promise(((e,t)=>{this.on("finish",(function(){this._flattenBufferIn();o.stats(this.options,((i,r)=>{if(i){t(i)}else{e(r)}}))}))}))}else{return new Promise(((e,t)=>{o.stats(this.options,((i,r)=>{if(i){t(i)}else{e(r)}}))}))}}}e.exports=function(e){Object.assign(e.prototype,{_inputOptionsFromObject:_inputOptionsFromObject,_createInputDescriptor:_createInputDescriptor,_write:_write,_flattenBufferIn:_flattenBufferIn,_isStreamInput:_isStreamInput,metadata:metadata,stats:stats});e.align=s}},127:e=>{"use strict";const defined=function(e){return typeof e!=="undefined"&&e!==null};const object=function(e){return typeof e==="object"};const plainObject=function(e){return Object.prototype.toString.call(e)==="[object Object]"};const fn=function(e){return typeof e==="function"};const bool=function(e){return typeof e==="boolean"};const buffer=function(e){return e instanceof Buffer};const typedArray=function(e){if(defined(e)){switch(e.constructor){case Uint8Array:case Uint8ClampedArray:case Int8Array:case Uint16Array:case Int16Array:case Uint32Array:case Int32Array:case Float32Array:case Float64Array:return true}}return false};const arrayBuffer=function(e){return e instanceof ArrayBuffer};const string=function(e){return typeof e==="string"&&e.length>0};const number=function(e){return typeof e==="number"&&!Number.isNaN(e)};const integer=function(e){return Number.isInteger(e)};const inRange=function(e,t,i){return e>=t&&e<=i};const inArray=function(e,t){return t.includes(e)};const invalidParameterError=function(e,t,i){return new Error(`Expected ${t} for ${e} but received ${i} of type ${typeof i}`)};e.exports={defined:defined,object:object,plainObject:plainObject,fn:fn,bool:bool,buffer:buffer,typedArray:typedArray,arrayBuffer:arrayBuffer,string:string,number:number,integer:integer,inRange:inRange,inArray:inArray,invalidParameterError:invalidParameterError}},712:(e,t,i)=>{"use strict";const r=i(896);const n=i(857);const o=i(928);const s=i(317).spawnSync;const a=i(93);const l=i(168);const c=i(256);const{config:h}=i(464);const f=process.env;const u=f.npm_package_config_libvips||h.libvips;const p=a(u).version;const d={encoding:"utf8",shell:true};const m=i.ab+"vendor/"+p+"/"+c();const mkdirSync=function(e){try{r.mkdirSync(e,{recursive:true})}catch(e){if(e.code!=="EEXIST"){throw e}}};const cachePath=function(){const e=f.npm_config_cache||(f.APPDATA?o.join(f.APPDATA,"npm-cache"):o.join(n.homedir(),".npm"));mkdirSync(e);const t=o.join(e,"_libvips");mkdirSync(t);return t};const integrity=function(e){return f[`npm_package_config_integrity_${e.replace("-","_")}`]||h.integrity[e]};const log=function(e){if(e instanceof Error){console.error(`sharp: Installation error: ${e.message}`)}else{console.log(`sharp: ${e}`)}};const isRosetta=function(){if(process.platform==="darwin"&&process.arch==="x64"){const e=s("sysctl sysctl.proc_translated",d).stdout;return(e||"").trim()==="sysctl.proc_translated: 1"}return false};const globalLibvipsVersion=function(){if(process.platform!=="win32"){const e=s("pkg-config --modversion vips-cpp",{...d,env:{...f,PKG_CONFIG_PATH:pkgConfigPath()}}).stdout;return(e||"").trim()}else{return""}};const hasVendoredLibvips=function(){return r.existsSync(m)};const removeVendoredLibvips=function(){r.rmSync(m,{recursive:true,maxRetries:3,force:true})};const pkgConfigPath=function(){if(process.platform!=="win32"){const e=s('which brew >/dev/null 2>&1 && brew environment --plain | grep PKG_CONFIG_LIBDIR | cut -d" " -f2',d).stdout||"";return[e.trim(),f.PKG_CONFIG_PATH,"/usr/local/lib/pkgconfig","/usr/lib/pkgconfig","/usr/local/libdata/pkgconfig","/usr/libdata/pkgconfig"].filter(Boolean).join(":")}else{return""}};const useGlobalLibvips=function(){if(Boolean(f.SHARP_IGNORE_GLOBAL_LIBVIPS)===true){return false}if(isRosetta()){log("Detected Rosetta, skipping search for globally-installed libvips");return false}const e=globalLibvipsVersion();return!!e&&l(e,p)};e.exports={minimumLibvipsVersion:p,minimumLibvipsVersionLabelled:u,cachePath:cachePath,integrity:integrity,log:log,globalLibvipsVersion:globalLibvipsVersion,hasVendoredLibvips:hasVendoredLibvips,removeVendoredLibvips:removeVendoredLibvips,pkgConfigPath:pkgConfigPath,useGlobalLibvips:useGlobalLibvips,mkdirSync:mkdirSync}},898:(e,t,i)=>{"use strict";const r=i(665);const n=i(127);function rotate(e,t){if(this.options.useExifOrientation||this.options.angle||this.options.rotationAngle){this.options.debuglog("ignoring previous rotate options")}if(!n.defined(e)){this.options.useExifOrientation=true}else if(n.integer(e)&&!(e%90)){this.options.angle=e}else if(n.number(e)){this.options.rotationAngle=e;if(n.object(t)&&t.background){const e=r(t.background);this.options.rotationBackground=[e.red(),e.green(),e.blue(),Math.round(e.alpha()*255)]}}else{throw n.invalidParameterError("angle","numeric",e)}return this}function flip(e){this.options.flip=n.bool(e)?e:true;return this}function flop(e){this.options.flop=n.bool(e)?e:true;return this}function affine(e,t){const i=[].concat(...e);if(i.length===4&&i.every(n.number)){this.options.affineMatrix=i}else{throw n.invalidParameterError("matrix","1x4 or 2x2 array",e)}if(n.defined(t)){if(n.object(t)){this._setBackgroundColourOption("affineBackground",t.background);if(n.defined(t.idx)){if(n.number(t.idx)){this.options.affineIdx=t.idx}else{throw n.invalidParameterError("options.idx","number",t.idx)}}if(n.defined(t.idy)){if(n.number(t.idy)){this.options.affineIdy=t.idy}else{throw n.invalidParameterError("options.idy","number",t.idy)}}if(n.defined(t.odx)){if(n.number(t.odx)){this.options.affineOdx=t.odx}else{throw n.invalidParameterError("options.odx","number",t.odx)}}if(n.defined(t.ody)){if(n.number(t.ody)){this.options.affineOdy=t.ody}else{throw n.invalidParameterError("options.ody","number",t.ody)}}if(n.defined(t.interpolator)){if(n.inArray(t.interpolator,Object.values(this.constructor.interpolators))){this.options.affineInterpolator=t.interpolator}else{throw n.invalidParameterError("options.interpolator","valid interpolator name",t.interpolator)}}}else{throw n.invalidParameterError("options","object",t)}}return this}function sharpen(e,t,i){if(!n.defined(e)){this.options.sharpenSigma=-1}else if(n.bool(e)){this.options.sharpenSigma=e?-1:0}else if(n.number(e)&&n.inRange(e,.01,1e4)){this.options.sharpenSigma=e;if(n.defined(t)){if(n.number(t)&&n.inRange(t,0,1e4)){this.options.sharpenM1=t}else{throw n.invalidParameterError("flat","number between 0 and 10000",t)}}if(n.defined(i)){if(n.number(i)&&n.inRange(i,0,1e4)){this.options.sharpenM2=i}else{throw n.invalidParameterError("jagged","number between 0 and 10000",i)}}}else if(n.plainObject(e)){if(n.number(e.sigma)&&n.inRange(e.sigma,1e-6,10)){this.options.sharpenSigma=e.sigma}else{throw n.invalidParameterError("options.sigma","number between 0.000001 and 10",e.sigma)}if(n.defined(e.m1)){if(n.number(e.m1)&&n.inRange(e.m1,0,1e6)){this.options.sharpenM1=e.m1}else{throw n.invalidParameterError("options.m1","number between 0 and 1000000",e.m1)}}if(n.defined(e.m2)){if(n.number(e.m2)&&n.inRange(e.m2,0,1e6)){this.options.sharpenM2=e.m2}else{throw n.invalidParameterError("options.m2","number between 0 and 1000000",e.m2)}}if(n.defined(e.x1)){if(n.number(e.x1)&&n.inRange(e.x1,0,1e6)){this.options.sharpenX1=e.x1}else{throw n.invalidParameterError("options.x1","number between 0 and 1000000",e.x1)}}if(n.defined(e.y2)){if(n.number(e.y2)&&n.inRange(e.y2,0,1e6)){this.options.sharpenY2=e.y2}else{throw n.invalidParameterError("options.y2","number between 0 and 1000000",e.y2)}}if(n.defined(e.y3)){if(n.number(e.y3)&&n.inRange(e.y3,0,1e6)){this.options.sharpenY3=e.y3}else{throw n.invalidParameterError("options.y3","number between 0 and 1000000",e.y3)}}}else{throw n.invalidParameterError("sigma","number between 0.01 and 10000",e)}return this}function median(e){if(!n.defined(e)){this.options.medianSize=3}else if(n.integer(e)&&n.inRange(e,1,1e3)){this.options.medianSize=e}else{throw n.invalidParameterError("size","integer between 1 and 1000",e)}return this}function blur(e){if(!n.defined(e)){this.options.blurSigma=-1}else if(n.bool(e)){this.options.blurSigma=e?-1:0}else if(n.number(e)&&n.inRange(e,.3,1e3)){this.options.blurSigma=e}else{throw n.invalidParameterError("sigma","number between 0.3 and 1000",e)}return this}function flatten(e){this.options.flatten=n.bool(e)?e:true;if(n.object(e)){this._setBackgroundColourOption("flattenBackground",e.background)}return this}function unflatten(){this.options.unflatten=true;return this}function gamma(e,t){if(!n.defined(e)){this.options.gamma=2.2}else if(n.number(e)&&n.inRange(e,1,3)){this.options.gamma=e}else{throw n.invalidParameterError("gamma","number between 1.0 and 3.0",e)}if(!n.defined(t)){this.options.gammaOut=this.options.gamma}else if(n.number(t)&&n.inRange(t,1,3)){this.options.gammaOut=t}else{throw n.invalidParameterError("gammaOut","number between 1.0 and 3.0",t)}return this}function negate(e){this.options.negate=n.bool(e)?e:true;if(n.plainObject(e)&&"alpha"in e){if(!n.bool(e.alpha)){throw n.invalidParameterError("alpha","should be boolean value",e.alpha)}else{this.options.negateAlpha=e.alpha}}return this}function normalise(e){if(n.plainObject(e)){if(n.defined(e.lower)){if(n.number(e.lower)&&n.inRange(e.lower,0,99)){this.options.normaliseLower=e.lower}else{throw n.invalidParameterError("lower","number between 0 and 99",e.lower)}}if(n.defined(e.upper)){if(n.number(e.upper)&&n.inRange(e.upper,1,100)){this.options.normaliseUpper=e.upper}else{throw n.invalidParameterError("upper","number between 1 and 100",e.upper)}}}if(this.options.normaliseLower>=this.options.normaliseUpper){throw n.invalidParameterError("range","lower to be less than upper",`${this.options.normaliseLower} >= ${this.options.normaliseUpper}`)}this.options.normalise=true;return this}function normalize(e){return this.normalise(e)}function clahe(e){if(n.plainObject(e)){if(n.integer(e.width)&&e.width>0){this.options.claheWidth=e.width}else{throw n.invalidParameterError("width","integer greater than zero",e.width)}if(n.integer(e.height)&&e.height>0){this.options.claheHeight=e.height}else{throw n.invalidParameterError("height","integer greater than zero",e.height)}if(n.defined(e.maxSlope)){if(n.integer(e.maxSlope)&&n.inRange(e.maxSlope,0,100)){this.options.claheMaxSlope=e.maxSlope}else{throw n.invalidParameterError("maxSlope","integer between 0 and 100",e.maxSlope)}}}else{throw n.invalidParameterError("options","plain object",e)}return this}function convolve(e){if(!n.object(e)||!Array.isArray(e.kernel)||!n.integer(e.width)||!n.integer(e.height)||!n.inRange(e.width,3,1001)||!n.inRange(e.height,3,1001)||e.height*e.width!==e.kernel.length){throw new Error("Invalid convolution kernel")}if(!n.integer(e.scale)){e.scale=e.kernel.reduce((function(e,t){return e+t}),0)}if(e.scale<1){e.scale=1}if(!n.integer(e.offset)){e.offset=0}this.options.convKernel=e;return this}function threshold(e,t){if(!n.defined(e)){this.options.threshold=128}else if(n.bool(e)){this.options.threshold=e?128:0}else if(n.integer(e)&&n.inRange(e,0,255)){this.options.threshold=e}else{throw n.invalidParameterError("threshold","integer between 0 and 255",e)}if(!n.object(t)||t.greyscale===true||t.grayscale===true){this.options.thresholdGrayscale=true}else{this.options.thresholdGrayscale=false}return this}function boolean(e,t,i){this.options.boolean=this._createInputDescriptor(e,i);if(n.string(t)&&n.inArray(t,["and","or","eor"])){this.options.booleanOp=t}else{throw n.invalidParameterError("operator","one of: and, or, eor",t)}return this}function linear(e,t){if(!n.defined(e)&&n.number(t)){e=1}else if(n.number(e)&&!n.defined(t)){t=0}if(!n.defined(e)){this.options.linearA=[]}else if(n.number(e)){this.options.linearA=[e]}else if(Array.isArray(e)&&e.length&&e.every(n.number)){this.options.linearA=e}else{throw n.invalidParameterError("a","number or array of numbers",e)}if(!n.defined(t)){this.options.linearB=[]}else if(n.number(t)){this.options.linearB=[t]}else if(Array.isArray(t)&&t.length&&t.every(n.number)){this.options.linearB=t}else{throw n.invalidParameterError("b","number or array of numbers",t)}if(this.options.linearA.length!==this.options.linearB.length){throw new Error("Expected a and b to be arrays of the same length")}return this}function recomb(e){if(!Array.isArray(e)||e.length!==3||e[0].length!==3||e[1].length!==3||e[2].length!==3){throw new Error("Invalid recombination matrix")}this.options.recombMatrix=[e[0][0],e[0][1],e[0][2],e[1][0],e[1][1],e[1][2],e[2][0],e[2][1],e[2][2]].map(Number);return this}function modulate(e){if(!n.plainObject(e)){throw n.invalidParameterError("options","plain object",e)}if("brightness"in e){if(n.number(e.brightness)&&e.brightness>=0){this.options.brightness=e.brightness}else{throw n.invalidParameterError("brightness","number above zero",e.brightness)}}if("saturation"in e){if(n.number(e.saturation)&&e.saturation>=0){this.options.saturation=e.saturation}else{throw n.invalidParameterError("saturation","number above zero",e.saturation)}}if("hue"in e){if(n.integer(e.hue)){this.options.hue=e.hue%360}else{throw n.invalidParameterError("hue","number",e.hue)}}if("lightness"in e){if(n.number(e.lightness)){this.options.lightness=e.lightness}else{throw n.invalidParameterError("lightness","number",e.lightness)}}return this}e.exports=function(e){Object.assign(e.prototype,{rotate:rotate,flip:flip,flop:flop,affine:affine,sharpen:sharpen,median:median,blur:blur,flatten:flatten,unflatten:unflatten,gamma:gamma,negate:negate,normalise:normalise,normalize:normalize,clahe:clahe,convolve:convolve,threshold:threshold,boolean:boolean,linear:linear,recomb:recomb,modulate:modulate})}},886:(e,t,i)=>{"use strict";const r=i(928);const n=i(127);const o=i(379);const s=new Map([["heic","heif"],["heif","heif"],["avif","avif"],["jpeg","jpeg"],["jpg","jpeg"],["jpe","jpeg"],["tile","tile"],["dz","tile"],["png","png"],["raw","raw"],["tiff","tiff"],["tif","tiff"],["webp","webp"],["gif","gif"],["jp2","jp2"],["jpx","jp2"],["j2k","jp2"],["j2c","jp2"],["jxl","jxl"]]);const a=/\.(jp[2x]|j2[kc])$/i;const errJp2Save=()=>new Error("JP2 output requires libvips with support for OpenJPEG");const bitdepthFromColourCount=e=>1<<31-Math.clz32(Math.ceil(Math.log2(e)));function toFile(e,t){let i;if(!n.string(e)){i=new Error("Missing output file path")}else if(n.string(this.options.input.file)&&r.resolve(this.options.input.file)===r.resolve(e)){i=new Error("Cannot use same file for input and output")}else if(a.test(r.extname(e))&&!this.constructor.format.jp2k.output.file){i=errJp2Save()}if(i){if(n.fn(t)){t(i)}else{return Promise.reject(i)}}else{this.options.fileOut=e;return this._pipeline(t)}return this}function toBuffer(e,t){if(n.object(e)){this._setBooleanOption("resolveWithObject",e.resolveWithObject)}else if(this.options.resolveWithObject){this.options.resolveWithObject=false}this.options.fileOut="";return this._pipeline(n.fn(e)?e:t)}function withMetadata(e){this.options.withMetadata=n.bool(e)?e:true;if(n.object(e)){if(n.defined(e.orientation)){if(n.integer(e.orientation)&&n.inRange(e.orientation,1,8)){this.options.withMetadataOrientation=e.orientation}else{throw n.invalidParameterError("orientation","integer between 1 and 8",e.orientation)}}if(n.defined(e.density)){if(n.number(e.density)&&e.density>0){this.options.withMetadataDensity=e.density}else{throw n.invalidParameterError("density","positive number",e.density)}}if(n.defined(e.icc)){if(n.string(e.icc)){this.options.withMetadataIcc=e.icc}else{throw n.invalidParameterError("icc","string filesystem path to ICC profile",e.icc)}}if(n.defined(e.exif)){if(n.object(e.exif)){for(const[t,i]of Object.entries(e.exif)){if(n.object(i)){for(const[e,r]of Object.entries(i)){if(n.string(r)){this.options.withMetadataStrs[`exif-${t.toLowerCase()}-${e}`]=r}else{throw n.invalidParameterError(`exif.${t}.${e}`,"string",r)}}}else{throw n.invalidParameterError(`exif.${t}`,"object",i)}}}else{throw n.invalidParameterError("exif","object",e.exif)}}}return this}function toFormat(e,t){const i=s.get((n.object(e)&&n.string(e.id)?e.id:e).toLowerCase());if(!i){throw n.invalidParameterError("format",`one of: ${[...s.keys()].join(", ")}`,e)}return this[i](t)}function jpeg(e){if(n.object(e)){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,1,100)){this.options.jpegQuality=e.quality}else{throw n.invalidParameterError("quality","integer between 1 and 100",e.quality)}}if(n.defined(e.progressive)){this._setBooleanOption("jpegProgressive",e.progressive)}if(n.defined(e.chromaSubsampling)){if(n.string(e.chromaSubsampling)&&n.inArray(e.chromaSubsampling,["4:2:0","4:4:4"])){this.options.jpegChromaSubsampling=e.chromaSubsampling}else{throw n.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",e.chromaSubsampling)}}const t=n.bool(e.optimizeCoding)?e.optimizeCoding:e.optimiseCoding;if(n.defined(t)){this._setBooleanOption("jpegOptimiseCoding",t)}if(n.defined(e.mozjpeg)){if(n.bool(e.mozjpeg)){if(e.mozjpeg){this.options.jpegTrellisQuantisation=true;this.options.jpegOvershootDeringing=true;this.options.jpegOptimiseScans=true;this.options.jpegProgressive=true;this.options.jpegQuantisationTable=3}}else{throw n.invalidParameterError("mozjpeg","boolean",e.mozjpeg)}}const i=n.bool(e.trellisQuantization)?e.trellisQuantization:e.trellisQuantisation;if(n.defined(i)){this._setBooleanOption("jpegTrellisQuantisation",i)}if(n.defined(e.overshootDeringing)){this._setBooleanOption("jpegOvershootDeringing",e.overshootDeringing)}const r=n.bool(e.optimizeScans)?e.optimizeScans:e.optimiseScans;if(n.defined(r)){this._setBooleanOption("jpegOptimiseScans",r);if(r){this.options.jpegProgressive=true}}const o=n.number(e.quantizationTable)?e.quantizationTable:e.quantisationTable;if(n.defined(o)){if(n.integer(o)&&n.inRange(o,0,8)){this.options.jpegQuantisationTable=o}else{throw n.invalidParameterError("quantisationTable","integer between 0 and 8",o)}}}return this._updateFormatOut("jpeg",e)}function png(e){if(n.object(e)){if(n.defined(e.progressive)){this._setBooleanOption("pngProgressive",e.progressive)}if(n.defined(e.compressionLevel)){if(n.integer(e.compressionLevel)&&n.inRange(e.compressionLevel,0,9)){this.options.pngCompressionLevel=e.compressionLevel}else{throw n.invalidParameterError("compressionLevel","integer between 0 and 9",e.compressionLevel)}}if(n.defined(e.adaptiveFiltering)){this._setBooleanOption("pngAdaptiveFiltering",e.adaptiveFiltering)}const t=e.colours||e.colors;if(n.defined(t)){if(n.integer(t)&&n.inRange(t,2,256)){this.options.pngBitdepth=bitdepthFromColourCount(t)}else{throw n.invalidParameterError("colours","integer between 2 and 256",t)}}if(n.defined(e.palette)){this._setBooleanOption("pngPalette",e.palette)}else if([e.quality,e.effort,e.colours,e.colors,e.dither].some(n.defined)){this._setBooleanOption("pngPalette",true)}if(this.options.pngPalette){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,0,100)){this.options.pngQuality=e.quality}else{throw n.invalidParameterError("quality","integer between 0 and 100",e.quality)}}if(n.defined(e.effort)){if(n.integer(e.effort)&&n.inRange(e.effort,1,10)){this.options.pngEffort=e.effort}else{throw n.invalidParameterError("effort","integer between 1 and 10",e.effort)}}if(n.defined(e.dither)){if(n.number(e.dither)&&n.inRange(e.dither,0,1)){this.options.pngDither=e.dither}else{throw n.invalidParameterError("dither","number between 0.0 and 1.0",e.dither)}}}}return this._updateFormatOut("png",e)}function webp(e){if(n.object(e)){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,1,100)){this.options.webpQuality=e.quality}else{throw n.invalidParameterError("quality","integer between 1 and 100",e.quality)}}if(n.defined(e.alphaQuality)){if(n.integer(e.alphaQuality)&&n.inRange(e.alphaQuality,0,100)){this.options.webpAlphaQuality=e.alphaQuality}else{throw n.invalidParameterError("alphaQuality","integer between 0 and 100",e.alphaQuality)}}if(n.defined(e.lossless)){this._setBooleanOption("webpLossless",e.lossless)}if(n.defined(e.nearLossless)){this._setBooleanOption("webpNearLossless",e.nearLossless)}if(n.defined(e.smartSubsample)){this._setBooleanOption("webpSmartSubsample",e.smartSubsample)}if(n.defined(e.preset)){if(n.string(e.preset)&&n.inArray(e.preset,["default","photo","picture","drawing","icon","text"])){this.options.webpPreset=e.preset}else{throw n.invalidParameterError("preset","one of: default, photo, picture, drawing, icon, text",e.preset)}}if(n.defined(e.effort)){if(n.integer(e.effort)&&n.inRange(e.effort,0,6)){this.options.webpEffort=e.effort}else{throw n.invalidParameterError("effort","integer between 0 and 6",e.effort)}}if(n.defined(e.minSize)){this._setBooleanOption("webpMinSize",e.minSize)}if(n.defined(e.mixed)){this._setBooleanOption("webpMixed",e.mixed)}}trySetAnimationOptions(e,this.options);return this._updateFormatOut("webp",e)}function gif(e){if(n.object(e)){if(n.defined(e.reuse)){this._setBooleanOption("gifReuse",e.reuse)}if(n.defined(e.progressive)){this._setBooleanOption("gifProgressive",e.progressive)}const t=e.colours||e.colors;if(n.defined(t)){if(n.integer(t)&&n.inRange(t,2,256)){this.options.gifBitdepth=bitdepthFromColourCount(t)}else{throw n.invalidParameterError("colours","integer between 2 and 256",t)}}if(n.defined(e.effort)){if(n.number(e.effort)&&n.inRange(e.effort,1,10)){this.options.gifEffort=e.effort}else{throw n.invalidParameterError("effort","integer between 1 and 10",e.effort)}}if(n.defined(e.dither)){if(n.number(e.dither)&&n.inRange(e.dither,0,1)){this.options.gifDither=e.dither}else{throw n.invalidParameterError("dither","number between 0.0 and 1.0",e.dither)}}if(n.defined(e.interFrameMaxError)){if(n.number(e.interFrameMaxError)&&n.inRange(e.interFrameMaxError,0,32)){this.options.gifInterFrameMaxError=e.interFrameMaxError}else{throw n.invalidParameterError("interFrameMaxError","number between 0.0 and 32.0",e.interFrameMaxError)}}if(n.defined(e.interPaletteMaxError)){if(n.number(e.interPaletteMaxError)&&n.inRange(e.interPaletteMaxError,0,256)){this.options.gifInterPaletteMaxError=e.interPaletteMaxError}else{throw n.invalidParameterError("interPaletteMaxError","number between 0.0 and 256.0",e.interPaletteMaxError)}}}trySetAnimationOptions(e,this.options);return this._updateFormatOut("gif",e)}function jp2(e){if(!this.constructor.format.jp2k.output.buffer){throw errJp2Save()}if(n.object(e)){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,1,100)){this.options.jp2Quality=e.quality}else{throw n.invalidParameterError("quality","integer between 1 and 100",e.quality)}}if(n.defined(e.lossless)){if(n.bool(e.lossless)){this.options.jp2Lossless=e.lossless}else{throw n.invalidParameterError("lossless","boolean",e.lossless)}}if(n.defined(e.tileWidth)){if(n.integer(e.tileWidth)&&n.inRange(e.tileWidth,1,32768)){this.options.jp2TileWidth=e.tileWidth}else{throw n.invalidParameterError("tileWidth","integer between 1 and 32768",e.tileWidth)}}if(n.defined(e.tileHeight)){if(n.integer(e.tileHeight)&&n.inRange(e.tileHeight,1,32768)){this.options.jp2TileHeight=e.tileHeight}else{throw n.invalidParameterError("tileHeight","integer between 1 and 32768",e.tileHeight)}}if(n.defined(e.chromaSubsampling)){if(n.string(e.chromaSubsampling)&&n.inArray(e.chromaSubsampling,["4:2:0","4:4:4"])){this.options.jp2ChromaSubsampling=e.chromaSubsampling}else{throw n.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",e.chromaSubsampling)}}}return this._updateFormatOut("jp2",e)}function trySetAnimationOptions(e,t){if(n.object(e)&&n.defined(e.loop)){if(n.integer(e.loop)&&n.inRange(e.loop,0,65535)){t.loop=e.loop}else{throw n.invalidParameterError("loop","integer between 0 and 65535",e.loop)}}if(n.object(e)&&n.defined(e.delay)){if(n.integer(e.delay)&&n.inRange(e.delay,0,65535)){t.delay=[e.delay]}else if(Array.isArray(e.delay)&&e.delay.every(n.integer)&&e.delay.every((e=>n.inRange(e,0,65535)))){t.delay=e.delay}else{throw n.invalidParameterError("delay","integer or an array of integers between 0 and 65535",e.delay)}}}function tiff(e){if(n.object(e)){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,1,100)){this.options.tiffQuality=e.quality}else{throw n.invalidParameterError("quality","integer between 1 and 100",e.quality)}}if(n.defined(e.bitdepth)){if(n.integer(e.bitdepth)&&n.inArray(e.bitdepth,[1,2,4,8])){this.options.tiffBitdepth=e.bitdepth}else{throw n.invalidParameterError("bitdepth","1, 2, 4 or 8",e.bitdepth)}}if(n.defined(e.tile)){this._setBooleanOption("tiffTile",e.tile)}if(n.defined(e.tileWidth)){if(n.integer(e.tileWidth)&&e.tileWidth>0){this.options.tiffTileWidth=e.tileWidth}else{throw n.invalidParameterError("tileWidth","integer greater than zero",e.tileWidth)}}if(n.defined(e.tileHeight)){if(n.integer(e.tileHeight)&&e.tileHeight>0){this.options.tiffTileHeight=e.tileHeight}else{throw n.invalidParameterError("tileHeight","integer greater than zero",e.tileHeight)}}if(n.defined(e.pyramid)){this._setBooleanOption("tiffPyramid",e.pyramid)}if(n.defined(e.xres)){if(n.number(e.xres)&&e.xres>0){this.options.tiffXres=e.xres}else{throw n.invalidParameterError("xres","number greater than zero",e.xres)}}if(n.defined(e.yres)){if(n.number(e.yres)&&e.yres>0){this.options.tiffYres=e.yres}else{throw n.invalidParameterError("yres","number greater than zero",e.yres)}}if(n.defined(e.compression)){if(n.string(e.compression)&&n.inArray(e.compression,["none","jpeg","deflate","packbits","ccittfax4","lzw","webp","zstd","jp2k"])){this.options.tiffCompression=e.compression}else{throw n.invalidParameterError("compression","one of: none, jpeg, deflate, packbits, ccittfax4, lzw, webp, zstd, jp2k",e.compression)}}if(n.defined(e.predictor)){if(n.string(e.predictor)&&n.inArray(e.predictor,["none","horizontal","float"])){this.options.tiffPredictor=e.predictor}else{throw n.invalidParameterError("predictor","one of: none, horizontal, float",e.predictor)}}if(n.defined(e.resolutionUnit)){if(n.string(e.resolutionUnit)&&n.inArray(e.resolutionUnit,["inch","cm"])){this.options.tiffResolutionUnit=e.resolutionUnit}else{throw n.invalidParameterError("resolutionUnit","one of: inch, cm",e.resolutionUnit)}}}return this._updateFormatOut("tiff",e)}function avif(e){return this.heif({...e,compression:"av1"})}function heif(e){if(n.object(e)){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,1,100)){this.options.heifQuality=e.quality}else{throw n.invalidParameterError("quality","integer between 1 and 100",e.quality)}}if(n.defined(e.lossless)){if(n.bool(e.lossless)){this.options.heifLossless=e.lossless}else{throw n.invalidParameterError("lossless","boolean",e.lossless)}}if(n.defined(e.compression)){if(n.string(e.compression)&&n.inArray(e.compression,["av1","hevc"])){this.options.heifCompression=e.compression}else{throw n.invalidParameterError("compression","one of: av1, hevc",e.compression)}}if(n.defined(e.effort)){if(n.integer(e.effort)&&n.inRange(e.effort,0,9)){this.options.heifEffort=e.effort}else{throw n.invalidParameterError("effort","integer between 0 and 9",e.effort)}}if(n.defined(e.chromaSubsampling)){if(n.string(e.chromaSubsampling)&&n.inArray(e.chromaSubsampling,["4:2:0","4:4:4"])){this.options.heifChromaSubsampling=e.chromaSubsampling}else{throw n.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",e.chromaSubsampling)}}}return this._updateFormatOut("heif",e)}function jxl(e){if(n.object(e)){if(n.defined(e.quality)){if(n.integer(e.quality)&&n.inRange(e.quality,1,100)){this.options.jxlDistance=e.quality>=30?.1+(100-e.quality)*.09:53/3e3*e.quality*e.quality-23/20*e.quality+25}else{throw n.invalidParameterError("quality","integer between 1 and 100",e.quality)}}else if(n.defined(e.distance)){if(n.number(e.distance)&&n.inRange(e.distance,0,15)){this.options.jxlDistance=e.distance}else{throw n.invalidParameterError("distance","number between 0.0 and 15.0",e.distance)}}if(n.defined(e.decodingTier)){if(n.integer(e.decodingTier)&&n.inRange(e.decodingTier,0,4)){this.options.jxlDecodingTier=e.decodingTier}else{throw n.invalidParameterError("decodingTier","integer between 0 and 4",e.decodingTier)}}if(n.defined(e.lossless)){if(n.bool(e.lossless)){this.options.jxlLossless=e.lossless}else{throw n.invalidParameterError("lossless","boolean",e.lossless)}}if(n.defined(e.effort)){if(n.integer(e.effort)&&n.inRange(e.effort,3,9)){this.options.jxlEffort=e.effort}else{throw n.invalidParameterError("effort","integer between 3 and 9",e.effort)}}}return this._updateFormatOut("jxl",e)}function raw(e){if(n.object(e)){if(n.defined(e.depth)){if(n.string(e.depth)&&n.inArray(e.depth,["char","uchar","short","ushort","int","uint","float","complex","double","dpcomplex"])){this.options.rawDepth=e.depth}else{throw n.invalidParameterError("depth","one of: char, uchar, short, ushort, int, uint, float, complex, double, dpcomplex",e.depth)}}}return this._updateFormatOut("raw")}function tile(e){if(n.object(e)){if(n.defined(e.size)){if(n.integer(e.size)&&n.inRange(e.size,1,8192)){this.options.tileSize=e.size}else{throw n.invalidParameterError("size","integer between 1 and 8192",e.size)}}if(n.defined(e.overlap)){if(n.integer(e.overlap)&&n.inRange(e.overlap,0,8192)){if(e.overlap>this.options.tileSize){throw n.invalidParameterError("overlap",`<= size (${this.options.tileSize})`,e.overlap)}this.options.tileOverlap=e.overlap}else{throw n.invalidParameterError("overlap","integer between 0 and 8192",e.overlap)}}if(n.defined(e.container)){if(n.string(e.container)&&n.inArray(e.container,["fs","zip"])){this.options.tileContainer=e.container}else{throw n.invalidParameterError("container","one of: fs, zip",e.container)}}if(n.defined(e.layout)){if(n.string(e.layout)&&n.inArray(e.layout,["dz","google","iiif","iiif3","zoomify"])){this.options.tileLayout=e.layout}else{throw n.invalidParameterError("layout","one of: dz, google, iiif, iiif3, zoomify",e.layout)}}if(n.defined(e.angle)){if(n.integer(e.angle)&&!(e.angle%90)){this.options.tileAngle=e.angle}else{throw n.invalidParameterError("angle","positive/negative multiple of 90",e.angle)}}this._setBackgroundColourOption("tileBackground",e.background);if(n.defined(e.depth)){if(n.string(e.depth)&&n.inArray(e.depth,["onepixel","onetile","one"])){this.options.tileDepth=e.depth}else{throw n.invalidParameterError("depth","one of: onepixel, onetile, one",e.depth)}}if(n.defined(e.skipBlanks)){if(n.integer(e.skipBlanks)&&n.inRange(e.skipBlanks,-1,65535)){this.options.tileSkipBlanks=e.skipBlanks}else{throw n.invalidParameterError("skipBlanks","integer between -1 and 255/65535",e.skipBlanks)}}else if(n.defined(e.layout)&&e.layout==="google"){this.options.tileSkipBlanks=5}const t=n.bool(e.center)?e.center:e.centre;if(n.defined(t)){this._setBooleanOption("tileCentre",t)}if(n.defined(e.id)){if(n.string(e.id)){this.options.tileId=e.id}else{throw n.invalidParameterError("id","string",e.id)}}if(n.defined(e.basename)){if(n.string(e.basename)){this.options.tileBasename=e.basename}else{throw n.invalidParameterError("basename","string",e.basename)}}}if(n.inArray(this.options.formatOut,["jpeg","png","webp"])){this.options.tileFormat=this.options.formatOut}else if(this.options.formatOut!=="input"){throw n.invalidParameterError("format","one of: jpeg, png, webp",this.options.formatOut)}return this._updateFormatOut("dz")}function timeout(e){if(!n.plainObject(e)){throw n.invalidParameterError("options","object",e)}if(n.integer(e.seconds)&&n.inRange(e.seconds,0,3600)){this.options.timeoutSeconds=e.seconds}else{throw n.invalidParameterError("seconds","integer between 0 and 3600",e.seconds)}return this}function _updateFormatOut(e,t){if(!(n.object(t)&&t.force===false)){this.options.formatOut=e}return this}function _setBooleanOption(e,t){if(n.bool(t)){this.options[e]=t}else{throw n.invalidParameterError(e,"boolean",t)}}function _read(){if(!this.options.streamOut){this.options.streamOut=true;this._pipeline()}}function _pipeline(e){if(typeof e==="function"){if(this._isStreamInput()){this.on("finish",(()=>{this._flattenBufferIn();o.pipeline(this.options,e)}))}else{o.pipeline(this.options,e)}return this}else if(this.options.streamOut){if(this._isStreamInput()){this.once("finish",(()=>{this._flattenBufferIn();o.pipeline(this.options,((e,t,i)=>{if(e){this.emit("error",e)}else{this.emit("info",i);this.push(t)}this.push(null);this.on("end",(()=>this.emit("close")))}))}));if(this.streamInFinished){this.emit("finish")}}else{o.pipeline(this.options,((e,t,i)=>{if(e){this.emit("error",e)}else{this.emit("info",i);this.push(t)}this.push(null);this.on("end",(()=>this.emit("close")))}))}return this}else{if(this._isStreamInput()){return new Promise(((e,t)=>{this.once("finish",(()=>{this._flattenBufferIn();o.pipeline(this.options,((i,r,n)=>{if(i){t(i)}else{if(this.options.resolveWithObject){e({data:r,info:n})}else{e(r)}}}))}))}))}else{return new Promise(((e,t)=>{o.pipeline(this.options,((i,r,n)=>{if(i){t(i)}else{if(this.options.resolveWithObject){e({data:r,info:n})}else{e(r)}}}))}))}}}e.exports=function(e){Object.assign(e.prototype,{toFile:toFile,toBuffer:toBuffer,withMetadata:withMetadata,toFormat:toFormat,jpeg:jpeg,jp2:jp2,png:png,webp:webp,tiff:tiff,avif:avif,heif:heif,jxl:jxl,gif:gif,raw:raw,tile:tile,timeout:timeout,_updateFormatOut:_updateFormatOut,_setBooleanOption:_setBooleanOption,_read:_read,_pipeline:_pipeline})}},256:(e,t,i)=>{"use strict";const r=i(513);const n=process.env;e.exports=function(){const e=n.npm_config_arch||process.arch;const t=n.npm_config_platform||process.platform;const i=process.env.npm_config_libc||(r.isNonGlibcLinuxSync()?r.familySync():"");const o=t!=="linux"||i===r.GLIBC?"":i;const s=[`${t}${o}`];if(e==="arm"){const e=process.versions.electron?"7":"6";s.push(`armv${n.npm_config_arm_version||process.config.variables.arm_version||e}`)}else if(e==="arm64"){s.push(`arm64v${n.npm_config_arm_version||"8"}`)}else{s.push(e)}return s.join("-")}},487:(e,t,i)=>{"use strict";const r=i(127);const n={center:0,centre:0,north:1,east:2,south:3,west:4,northeast:5,southeast:6,southwest:7,northwest:8};const o={top:1,right:2,bottom:3,left:4,"right top":5,"right bottom":6,"left bottom":7,"left top":8};const s={background:"background",copy:"copy",repeat:"repeat",mirror:"mirror"};const a={entropy:16,attention:17};const l={nearest:"nearest",cubic:"cubic",mitchell:"mitchell",lanczos2:"lanczos2",lanczos3:"lanczos3"};const c={contain:"contain",cover:"cover",fill:"fill",inside:"inside",outside:"outside"};const h={contain:"embed",cover:"crop",fill:"ignore_aspect",inside:"max",outside:"min"};function isRotationExpected(e){return e.angle%360!==0||e.useExifOrientation===true||e.rotationAngle!==0}function isResizeExpected(e){return e.width!==-1||e.height!==-1}function resize(e,t,i){if(isResizeExpected(this.options)){this.options.debuglog("ignoring previous resize options")}if(r.defined(e)){if(r.object(e)&&!r.defined(i)){i=e}else if(r.integer(e)&&e>0){this.options.width=e}else{throw r.invalidParameterError("width","positive integer",e)}}else{this.options.width=-1}if(r.defined(t)){if(r.integer(t)&&t>0){this.options.height=t}else{throw r.invalidParameterError("height","positive integer",t)}}else{this.options.height=-1}if(r.object(i)){if(r.defined(i.width)){if(r.integer(i.width)&&i.width>0){this.options.width=i.width}else{throw r.invalidParameterError("width","positive integer",i.width)}}if(r.defined(i.height)){if(r.integer(i.height)&&i.height>0){this.options.height=i.height}else{throw r.invalidParameterError("height","positive integer",i.height)}}if(r.defined(i.fit)){const e=h[i.fit];if(r.string(e)){this.options.canvas=e}else{throw r.invalidParameterError("fit","valid fit",i.fit)}}if(r.defined(i.position)){const e=r.integer(i.position)?i.position:a[i.position]||o[i.position]||n[i.position];if(r.integer(e)&&(r.inRange(e,0,8)||r.inRange(e,16,17))){this.options.position=e}else{throw r.invalidParameterError("position","valid position/gravity/strategy",i.position)}}this._setBackgroundColourOption("resizeBackground",i.background);if(r.defined(i.kernel)){if(r.string(l[i.kernel])){this.options.kernel=l[i.kernel]}else{throw r.invalidParameterError("kernel","valid kernel name",i.kernel)}}if(r.defined(i.withoutEnlargement)){this._setBooleanOption("withoutEnlargement",i.withoutEnlargement)}if(r.defined(i.withoutReduction)){this._setBooleanOption("withoutReduction",i.withoutReduction)}if(r.defined(i.fastShrinkOnLoad)){this._setBooleanOption("fastShrinkOnLoad",i.fastShrinkOnLoad)}}if(isRotationExpected(this.options)&&isResizeExpected(this.options)){this.options.rotateBeforePreExtract=true}return this}function extend(e){if(r.integer(e)&&e>0){this.options.extendTop=e;this.options.extendBottom=e;this.options.extendLeft=e;this.options.extendRight=e}else if(r.object(e)){if(r.defined(e.top)){if(r.integer(e.top)&&e.top>=0){this.options.extendTop=e.top}else{throw r.invalidParameterError("top","positive integer",e.top)}}if(r.defined(e.bottom)){if(r.integer(e.bottom)&&e.bottom>=0){this.options.extendBottom=e.bottom}else{throw r.invalidParameterError("bottom","positive integer",e.bottom)}}if(r.defined(e.left)){if(r.integer(e.left)&&e.left>=0){this.options.extendLeft=e.left}else{throw r.invalidParameterError("left","positive integer",e.left)}}if(r.defined(e.right)){if(r.integer(e.right)&&e.right>=0){this.options.extendRight=e.right}else{throw r.invalidParameterError("right","positive integer",e.right)}}this._setBackgroundColourOption("extendBackground",e.background);if(r.defined(e.extendWith)){if(r.string(s[e.extendWith])){this.options.extendWith=s[e.extendWith]}else{throw r.invalidParameterError("extendWith","one of: background, copy, repeat, mirror",e.extendWith)}}}else{throw r.invalidParameterError("extend","integer or object",e)}return this}function extract(e){const t=isResizeExpected(this.options)||this.options.widthPre!==-1?"Post":"Pre";if(this.options[`width${t}`]!==-1){this.options.debuglog("ignoring previous extract options")}["left","top","width","height"].forEach((function(i){const n=e[i];if(r.integer(n)&&n>=0){this.options[i+(i==="left"||i==="top"?"Offset":"")+t]=n}else{throw r.invalidParameterError(i,"integer",n)}}),this);if(isRotationExpected(this.options)&&!isResizeExpected(this.options)){if(this.options.widthPre===-1||this.options.widthPost===-1){this.options.rotateBeforePreExtract=true}}return this}function trim(e){if(!r.defined(e)){this.options.trimThreshold=10}else if(r.string(e)){this._setBackgroundColourOption("trimBackground",e);this.options.trimThreshold=10}else if(r.number(e)){if(e>=0){this.options.trimThreshold=e}else{throw r.invalidParameterError("threshold","positive number",e)}}else if(r.object(e)){this._setBackgroundColourOption("trimBackground",e.background);if(!r.defined(e.threshold)){this.options.trimThreshold=10}else if(r.number(e.threshold)&&e.threshold>=0){this.options.trimThreshold=e.threshold}else{throw r.invalidParameterError("threshold","positive number",e)}}else{throw r.invalidParameterError("trim","string, number or object",e)}if(isRotationExpected(this.options)){this.options.rotateBeforePreExtract=true}return this}e.exports=function(e){Object.assign(e.prototype,{resize:resize,extend:extend,extract:extract,trim:trim});e.gravity=n;e.strategy=a;e.kernel=l;e.fit=c;e.position=o}},379:(e,t,i)=>{function __ncc_wildcard$0(e){if(e==="darwin-arm64v8")return i(251)}"use strict";const r=i(256)();try{e.exports=__ncc_wildcard$0(r)}catch(e){const t=["",'Something went wrong installing the "sharp" module',"",e.message,"","Possible solutions:"];if(/dylib/.test(e.message)&&/Incompatible library version/.test(e.message)){t.push('- Update Homebrew: "brew update && brew upgrade vips"')}else{const[i,n]=r.split("-");if(i==="linux"&&/Module did not self-register/.test(e.message)){t.push("- Using worker threads? See https://sharp.pixelplumbing.com/install#worker-threads")}t.push('- Install with verbose logging and look for errors: "npm install --ignore-scripts=false --foreground-scripts --verbose sharp"',`- Install for the current ${r} runtime: "npm install --platform=${i} --arch=${n} sharp"`)}t.push("- Consult the installation documentation: https://sharp.pixelplumbing.com/install");if(process.platform==="win32"||/symbol/.test(e.message)){const e=Object.keys(require.cache).find((e=>/[\\/]build[\\/]Release[\\/]sharp(.*)\.node$/.test(e)));if(e){const[,i]=e.match(/node_modules[\\/]([^\\/]+)[\\/]/);t.push(`- Ensure the version of sharp aligns with the ${i} package: "npm ls sharp"`)}}throw new Error(t.join("\n"))}},231:(e,t,i)=>{"use strict";const r=i(896);const n=i(928);const o=i(434);const s=i(513);const a=i(127);const l=i(256)();const c=i(379);const h=c.format();h.heif.output.alias=["avif","heic"];h.jpeg.output.alias=["jpe","jpg"];h.tiff.output.alias=["tif"];h.jp2k.output.alias=["j2c","j2k","jp2","jpx"];const f={nearest:"nearest",bilinear:"bilinear",bicubic:"bicubic",locallyBoundedBicubic:"lbb",nohalo:"nohalo",vertexSplitQuadraticBasisSpline:"vsqbs"};let u={vips:c.libvipsVersion()};try{u=require(`../vendor/${u.vips}/${l}/versions.json`)}catch(e){}u.sharp=i(464).version;const p={current:l,installed:[]};try{p.installed=r.readdirSync(i.ab+"vendor/"+u.vips)}catch(e){}function cache(e){if(a.bool(e)){if(e){return c.cache(50,20,100)}else{return c.cache(0,0,0)}}else if(a.object(e)){return c.cache(e.memory,e.files,e.items)}else{return c.cache()}}cache(true);function concurrency(e){return c.concurrency(a.integer(e)?e:null)}if(s.familySync()===s.GLIBC&&!c._isUsingJemalloc()){c.concurrency(1)}const d=new o.EventEmitter;function counters(){return c.counters()}function simd(e){return c.simd(a.bool(e)?e:null)}simd(true);function block(e){if(a.object(e)){if(Array.isArray(e.operation)&&e.operation.every(a.string)){c.block(e.operation,true)}else{throw a.invalidParameterError("operation","Array<string>",e.operation)}}else{throw a.invalidParameterError("options","object",e)}}function unblock(e){if(a.object(e)){if(Array.isArray(e.operation)&&e.operation.every(a.string)){c.block(e.operation,false)}else{throw a.invalidParameterError("operation","Array<string>",e.operation)}}else{throw a.invalidParameterError("options","object",e)}}e.exports=function(e){e.cache=cache;e.concurrency=concurrency;e.counters=counters;e.simd=simd;e.format=h;e.interpolators=f;e.versions=u;e.vendor=p;e.queue=d;e.block=block;e.unblock=unblock}},110:(e,t,i)=>{"use strict";var r=i(199);var n=Array.prototype.concat;var o=Array.prototype.slice;var s=e.exports=function swizzle(e){var t=[];for(var i=0,s=e.length;i<s;i++){var a=e[i];if(r(a)){t=n.call(t,o.call(a))}else{t.push(a)}}return t};s.wrap=function(e){return function(){return e(s(arguments))}}},251:(e,t,i)=>{e.exports=require(i.ab+"build/Release/sharp-darwin-arm64v8.node")},317:e=>{"use strict";e.exports=require("child_process")},434:e=>{"use strict";e.exports=require("events")},896:e=>{"use strict";e.exports=require("fs")},857:e=>{"use strict";e.exports=require("os")},928:e=>{"use strict";e.exports=require("path")},203:e=>{"use strict";e.exports=require("stream")},23:e=>{"use strict";e.exports=require("util")},464:e=>{"use strict";e.exports=JSON.parse('{"name":"sharp","description":"High performance Node.js image processing, the fastest module to resize JPEG, PNG, WebP, GIF, AVIF and TIFF images","version":"0.32.6","author":"Lovell Fuller <<EMAIL>>","homepage":"https://github.com/lovell/sharp","contributors":["Pierre Inglebert <<EMAIL>>","Jonathan Ong <<EMAIL>>","Chanon Sajjamanochai <<EMAIL>>","Juliano Julio <<EMAIL>>","Daniel Gasienica <<EMAIL>>","Julian Walker <<EMAIL>>","Amit Pitaru <<EMAIL>>","Brandon Aaron <<EMAIL>>","Andreas Lind <<EMAIL>>","Maurus Cuelenaere <<EMAIL>>","Linus Unnebäck <<EMAIL>>","Victor Mateevitsi <<EMAIL>>","Alaric Holloway <<EMAIL>>","Bernhard K. Weisshuhn <<EMAIL>>","Chris Riley <<EMAIL>>","David Carley <<EMAIL>>","John Tobin <<EMAIL>>","Kenton Gray <<EMAIL>>","Felix Bünemann <<EMAIL>>","Samy Al Zahrani <<EMAIL>>","Chintan Thakkar <<EMAIL>>","F. Orlando Galashan <<EMAIL>>","Kleis Auke Wolthuizen <<EMAIL>>","Matt Hirsch <<EMAIL>>","Matthias Thoemmes <<EMAIL>>","Patrick Paskaris <<EMAIL>>","Jérémy Lal <<EMAIL>>","Rahul Nanwani <<EMAIL>>","Alice Monday <<EMAIL>>","Kristo Jorgenson <<EMAIL>>","YvesBos <<EMAIL>>","Guy Maliar <<EMAIL>>","Nicolas Coden <<EMAIL>>","Matt Parrish <<EMAIL>>","Marcel Bretschneider <<EMAIL>>","Matthew McEachen <<EMAIL>>","Jarda Kotěšovec <<EMAIL>>","Kenric D\'Souza <<EMAIL>>","Oleh Aleinyk <<EMAIL>>","Marcel Bretschneider <<EMAIL>>","Andrea Bianco <<EMAIL>>","Rik Heywood <<EMAIL>>","Thomas Parisot <<EMAIL>>","Nathan Graves <<EMAIL>>","Tom Lokhorst <<EMAIL>>","Espen Hovlandsdal <<EMAIL>>","Sylvain Dumont <<EMAIL>>","Alun Davies <<EMAIL>>","Aidan Hoolachan <<EMAIL>>","Axel Eirola <<EMAIL>>","Freezy <<EMAIL>>","Daiz <<EMAIL>>","Julian Aubourg <<EMAIL>>","Keith Belovay <<EMAIL>>","Michael B. Klein <<EMAIL>>","Jordan Prudhomme <<EMAIL>>","Ilya Ovdin <<EMAIL>>","Andargor <<EMAIL>>","Paul Neave <<EMAIL>>","Brendan Kennedy <<EMAIL>>","Brychan Bennett-Odlum <**************>","Edward Silverton <<EMAIL>>","Roman Malieiev <<EMAIL>>","Tomas Szabo <<EMAIL>>","Robert O\'Rourke <<EMAIL>>","Guillermo Alfonso Varela Chouciño <<EMAIL>>","Christian Flintrup <<EMAIL>>","Manan Jadhav <<EMAIL>>","Leon Radley <<EMAIL>>","alza54 <<EMAIL>>","Jacob Smith <<EMAIL>>","Michael Nutt <<EMAIL>>","Brad Parham <<EMAIL>>","Taneli Vatanen <<EMAIL>>","Joris Dugué <<EMAIL>>","Chris Banks <<EMAIL>>","Ompal Singh <<EMAIL>>","Brodan <<EMAIL>","Ankur Parihar <<EMAIL>>","Brahim Ait elhaj <<EMAIL>>","Mart Jansink <<EMAIL>>","Lachlan Newman <<EMAIL>>"],"scripts":{"install":"(node install/libvips && node install/dll-copy && prebuild-install) || (node install/can-compile && node-gyp rebuild && node install/dll-copy)","clean":"rm -rf node_modules/ build/ vendor/ .nyc_output/ coverage/ test/fixtures/output.*","test":"npm run test-lint && npm run test-unit && npm run test-licensing && npm run test-types","test-lint":"semistandard && cpplint","test-unit":"nyc --reporter=lcov --reporter=text --check-coverage --branches=100 mocha","test-licensing":"license-checker --production --summary --onlyAllow=\\"Apache-2.0;BSD;ISC;MIT\\"","test-leak":"./test/leak/leak.sh","test-types":"tsd","docs-build":"node docs/build && node docs/search-index/build","docs-serve":"cd docs && npx serve","docs-publish":"cd docs && npx firebase-tools deploy --project pixelplumbing --only hosting:pixelplumbing-sharp"},"main":"lib/index.js","types":"lib/index.d.ts","files":["binding.gyp","install/**","lib/**","src/**"],"repository":{"type":"git","url":"git://github.com/lovell/sharp"},"keywords":["jpeg","png","webp","avif","tiff","gif","svg","jp2","dzi","image","resize","thumbnail","crop","embed","libvips","vips"],"dependencies":{"color":"^4.2.3","detect-libc":"^2.0.2","node-addon-api":"^6.1.0","prebuild-install":"^7.1.1","semver":"^7.5.4","simple-get":"^4.0.1","tar-fs":"^3.0.4","tunnel-agent":"^0.6.0"},"devDependencies":{"@types/node":"*","async":"^3.2.4","cc":"^3.0.1","exif-reader":"^1.2.0","extract-zip":"^2.0.1","icc":"^3.0.0","jsdoc-to-markdown":"^8.0.0","license-checker":"^25.0.1","mocha":"^10.2.0","mock-fs":"^5.2.0","nyc":"^15.1.0","prebuild":"^12.0.0","semistandard":"^16.0.1","tsd":"^0.29.0"},"license":"Apache-2.0","config":{"libvips":"8.14.5","integrity":{"darwin-arm64v8":"sha512-1QZzICfCJd4wAO0P6qmYI5e5VFMt9iCE4QgefI8VMMbdSzjIXA9L/ARN6pkMQPZ3h20Y9RtJ2W1skgCsvCIccw==","darwin-x64":"sha512-sMIKMYXsdU9FlIfztj6Kt/SfHlhlDpP0Ups7ftVFqwjaszmYmpI9y/d/q3mLb4jrzuSiSUEislSWCwBnW7MPTw==","linux-arm64v8":"sha512-CD8owELzkDumaom+O3jJ8fKamILAQdj+//KK/VNcHK3sngUcFpdjx36C8okwbux9sml/T7GTB/gzpvReDrAejQ==","linux-armv6":"sha512-wk6IPHatDFVWKJy7lI1TJezHGHPQut1wF2bwx256KlZwXUQU3fcVcMpV1zxXjgLFewHq2+uhyMkoSGBPahWzlA==","linux-armv7":"sha512-HEZC9KYtkmBK5rUR2MqBhrVarnQVZ/TwLUeLkKq0XuoM2pc/eXI6N0Fh5NGEFwdXI2XE8g1ySf+OYS6DDi+xCQ==","linux-x64":"sha512-SlFWrITSW5XVUkaFPQOySAaSGXnhkGJCj8X2wGYYta9hk5piZldQyMp4zwy0z6UeRu1qKTKtZvmq28W3Gnh9xA==","linuxmusl-arm64v8":"sha512-ga9iX7WUva3sG/VsKkOD318InLlCfPIztvzCZKZ2/+izQXRbQi8VoXWMHgEN4KHACv45FTl7mJ/8CRqUzhS8wQ==","linuxmusl-x64":"sha512-yeaHnpfee1hrZLok2l4eFceHzlfq8gN3QOu0R4Mh8iMK5O5vAUu97bdtxeZZeJJvHw8tfh2/msGi0qysxKN8bw==","win32-arm64v8":"sha512-kR91hy9w1+GEXK56hLh51+hBCBo7T+ijM4Slkmvb/2PsYZySq5H7s61n99iDYl6kTJP2y9sW5Xcvm3uuXDaDgg==","win32-ia32":"sha512-HrnofEbzHNpHJ0vVnjsTj5yfgVdcqdWshXuwFO2zc8xlEjA83BvXZ0lVj9MxPxkxJ2ta+/UlLr+CFzc5bOceMw==","win32-x64":"sha512-BwKckinJZ0Fu/EcunqiLPwOLEBWp4xf8GV7nvmVuKKz5f6B+GxoA2k9aa2wueqv4r4RJVgV/aWXZWFKOIjre/Q=="},"runtime":"napi","target":7},"engines":{"node":">=14.15.0"},"funding":{"url":"https://opencollective.com/libvips"},"binary":{"napi_versions":[7]},"semistandard":{"env":["mocha"]},"cc":{"linelength":"120","filter":["build/include"]},"tsd":{"directory":"test/types/"}}')}};var t={};function __nccwpck_require__(i){var r=t[i];if(r!==undefined){return r.exports}var n=t[i]={exports:{}};var o=true;try{e[i](n,n.exports,__nccwpck_require__);o=false}finally{if(o)delete t[i]}return n.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var i=__nccwpck_require__(539);module.exports=i})();