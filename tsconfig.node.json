{"extends": "@electron-toolkit/tsconfig/tsconfig.node.json", "include": ["vite.desktop.*", "vite.app.*", "vite.config.*", "desktop/src/main/**/*", "desktop/src/preload/**/*", "desktop/src/common/**/*", "env.d.ts", "vite-env.d.ts"], "exclude": ["node_modules", "app/dist", "app/android", "app/ios"], "compilerOptions": {"composite": true, "moduleResolution": "bundler", "types": ["electron-vite/node"], "baseUrl": ".", "paths": {"@common/*": ["desktop/src/common/*"], "@main/*": ["desktop/src/main/*"], "@/*": ["shared/*"]}}}