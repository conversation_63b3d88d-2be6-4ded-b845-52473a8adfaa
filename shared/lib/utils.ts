import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
export { cva } from 'class-variance-authority'

/**
 * 合并类名工具函数
 * 结合clsx和tailwind-merge，用于处理条件类名和合并tailwind类
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 延迟执行函数
 * @param ms 延迟时间（毫秒）
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
