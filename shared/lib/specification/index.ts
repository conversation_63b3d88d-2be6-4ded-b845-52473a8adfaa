import type { VideoSpecification } from './content-type/video/video-specification'
import { getVideoSpecification } from './content-type/video/video-specification'
import type { ImageTextSpecification } from './content-type/image-text/image-text-specification'
import { getImageTextSpecification } from './content-type/image-text/image-text-specification'
import type { ArticleSpecification } from './content-type/article/article-specification'
import { getArticleSpecification } from './content-type/article/article-specification'
import { useMemo } from 'react'
import type { Platform } from '@/lib/platform'

export function usePlatformSpecification() {
  return useMemo(
    () => ({
      getVideoSpecification,
      getImageTextSpecification,
      getArticleSpecification,
    }),
    [],
  )
}

export interface PlatformSpecifications<
  T extends ArticleSpecification | ImageTextSpecification | VideoSpecification,
> {
  platform: Platform
  specification: T
}
