import { specifications } from './platforms'
import type { TimeSpan } from '@/utils'
import { defaultSpecification } from './default'
import type { ByteSize } from '@/utils'
import type { PixelSize } from '@/utils'
import type { Platform } from '@/lib/platform'
import type { PlatformSpecifications } from '@/lib/specification'

/**
 * 平台规格，只定义平台是否支持某字段、长度限制等信息，没有任何的技术实现
 */
export interface ArticleSpecification {
  // 是否支持本内容类型
  contentTypeSupport: boolean
  // 标题最小长度
  titleMinLength: number | null
  // 标题最大长度
  titleMaxLength: number | null
  // 正文最小长度
  contentMinLength: number | null
  // 正文最大长度
  contentMaxLength: number | null
  // 是否需要封面
  coverRequired: boolean
  // 封面最小像素大小
  coverMinPixelSize: PixelSize | null
  // 封面最大字节大小
  coverMaxByteSize: ByteSize | null
  // 是否需要竖版封面
  verticalCoverRequired: boolean
  // 是否支持首发
  firstPublishSupport: boolean
  // 是否支持分类
  categorySupport: boolean
  // 是否支持位置
  locationSupport: boolean
  // 是否支持定时发布
  scheduledTimeSupport: boolean
  // 定时发布最小推迟时间
  scheduledTimeMinTimeSpan: TimeSpan | null
  // 定时发布最大推迟时间
  scheduledTimeMaxTimeSpan: TimeSpan | null
  // 是否支持话题
  topicSupport: boolean
  // 话题是否必填
  topicRequired: boolean
  // 话题最大数量
  topicMaxCount: number | null
  // 支持草稿
  draftPublishSupport: boolean
}

export function getArticleSpecification(platform: Platform): ArticleSpecification {
  const result = specifications[platform.name]
  return result ?? { ...defaultSpecification, contentTypeSupport: false }
}

export function getArticlePlatformSpecifications(platforms: Platform[]) {
  return platforms.map(
    (platform) =>
      ({
        platform,
        specification: getArticleSpecification(platform),
      }) satisfies PlatformSpecifications<ArticleSpecification> as PlatformSpecifications<ArticleSpecification>,
  )
}
