import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 1,
  titleMaxLength: 32,
  contentMaxLength: 10000,
  coverRequired: true,
  coverMaxByteSize: ByteSize.fromMB(20),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
