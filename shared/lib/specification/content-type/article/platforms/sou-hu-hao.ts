import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { TimeSpan } from '@/utils'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 5,
  titleMaxLength: 72,
  contentMaxLength: 20000,
  coverRequired: true,
  coverMaxByteSize: ByteSize.fromMB(10),
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromMinutes(30),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(4),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
