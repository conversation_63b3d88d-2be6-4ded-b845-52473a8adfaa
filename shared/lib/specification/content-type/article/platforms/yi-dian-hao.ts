import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { TimeSpan } from '@/utils'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 5,
  titleMaxLength: 64,
  contentMaxLength: 20000,
  coverRequired: true,
  coverMaxByteSize: ByteSize.fromMB(6),
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(3),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
