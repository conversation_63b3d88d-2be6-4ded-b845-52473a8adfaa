import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 5,
  titleMaxLength: 64,
  contentMaxLength: 20000,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(4),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
