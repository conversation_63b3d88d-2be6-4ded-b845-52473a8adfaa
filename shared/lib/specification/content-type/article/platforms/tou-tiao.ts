import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { TimeSpan } from '@/utils'
import { PixelSize } from '@/utils'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 2,
  titleMaxLength: 30,
  contentMinLength: 1,
  contentMaxLength: 20000,
  coverRequired: true,
  coverMinPixelSize: PixelSize.from(372, 248),
  coverMaxByteSize: ByteSize.fromMB(20),
  firstPublishSupport: true,
  locationSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
