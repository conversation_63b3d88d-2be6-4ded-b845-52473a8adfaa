import TouTiao from './tou-tiao'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './bai-jia-hao'
import Qi<PERSON><PERSON><PERSON> from './qi-e-hao'
import Sou<PERSON>u<PERSON>ao from './sou-hu-hao'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './yi-dian-hao'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './da-yu-hao'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './wang-yi-hao'
import <PERSON>hiHu from './zhi-hu'
import AiQiYi from './ai-qi-yi'
import XinLangWeiBo from './xin-lang-wei-bo'
import type { PlatformName} from '@/lib/platform';
import { platformNames } from '@/lib/platform'
import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'

const UnknownSpecification = {
  ...defaultSpecification,
  contentTypeSupport: false,
}

type PlatformSpecificationMap = {
  [K in PlatformName]: ArticleSpecification;
};

export const specifications: Partial<PlatformSpecificationMap> = {
  [platformNames.TouTiaoHao]: Tou<PERSON><PERSON><PERSON>,
  [platformNames.BaiJiaHao]: BaiJiaHao,
  [platformNames.QiEHao]: QiEHao,
  [platformNames.SouHuHao]: SouHuHao,
  [platformNames.YiDianHao]: YiDianHao,
  [platformNames.DaYuHao]: DaYuHao,
  [platformNames.WangYiHao]: WangYiHao,
  [platformNames.ZhiHu]: ZhiHu,
  [platformNames.AiQiYi]: AiQiYi,
  [platformNames.XinLangWeiBo]: XinLangWeiBo,
  '未知': UnknownSpecification,
}
