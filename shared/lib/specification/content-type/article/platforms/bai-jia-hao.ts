import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { PixelSize } from '@/utils'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 8,
  titleMaxLength: 30,
  contentMinLength: 9,
  contentMaxLength: 10000,
  coverRequired: true,
  coverMinPixelSize: PixelSize.from(372, 248),
  coverMaxByteSize: ByteSize.fromMB(5),
  categorySupport: true,
  verticalCoverRequired: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
