import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { PixelSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 5,
  titleMaxLength: 30,
  contentMaxLength: 20000,
  coverRequired: true,
  coverMinPixelSize: PixelSize.from(160, 160),
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
