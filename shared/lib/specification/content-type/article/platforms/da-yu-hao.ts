import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { TimeSpan } from '@/utils'
import { PixelSize } from '@/utils'

export default {
  ...defaultSpecification,
  titleMinLength: 5,
  titleMaxLength: 50,
  contentMinLength: 9,
  contentMaxLength: 20000,
  verticalCoverRequired: true,
  coverRequired: true,
  coverMinPixelSize: PixelSize.from(200, 96),
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
} satisfies ArticleSpecification as ArticleSpecification
