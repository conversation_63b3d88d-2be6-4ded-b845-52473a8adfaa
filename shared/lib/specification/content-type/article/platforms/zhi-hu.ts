import { defaultSpecification } from '../default'
import type { ArticleSpecification } from '../article-specification'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  contentMinLength: 9,
  contentMaxLength: 10000,
  titleMinLength: 9,
  titleMaxLength: 100,
  coverMaxByteSize: ByteSize.fromMB(10),
  topicSupport: true,
  topicRequired: true,
  topicMaxCount: 3,
  draftPublishSupport: true,
} satisfies ArticleSpecification as ArticleSpecification
