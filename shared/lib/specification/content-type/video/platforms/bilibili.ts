import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils/byte-size'
import { TimeSpan } from '@/utils/time-span'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(16),
  videoMaxDuration: TimeSpan.fromHours(10),
  coverMaxByteSize: ByteSize.fromMB(5),
  titleSupport: true,
  titleRequired: true,
  titleMaxLength: 80,
  descriptionMaxLength: 2000,
  originalSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(15),
  locationSupport: false,
  categorySupport: true,
  categoryRequired: true,
  tagSupport: true,
  tagMinCount: 1,
  tagMaxCount: 10,
  tagMaxLength: 20,
  publishByCloudSupport: true,
  privatePublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
