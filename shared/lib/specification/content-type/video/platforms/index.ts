import DouYin from './dou-yin';
import Tou<PERSON>iao from './tou-tiao';
import BiliBili from './bilibili';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './bai-jia-hao';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './xiao-hong-shu';
import <PERSON><PERSON><PERSON><PERSON> from './kuai-shou';
import Xin<PERSON>angWeiBo from './xin-lang-wei-bo';
import WeiXinShiPinHao from './wei-xin-shi-pin-hao';
import ZhiHu from './zhi-hu';
import <PERSON><PERSON>Hao from './qi-e-hao';
import <PERSON>QiYi from './ai-qi-yi';
import <PERSON><PERSON><PERSON><PERSON>ao from './wang-yi-hao';
import <PERSON><PERSON>ian<PERSON>ao from './yi-dian-hao';
import SouHuHao from './sou-hu-hao';
import TengXunWeiShi from './teng-xun-wei-shi';
import type { VideoSpecification } from '../video-specification';
import { defaultSpecification } from '../default';
import { platformNames, type PlatformName } from '@/lib/platform';

const UnknownSpecification: VideoSpecification = {
  ...defaultSpecification,
  contentTypeSupport: false,
};

type PlatformSpecificationMap = {
  [K in PlatformName]: VideoSpecification;
};

export const specifications: Partial<PlatformSpecificationMap> = {
  [platformNames.DouYin]: DouYin,
  [platformNames.TouTiaoHao]: TouTiao,
  [platformNames.BiliBili]: BiliBili,
  [platformNames.BaiJiaHao]: BaiJiaHao,
  [platformNames.XiaoHongShu]: XiaoHongShu,
  [platformNames.KuaiShou]: KusiShou,
  [platformNames.XinLangWeiBo]: XinLangWeiBo,
  [platformNames.WeiXinShiPinHao]: WeiXinShiPinHao,
  [platformNames.ZhiHu]: ZhiHu,
  [platformNames.QiEHao]: QiEHao,
  [platformNames.AiQiYi]: AiQiYi,
  [platformNames.WangYiHao]: WangYiHao,
  [platformNames.YiDianHao]: YiDianHao,
  [platformNames.SouHuHao]: SouHuHao,
  [platformNames.TengXunWeiShi]: TengXunWeiShi,
  未知: UnknownSpecification,
};
