import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(12),
  videoNeedAudioTrack: true,
  coverMaxByteSize: ByteSize.fromMB(5),
  titleSupport: true,
  titleMaxLength: 30,
  descriptionMaxLength: 100,
  topicMaxCount: 5,
  topicMaxLength: 30,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  locationSupport: true,
  draftPublishSupport: true,
  publishByCloudSupport: true,
} satisfies VideoSpecification as VideoSpecification
