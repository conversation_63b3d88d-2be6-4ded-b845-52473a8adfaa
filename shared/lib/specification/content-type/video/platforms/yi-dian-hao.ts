import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  coverMaxByteSize: ByteSize.fromMB(6),
  descriptionMaxLength: 200,
  titleSupport: true,
  titleMinLength: 5,
  titleMaxLength: 30,
  originalSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(5),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  categorySupport: true,
  categoryRequired: true,
  tagSupport: true,
  tagMaxCount: 8,
  tagMaxLength: 8,
  draftPublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
