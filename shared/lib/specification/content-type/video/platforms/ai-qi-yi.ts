import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { TimeSpan } from '@/utils'
import { PixelSize } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxDuration: TimeSpan.fromMinutes(10),
  coverMaxPixelSize: PixelSize.from(5000, 5000),
  titleSupport: true,
  titleRequired: true,
  titleMaxLength: 30,
  originalSupport: true,
  categorySupport: true,
  tagSupport: true,
  tagMinCount: 1,
  tagMaxCount: 10,
  tagMaxLength: 20,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(15),
  draftPublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
