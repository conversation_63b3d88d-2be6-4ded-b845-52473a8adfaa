import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  videoMaxByteSize: ByteSize.fromGB(8),
  videoMaxDuration: TimeSpan.fromMinutes(30),
  coverMaxByteSize: ByteSize.fromMB(5),
  descriptionMaxLength: 500,
  topicMaxCount: 4,
  topicMaxLength: 500,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
  locationSupport: true,
  privatePublishSupport: true,
  publishByCloudSupport: true,
} satisfies VideoSpecification as VideoSpecification
