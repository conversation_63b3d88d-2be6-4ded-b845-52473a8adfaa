import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'
import { PixelSize } from '@/utils'

export default {
  ...defaultSpecification,
  videoMaxByteSize: ByteSize.fromMB(300),
  videoMaxDuration: TimeSpan.fromMinutes(3),
  videoEncodeRestricts: ['AVC'],
  coverMinPixelSize: PixelSize.from(300, 400),
  descriptionMaxLength: 200,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
} satisfies VideoSpecification as VideoSpecification
