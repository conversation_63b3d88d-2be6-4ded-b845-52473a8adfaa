import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(16),
  videoMaxDuration: TimeSpan.fromHours(10),
  coverMaxByteSize: ByteSize.fromMB(20),
  descriptionMaxLength: 200,
  categorySupport: true,
  categoryRequired: true,
  tagSupport: true,
  tagMinCount: 2,
  tagMaxCount: 9,
  tagMaxLength: 8,
  titleSupport: true,
  titleMinLength: 5,
  titleMaxLength: 60,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromMinutes(0),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  draftPublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
