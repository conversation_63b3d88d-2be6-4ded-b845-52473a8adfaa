import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(32),
  videoMaxDuration: TimeSpan.fromMinutes(180),
  coverMaxByteSize: ByteSize.fromMB(20),
  titleSupport: true,
  titleRequired: true,
  titleMaxLength: 80,
  descriptionMaxLength: 400,
  originalSupport: true,
  draftPublishSupport: true,
  publishByCloudSupport: true,
} satisfies VideoSpecification as VideoSpecification
