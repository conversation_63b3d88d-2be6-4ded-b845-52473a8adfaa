import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(5),
  coverMaxByteSize: ByteSize.fromMB(20),
  descriptionMaxLength: 30,
  titleSupport: true,
  titleMinLength: 5,
  titleMaxLength: 40,
  originalSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromMinutes(30),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
  categorySupport: true,
  categoryRequired: true,
  tagSupport: true,
  tagMinCount: 1,
  tagMaxCount: 6,
  draftPublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
