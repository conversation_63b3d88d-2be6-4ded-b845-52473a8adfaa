import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  videoMaxByteSize: ByteSize.fromGB(15),
  coverMaxByteSize: ByteSize.fromMB(5),
  titleSupport: true,
  titleMaxLength: 30,
  descriptionMaxLength: 5000,
  locationSupport: true,
  originalSupport: true,
  publishByCloudSupport: true,
} satisfies VideoSpecification as VideoSpecification
