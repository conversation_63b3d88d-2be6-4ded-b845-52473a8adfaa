import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  videoMaxByteSize: ByteSize.fromGB(20),
  videoMaxDuration: TimeSpan.fromMinutes(60),
  titleSupport: true,
  titleMaxLength: 20,
  descriptionMaxLength: 1000,
  topicMaxCount: 10,
  topicMaxLength: 14,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
  locationSupport: true,
  privatePublishSupport: true,
  publishByCloudSupport: true,
} satisfies VideoSpecification as VideoSpecification
