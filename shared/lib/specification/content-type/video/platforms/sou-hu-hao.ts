import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(4),
  coverMaxByteSize: ByteSize.fromMB(10),
  titleSupport: true,
  titleRequired: true,
  titleMinLength: 5,
  titleMaxLength: 72,
  descriptionMinLength: 5,
  descriptionMaxLength: 200,
  categorySupport: true,
  categoryRequired: true,
  draftPublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
