import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  videoMaxByteSize: ByteSize.fromGB(4),
  videoMaxDuration: TimeSpan.fromMinutes(120),
  coverMaxByteSize: ByteSize.fromKB(512),
  descriptionMaxLength: 1000,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromMinutes(0),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(30),
  originalSupport: true,
  locationSupport: true,
  draftPublishSupport: true,
  publishByCloudSupport: true,
} satisfies VideoSpecification as VideoSpecification
