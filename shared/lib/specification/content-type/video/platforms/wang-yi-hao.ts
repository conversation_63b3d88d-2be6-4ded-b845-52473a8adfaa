import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  isModern: false,
  videoMaxByteSize: ByteSize.fromGB(8),
  coverMaxByteSize: ByteSize.fromMB(10),
  titleSupport: true,
  titleMinLength: 5,
  titleMaxLength: 30,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(5),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(7),
  categorySupport: true,
  categoryRequired: true,
  tagSupport: true,
  tagMinCount: 3,
  tagMaxCount: 5,
  draftPublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
