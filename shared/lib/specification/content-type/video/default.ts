import type { VideoSpecification } from './video-specification'

export const defaultSpecification = {
  isModern: true,
  contentTypeSupport: true,
  videoMaxDuration: null,
  videoMaxByteSize: null,
  videoEncodeRestricts: null,
  videoNeedAudioTrack: false,
  coverMaxByteSize: null,
  coverMinPixelSize: null,
  coverMaxPixelSize: null,
  titleSupport: false,
  titleRequired: false,
  titleMinLength: null,
  titleMaxLength: null,
  descriptionMinLength: null,
  descriptionMaxLength: null,
  topicMinCount: null,
  topicMaxCount: null,
  topicMaxLength: null,
  scheduledTimeSupport: false,
  scheduledTimeMinTimeSpan: null,
  scheduledTimeMaxTimeSpan: null,
  originalSupport: false,
  locationSupport: false,
  categorySupport: false,
  categoryRequired: false,
  tagSupport: false,
  tagMinCount: null,
  tagMaxCount: null,
  tagMaxLength: null,
  draftPublishSupport: false,
  privatePublishSupport: false,
  publishByCloudSupport: false,
} satisfies VideoSpecification
