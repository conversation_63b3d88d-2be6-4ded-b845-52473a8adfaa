import { specifications } from './platforms'
import { defaultSpecification } from './default'
import type { ByteSize } from '@/utils'
import type { TimeSpan } from '@/utils'
import type { Platform } from '@/lib/platform'
import type { PlatformSpecifications } from '@/lib/specification'
import type { PixelSize } from '@/utils'

/**
 * 平台规格，只定义平台是否支持某字段、长度限制等信息，没有任何的技术实现
 */
export interface VideoSpecification {
  // 是否支持本内容类型
  contentTypeSupport: boolean
  // 是否新潮平台
  isModern: boolean
  // 视频最大体积
  videoMaxByteSize: ByteSize | null
  // 视频最大时长
  videoMaxDuration: TimeSpan | null
  // 视频编码限制
  videoEncodeRestricts: string[] | null
  // 视频需要音轨
  videoNeedAudioTrack: boolean
  // 封面最大体积
  coverMaxByteSize: ByteSize | null
  // 封面最小像素大小
  coverMinPixelSize: PixelSize | null
  // 封面最大像素大小
  coverMaxPixelSize: PixelSize | null
  // 是否支持标题
  titleSupport: boolean
  // 标题是否必填
  titleRequired: boolean
  // 标题最小长度
  titleMinLength: number | null
  // 标题最大长度
  titleMaxLength: number | null
  // 描述最小长度
  descriptionMinLength: number | null
  // 描述最大长度
  descriptionMaxLength: number | null
  // 话题最小数量
  topicMinCount: number | null
  // 话题最大数量
  topicMaxCount: number | null
  // 单个话题最大字数
  topicMaxLength: number | null
  // 是否支持定时发布
  scheduledTimeSupport: boolean
  // 定时发布最小推迟时间
  scheduledTimeMinTimeSpan: TimeSpan | null
  // 定时发布最大推迟时间
  scheduledTimeMaxTimeSpan: TimeSpan | null
  // 是否支持原创标识
  originalSupport: boolean
  // 是否支持地理位置
  locationSupport: boolean
  // 是否支持分类
  categorySupport: boolean
  // 分类是否必填
  categoryRequired: boolean
  // 是否支持标签
  tagSupport: boolean
  // 标签最小数量
  tagMinCount: number | null
  // 标签最大数量
  tagMaxCount: number | null
  // 单个标签最大字数
  tagMaxLength: number | null
  // 支持草稿
  draftPublishSupport: boolean
  // 支持私密
  privatePublishSupport: boolean
  // 支持云端发布
  publishByCloudSupport: boolean
}

export function getVideoSpecification(platform: Platform): VideoSpecification {
  const result = specifications[platform.name]
  return result ?? { ...defaultSpecification, contentTypeSupport: false }
}

export function getVideoPlatformSpecifications(platforms: Platform[]) {
  return platforms.map(
    (platform) =>
      ({
        platform,
        specification: getVideoSpecification(platform),
      }) as PlatformSpecifications<VideoSpecification>,
  )
}

export function isModernPlatform(platform: Platform) {
  return getVideoSpecification(platform).isModern
}
