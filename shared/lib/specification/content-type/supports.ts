import { getArticlePlatformSpecifications } from './article/article-specification'
import { allPlatform } from '@/lib/platform'
import { getImageTextPlatformSpecifications } from './image-text/image-text-specification'
import { getVideoPlatformSpecifications } from './video/video-specification'
import { EditContentType } from '@/types/content-type'

export const articlePlatforms = getArticlePlatformSpecifications(allPlatform)
  .filter((spec) => spec.specification.contentTypeSupport)
  .map((spec) => spec.platform)
export const videoPlatforms = getVideoPlatformSpecifications(allPlatform)
  .filter((spec) => spec.specification.contentTypeSupport)
  .map((spec) => spec.platform)
export const cloudVideoPlatforms = getVideoPlatformSpecifications(allPlatform)
  .filter(
    (spec) => spec.specification.contentTypeSupport && spec.specification.publishByCloudSupport,
  )
  .map((spec) => spec.platform)
export const imageTextPlatforms = getImageTextPlatformSpecifications(allPlatform)
  .filter((spec) => spec.specification.contentTypeSupport)
  .map((spec) => spec.platform)

export const modernVideoPlatforms = getVideoPlatformSpecifications(allPlatform)
  .filter((spec) => spec.specification.contentTypeSupport && spec.specification.isModern)
  .map((spec) => spec.platform)

export const classicVideoPlatforms = getVideoPlatformSpecifications(allPlatform)
  .filter((spec) => spec.specification.contentTypeSupport && !spec.specification.isModern)
  .map((spec) => spec.platform)

export function getSupportPlatforms(contentType: EditContentType) {
  switch (contentType) {
    case EditContentType.Article:
      return articlePlatforms
    case EditContentType.Video:
      return videoPlatforms
    case EditContentType.ImageText:
      return imageTextPlatforms
    default:
      return []
  }
}
