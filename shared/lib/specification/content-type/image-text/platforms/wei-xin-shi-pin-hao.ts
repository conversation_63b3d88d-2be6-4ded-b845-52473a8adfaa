import { type ImageTextSpecification } from '../image-text-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  imageMaxByteSize: ByteSize.fromKB(512),
  descriptionMaxLength: 1000,
  titleSupport: true,
  titleRequired: true,
  titleMaxLength: 22,
  musicSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(30),
} satisfies ImageTextSpecification as ImageTextSpecification
