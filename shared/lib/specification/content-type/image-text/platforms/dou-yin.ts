import { type ImageTextSpecification } from '../image-text-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  imageMaxByteSize: ByteSize.fromMB(50),
  descriptionMaxLength: 1000,
  topicMaxCount: 5,
  topicMaxLength: 500,
  titleSupport: true,
  titleMaxLength: 20,
  coverSupport: true,
  coverRequired: true,
  musicSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
} satisfies ImageTextSpecification as ImageTextSpecification
