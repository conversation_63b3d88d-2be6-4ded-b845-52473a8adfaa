import DouYin from './dou-yin'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './xiao-hong-shu'
import <PERSON><PERSON><PERSON><PERSON> from './kuai-shou'
import XinLangWeiBo from './xin-lang-wei-bo'
import WeiXinShiPinHao from './wei-xin-shi-pin-hao'
import { platformNames } from '@/lib/platform'

export const specifications = {
  [platformNames.DouYin]: <PERSON>u<PERSON>in,
  [platformNames.XiaoHongShu]: XiaoHongShu,
  [platformNames.KuaiShou]: KusiShou,
  [platformNames.XinLangWeiBo]: XinLangWeiBo,
  [platformNames.WeiXinShiPinHao]: WeiXinShiPinHao,
}
