import { type ImageTextSpecification } from '../image-text-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  imageMaxByteSize: ByteSize.fromMB(20),
  descriptionMaxLength: 1000,
  topicMaxCount: 10,
  topicMaxLength: 14,
  titleSupport: true,
  titleMaxLength: 20,
  coverSupport: true,
  coverRequired: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
} satisfies ImageTextSpecification as ImageTextSpecification
