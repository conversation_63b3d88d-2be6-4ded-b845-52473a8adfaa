import { type ImageTextSpecification } from '../image-text-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@/utils'
import { TimeSpan } from '@/utils'

export default {
  ...defaultSpecification,
  imageMaxByteSize: ByteSize.fromMB(15),
  descriptionMaxLength: 500,
  topicMaxCount: 4,
  topicMaxLength: 500,
  titleSupport: false,
  coverSupport: true,
  coverRequired: true,
  musicSupport: true,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(1),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
} satisfies ImageTextSpecification as ImageTextSpecification
