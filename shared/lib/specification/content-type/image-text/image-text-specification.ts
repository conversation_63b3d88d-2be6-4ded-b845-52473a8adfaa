import { specifications } from './platforms'
import { defaultSpecification } from './default'
import type { ByteSize } from '@/utils'
import type { TimeSpan } from '@/utils'
import type { Platform } from '@/lib/platform'
import type { PlatformSpecifications } from '@/lib/specification'

/**
 * 平台规格，只定义平台是否支持某字段、长度限制等信息，没有任何的技术实现
 */
export interface ImageTextSpecification {
  // 图片是否必填
  imageRequired: boolean
  // 是否支持本内容类型
  contentTypeSupport: boolean
  // 图片最大体积
  imageMaxByteSize: ByteSize | null
  // 标题是否支持
  titleSupport: boolean
  // 标题是否必填
  titleRequired: boolean
  // 标题最大长度
  titleMaxLength: number | null
  // 描述是否必填
  descriptionRequired: boolean
  // 描述最大长度
  descriptionMaxLength: number | null
  // 话题最大数量
  topicMaxCount: number | null
  // 话题最大长度
  topicMaxLength: number | null
  // 封面是否支持
  coverSupport: boolean
  // 封面是否必填
  coverRequired: boolean
  // 是否支持音乐
  musicSupport: boolean
  // 是否支持定时发布
  scheduledTimeSupport: boolean
  // 定时发布最小推迟时间
  scheduledTimeMinTimeSpan: TimeSpan | null
  // 定时发布最大推迟时间
  scheduledTimeMaxTimeSpan: TimeSpan | null
  // 是否支持地理位置
  locationSupport: boolean
}

export function getImageTextSpecification(
  platform: Platform,
): ImageTextSpecification {
  const result = (specifications as Record<string, ImageTextSpecification>)[
    platform.name
  ];
  return result ?? { ...defaultSpecification, contentTypeSupport: false };
}

export function getImageTextPlatformSpecifications(platforms: Platform[]) {
  return platforms.map(
    (platform) =>
      ({
        platform,
        specification: getImageTextSpecification(platform),
      }) as PlatformSpecifications<ImageTextSpecification>,
  )
}
