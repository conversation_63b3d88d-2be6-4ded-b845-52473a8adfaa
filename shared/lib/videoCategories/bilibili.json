[{"id": 160, "parent": 0, "parent_name": "", "name": "生活", "description": "生活", "desc": "生活", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 95, "children": [{"id": 138, "parent": 160, "parent_name": "", "name": "搞笑", "description": "搞笑挑战、剪辑、表演、配音以及各类日常沙雕视频", "desc": "搞笑挑战、剪辑、表演、配音以及各类日常沙雕视频", "intro_original": "能够选择自制的必须是up主个人或工作室自己制作剪辑的视频，除此之外的搬运视频字幕制作，对于视频进行加速、慢放等简易二次创作，在视频中添加前后贴片或者打水印等行为均不被认作自制", "intro_copy": "转载需写明请注明转载作品详细信息原作者、原标题及出处（需为该视频最原始出处，如所标注明显为非原始出处的话会被打回）", "notice": "", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 254, "parent": 160, "parent_name": "", "name": "亲子", "description": "与萌娃、母婴、育儿相关的视频，包括但不限于萌娃日常、萌娃才艺、亲子互动、亲子教育、母婴经验分享、少儿用品分享等", "desc": "与萌娃、母婴、育儿相关的视频，包括但不限于萌娃日常、萌娃才艺、亲子互动、亲子教育、母婴经验分享、少儿用品分享等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 29, "max_video_count": 50, "request_id": ""}, {"id": 250, "parent": 160, "parent_name": "", "name": "出行", "description": "旅行、户外、本地探店相关的视频，如旅行vlog、治愈系风景、城市景点攻略、自驾游、户外露营、徒步、骑行、钓鱼、乡村游、本地探店体验，演出看展等 ", "desc": "旅行、户外、本地探店相关的视频，如旅行vlog、治愈系风景、城市景点攻略、自驾游、户外露营、徒步、骑行、钓鱼、乡村游、本地探店体验，演出看展等 ", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 28, "max_video_count": 50, "request_id": ""}, {"id": 251, "parent": 160, "parent_name": "", "name": "三农", "description": "与农业、农村、农民相关的视频，包括但不限于农村生活、户外打野、种植技术、养殖技术、三农资讯", "desc": "与农业、农村、农民相关的视频，包括但不限于农村生活、户外打野、种植技术、养殖技术、三农资讯", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 26, "max_video_count": 50, "request_id": ""}, {"id": 239, "parent": 160, "parent_name": "", "name": "家居房产", "description": "与买房、装修、居家生活相关的视频，如买房租房、装修改造、智能家居、园艺绿植、居家好物等", "desc": "与买房、装修、居家生活相关的视频，如买房租房、装修改造、智能家居、园艺绿植、居家好物等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 25, "max_video_count": 50, "request_id": ""}, {"id": 161, "parent": 160, "parent_name": "", "name": "手工", "description": "与手工艺、DIY、发明创造相关的视频，例如手工记录、四坑手作、毛毡粘土、手账文具、写字书法、模型、玩具、解压、传统手艺、非遗等", "desc": "与手工艺、DIY、发明创造相关的视频，例如手工记录、四坑手作、毛毡粘土、手账文具、写字书法、模型、玩具、解压、传统手艺、非遗等", "intro_original": "能够选择自制的必须是up主个人或工作室自己制作剪辑的视频，除此之外的搬运视频字幕制作，对于视频进行加速、慢放等简易二次创作，在视频中添加前后贴片或者打水印等行为均不被认作自制；\nUp主可以在简介中简易介绍该手工艺品的制作流程和使用素材", "intro_copy": "转载需写明请注明转载作品详细信息原作者、原标题及出处（需为该视频最原始出处，如所标注明显为非原始出处的话会被打回）", "notice": "【手工类型】+所制作的手工物品名字，例：【手账】、【黏土】", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 162, "parent": 160, "parent_name": "", "name": "绘画", "description": "与绘画、艺术、设计相关的视频，例如绘画记录、数字绘画、手绘、潮流艺术、创意作画、绘画教程、美术分享、动漫、插画等", "desc": "与绘画、艺术、设计相关的视频，例如绘画记录、数字绘画、手绘、潮流艺术、创意作画、绘画教程、美术分享、动漫、插画等", "intro_original": "能够选择自制的必须是up主个人或工作室自己制作的视频，除此之外的搬运视频字幕制作，对于视频进行加速、慢放等简易二次创作，在视频中添加前后贴片或者打水印等行为均不被认作自制；\nUP可以在简介中介绍自己的绘画工具或软件，所画的人物名字及出处，大图地址等", "intro_copy": "转载需写明请注明转载作品详细信息原作者、原标题及出处（需为该视频最原始出处，如所标注明显为非原始出处的话会被打回）", "notice": "【绘画种类】+作品名/教程，例：【板绘】侧面眼睛教程", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 21, "parent": 160, "parent_name": "", "name": "日常", "description": "一般日常向的生活类视频", "desc": "一般日常向的生活类视频", "intro_original": "能够选择自制的必须是up主个人或工作室自己制作剪辑的视频，除此之外的搬运视频字幕制作，对于视频进行加速、慢放等简易二次创作，在视频中添加前后贴片或者打水印等行为均不被认作自制", "intro_copy": "转载需写明请注明转载作品详细信息原作者、原标题及出处（需为该视频最原始出处，如所标注明显为非原始出处的话会被打回）", "notice": "", "copy_right": 0, "show": true, "rank": 4, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 4, "parent": 0, "parent_name": "", "name": "游戏", "description": "游戏", "desc": "游戏", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 90, "children": [{"id": 17, "parent": 4, "parent_name": "", "name": "单机游戏", "description": "以单机或其联机模式为主要内容的相关视频", "desc": "以单机或其联机模式为主要内容的相关视频", "intro_original": "建议在简介和TAG中添加正确的游戏名，以便在分区和搜索中得到更好的展示。\n录制他人直播（包括授权转载、授权录制）不属于自制内容，请选转载。", "intro_copy": "建议在简介和TAG中添加正确的游戏名。\n搬运转载内容必须添加原作者、原链接地址信息。录制他人直播内容必须添加原主播信息、直播时间。\n未添加正确转载、录播信息的稿件可能被打回。", "notice": "【UP主/节目名】+《游戏名》+主要标题+期号", "copy_right": 0, "show": true, "rank": 35, "max_video_count": 50, "request_id": ""}, {"id": 65, "parent": 4, "parent_name": "", "name": "网络游戏", "description": "多人在线游戏为主要内容的相关视频", "desc": "多人在线游戏为主要内容的相关视频", "intro_original": "建议在简介和TAG中添加正确的游戏名，以便在分区和搜索中得到更好的展示。\n录制他人直播（包括授权转载、授权录制）不属于自制内容，请选转载。", "intro_copy": "建议在简介和TAG中添加正确的游戏名。\n搬运转载内容请添加原作者、原链接地址信息。录制他人直播内容请添加原主播信息、直播时间。\n未添加正确转载、录播信息的稿件可能被打回。", "notice": "【UP主/节目名】+《游戏名》+主要标题+期号", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 172, "parent": 4, "parent_name": "", "name": "手机游戏", "description": "手机及平板设备平台上的游戏相关视频", "desc": "手机及平板设备平台上的游戏相关视频", "intro_original": "建议在简介和TAG中添加正确的游戏名及操作平台，以便在分区和搜索中得到更好的展示。\n录制他人直播（包括授权转载、授权录制）不属于自制内容，请选转载。", "intro_copy": "建议在简介和TAG中添加正确的游戏名。\n搬运转载内容请添加原作者、原链接地址信息。录制他人直播内容请添加原主播信息、直播时间。\n未添加正确转载、录播信息的稿件可能被打回。", "notice": "【UP主/节目名】+《游戏名》+主要标题", "copy_right": 0, "show": true, "rank": 25, "max_video_count": 50, "request_id": ""}, {"id": 171, "parent": 4, "parent_name": "", "name": "电子竞技", "description": "电子竞技游戏项目为主要内容的相关视频", "desc": "电子竞技游戏项目为主要内容的相关视频", "intro_original": "建议在简介和TAG中添加正确的游戏名、赛事名称和场次，以便在分区和搜索中得到更好的展示。\n录制他人直播（包括授权转载、授权录制）不属于自制内容，请选转载。", "intro_copy": "建议在简介和TAG中添加正确的游戏名、赛事名称和场次。\n搬运转载内容请添加原作者、原链接地址信息。录制他人直播内容请添加原主播信息、直播时间。\n未添加正确转载、录播信息的稿件可能被打回。", "notice": "【UP主/节目名】+《游戏名》+主要标题+期号", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 100, "request_id": ""}, {"id": 173, "parent": 4, "parent_name": "", "name": "桌游棋牌", "description": "桌游、棋牌、卡牌、聚会游戏等相关视频", "desc": "桌游、棋牌、卡牌、聚会游戏等相关视频", "intro_original": "建议在简介和TAG中添加正确的游戏名，以便在分区和搜索中得到更好的展示。\n录制他人直播（包括授权转载、授权录制）不属于自制内容，请选转载。", "intro_copy": "建议在简介和TAG中添加正确的游戏名。\n搬运转载内容请添加原作者、原链接地址信息。录制他人直播内容请添加原主播信息、直播时间。\n未添加正确转载、录播信息的稿件可能被打回。", "notice": "【UP主/游戏名/节目名】+主要标题", "copy_right": 0, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 136, "parent": 4, "parent_name": "", "name": "音游", "description": "通过配合音乐与节奏而进行的音乐类游戏视频", "desc": "通过配合音乐与节奏而进行的音乐类游戏视频", "intro_original": "建议在简介和TAG中添加正确的游戏名、曲名、难度及操作平台，以便在分区和搜索中得到更好的展示。\n录制他人直播（包括授权转载、授权录制）不属于自制内容，请选转载。", "intro_copy": "建议在简介和TAG中添加正确的游戏名、曲名、难度及操作平台。\n搬运转载内容请添加原作者、原链接地址信息。录制他人直播内容请添加原主播信息、直播时间。\n未添加正确转载、录播信息的稿件可能被打回。", "notice": "【UP主/游戏名】+曲名+难度+程度", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 121, "parent": 4, "parent_name": "", "name": "GMV", "description": "使用游戏内容或CG为素材制作的MV类型的视频", "desc": "使用游戏内容或CG为素材制作的MV类型的视频", "intro_original": "建议在简介和TAG中添加相关素材的游戏名、背景音乐信息，以便在分区和搜索中得到更好的展示。", "intro_copy": "搬运转载内容请添加原作者、原链接地址信息。未添加正确转载信息的稿件可能被打回。", "notice": "【游戏名/风格】主要标题 ", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 19, "parent": 4, "parent_name": "", "name": "Mugen", "description": "使用Mugen引擎制作或与Mugen相关的游戏视频", "desc": "使用Mugen引擎制作或与Mugen相关的游戏视频", "intro_original": "建议在简介和TAG中添加人物、分级、杯赛规则、描述等信息，以便在分区和搜索中得到更好的展示。", "intro_copy": "搬运转载内容请添加原作者、原链接地址信息。 未添加正确转载信息的稿件可能被打回。", "notice": "【Mugen】+杯名+级别+角色名+主要标题", "copy_right": 0, "show": true, "rank": 1, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 5, "parent": 0, "parent_name": "", "name": "娱乐", "description": "娱乐", "desc": "娱乐", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 85, "children": [{"id": 241, "parent": 5, "parent_name": "", "name": "娱乐杂谈", "description": "娱乐人物解读、娱乐热点点评、娱乐行业分析", "desc": "娱乐人物解读、娱乐热点点评、娱乐行业分析", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 70, "max_video_count": 50, "request_id": ""}, {"id": 262, "parent": 5, "parent_name": "", "name": "CP安利", "description": "以安利各类娱乐名人、角色CP之间默契于火花为主题的混剪、解说，观点表达类视频", "desc": "以安利各类娱乐名人、角色CP之间默契于火花为主题的混剪、解说，观点表达类视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 60, "max_video_count": 50, "request_id": ""}, {"id": 263, "parent": 5, "parent_name": "", "name": "颜值安利", "description": "以各类娱乐名人、角色的颜值、气质魅力为核心的混剪视频", "desc": "以各类娱乐名人、角色的颜值、气质魅力为核心的混剪视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 50, "max_video_count": 50, "request_id": ""}, {"id": 242, "parent": 5, "parent_name": "", "name": "娱乐粉丝创作", "description": "粉丝向创作视频", "desc": "粉丝向创作视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 40, "max_video_count": 50, "request_id": ""}, {"id": 264, "parent": 5, "parent_name": "", "name": "娱乐资讯", "description": "具备趣味价值的文化娱乐新闻与动态报道，如名人动态，作品发布，舞台演出，趣闻盘点等", "desc": "具备趣味价值的文化娱乐新闻与动态报道，如名人动态，作品发布，舞台演出，趣闻盘点等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 137, "parent": 5, "parent_name": "", "name": "明星综合", "description": "娱乐圈动态、明星资讯相关", "desc": "娱乐圈动态、明星资讯相关", "intro_original": "简介需要写明素材来源、使用BGM等，列出相关明星。", "intro_copy": "简介需要写明原出处或原作者，列出相关明星。", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 100, "request_id": ""}, {"id": 71, "parent": 5, "parent_name": "", "name": "综艺", "description": "所有综艺相关，全部一手掌握！", "desc": "所有综艺相关，全部一手掌握！", "intro_original": "若为自制综艺的正片，需有该自制综艺的详细介绍和信息\n例：《我们相爱吧》是江苏卫视和韩国MBC电视台联合制作的明星恋爱实境真人秀节目，通过明星组成假想情侣，进行假想情侣\n若为综艺剪辑视频，需要在简介表明该视频中所用到的综艺名称。", "intro_copy": "若为转载综艺的正片，需有该综艺节目的原标题、期数（时间）以及翻译字幕组\n例：【乃木坂不够热】 160412 NOGIBINGO!6 EP01+NOGIROOM4 EP01", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 36, "parent": 0, "parent_name": "", "name": "知识", "description": "知识", "desc": "知识", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 80, "children": [{"id": 201, "parent": 36, "parent_name": "", "name": "科学科普", "description": "以自然科学或基于自然科学思维展开的知识视频", "desc": "以自然科学或基于自然科学思维展开的知识视频", "intro_original": "填写更全面的相关信息，让更多的人能找到你的视频吧！", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 100, "max_video_count": 50, "request_id": ""}, {"id": 124, "parent": 36, "parent_name": "", "name": "社科·法律·心理", "description": "法律/心理/社会学/观点输出类内容等", "desc": "法律/心理/社会学/观点输出类内容等", "intro_original": "填写更全面的相关信息，让更多的人能找到你的视频吧！", "intro_copy": "转载稿件需标明出处，请注明原作者、原作者频道名或原作者投稿地址。\n可对相关内容进行补充说明。\n请勿加入涉政或具较大争议性的文字简介，否则将做打回处理。\n如是系列，也可附带上期视频地址。", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 95, "max_video_count": 50, "request_id": ""}, {"id": 228, "parent": 36, "parent_name": "", "name": "人文历史", "description": "人物/文学/历史/文化/奇闻/艺术等", "desc": "人物/文学/历史/文化/奇闻/艺术等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 91, "max_video_count": 50, "request_id": ""}, {"id": 207, "parent": 36, "parent_name": "", "name": "财经商业", "description": "财经/商业/经济金融/互联网等", "desc": "财经/商业/经济金融/互联网等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 90, "max_video_count": 50, "request_id": ""}, {"id": 208, "parent": 36, "parent_name": "", "name": "校园学习", "description": "学习方法及经验、课程教学、校园干货分享等", "desc": "学习方法及经验、课程教学、校园干货分享等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 85, "max_video_count": 50, "request_id": ""}, {"id": 209, "parent": 36, "parent_name": "", "name": "职业职场", "description": "职场技能、职业分享、行业分析、求职规划等", "desc": "职场技能、职业分享、行业分析、求职规划等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 80, "max_video_count": 50, "request_id": ""}, {"id": 229, "parent": 36, "parent_name": "", "name": "设计·创意", "description": "以设计美学或基于设计思维展开的知识视频", "desc": "以设计美学或基于设计思维展开的知识视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 76, "max_video_count": 50, "request_id": ""}, {"id": 122, "parent": 36, "parent_name": "", "name": "野生技能协会", "description": "技能展示或技能教学分享类视频", "desc": "技能展示或技能教学分享类视频", "intro_original": "可对视频内容进行补充说明，并对所使用的视频素材进行标明。\n如是系列，也可附带上期视频地址。\n请勿加入涉政或具较大争议性的文字简介，否则将做打回处理。", "intro_copy": "转载稿件需标明出处，请注明原作者、原作者频道名或原作者投稿地址。\n可对相关内容进行补充说明。\n请勿加入涉政或具较大争议性的文字简介，否则将做打回处理。\n如是系列，也可附带上期视频地址。", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 75, "max_video_count": 100, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 181, "parent": 0, "parent_name": "", "name": "影视", "description": "影视", "desc": "影视", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 80, "children": [{"id": 182, "parent": 181, "parent_name": "", "name": "影视杂谈", "description": "影视评论、解说、吐槽、科普、配音等", "desc": "影视评论、解说、吐槽、科普、配音等", "intro_original": "建议在简介和TAG中添加正确的影视剧名等信息，以便在分区和搜索中得到更好的展示。", "intro_copy": "建议在简介和TAG中添加正确的影视剧名等信息。\n搬运转载内容必须添加原作者、原链接地址信息。", "notice": "【UP主/节目名】+《影视剧名》（选填）+主要标题", "copy_right": 0, "show": true, "rank": 80, "max_video_count": 50, "request_id": ""}, {"id": 183, "parent": 181, "parent_name": "", "name": "影视剪辑", "description": "对影视素材进行剪辑再创作的视频", "desc": "对影视素材进行剪辑再创作的视频", "intro_original": "建议在简介中添加正确的影视剧名、BGM等信息，以便在分区和搜索中得到更好的展示。CUT不属于自制内容，请选转载。", "intro_copy": "建议在简介中添加正确的影视剧名等信息。搬运转载内容必须添加原作者、原链接地址信息。", "notice": "【剪辑类型】+主要标题", "copy_right": 0, "show": true, "rank": 70, "max_video_count": 50, "request_id": ""}, {"id": 260, "parent": 181, "parent_name": "", "name": "影视整活", "description": "使用影视素材制造的有趣、有梗的创意混剪、配音、特效玩梗视频", "desc": "使用影视素材制造的有趣、有梗的创意混剪、配音、特效玩梗视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 60, "max_video_count": 50, "request_id": ""}, {"id": 259, "parent": 181, "parent_name": "", "name": "AI影像", "description": "分享AI制作的影像作品、创作历程、技术风向", "desc": "分享AI制作的影像作品、创作历程、技术风向", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 50, "max_video_count": 50, "request_id": ""}, {"id": 184, "parent": 181, "parent_name": "", "name": "预告·资讯", "description": "影视类相关资讯，预告，花絮等视频", "desc": "影视类相关资讯，预告，花絮等视频", "intro_original": "建议在简介中添加正确的影视剧名等信息，以便在分区和搜索中得到更好的展示。", "intro_copy": "建议在简介中添加正确的影视剧名等信息。搬运转载内容必须添加原链接地址信息。", "notice": "【节目类型/节目名】+《影视剧名》+主要标题", "copy_right": 0, "show": true, "rank": 40, "max_video_count": 50, "request_id": ""}, {"id": 85, "parent": 181, "parent_name": "", "name": "小剧场", "description": "单线或连续剧情，且有演绎成分的小剧场（短剧）内容", "desc": "单线或连续剧情，且有演绎成分的小剧场（短剧）内容", "intro_original": "", "intro_copy": "搬运转载内容必须添加原短片相关信息及原链接地址信息等。", "notice": "", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 256, "parent": 181, "parent_name": "", "name": "短片", "description": "各种类型的短片，包括但不限于真人故事短片、微电影、学生作品、创意短片、励志短片、广告短片、摄影短片、纪实短片、科幻短片等", "desc": "各种类型的短片，包括但不限于真人故事短片、微电影、学生作品、创意短片、励志短片、广告短片、摄影短片、纪实短片、科幻短片等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！\n", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 261, "parent": 181, "parent_name": "", "name": "影视综合", "description": "一切无法被收纳其他影视二级分区的影视相关内容", "desc": "一切无法被收纳其他影视二级分区的影视相关内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 3, "parent": 0, "parent_name": "", "name": "音乐", "description": "音乐", "desc": "音乐", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 75, "children": [{"id": 28, "parent": 3, "parent_name": "", "name": "原创音乐", "description": "以任何题材创作的、以音乐主体为主要原创考量标准的原创歌曲及纯音乐，包括对音乐歌曲中曲的改编、重编曲及remix。", "desc": "以任何题材创作的、以音乐主体为主要原创考量标准的原创歌曲及纯音乐，包括对音乐歌曲中曲的改编、重编曲及remix。", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧!\n·请填写歌曲的详细制作人员信息，如词、曲作者等。\n·如无作品原作者授权，请勿使用他人音乐作品投稿至原创音乐区。\n·请根据实际情况慎重选择投稿类型（自制或转载）", "intro_copy": "原创音乐区原则上不接受转载视频，请获取作品原作者的有效授权并投稿至其他音乐相关分区。多次错误投稿的账号将有可能会被处理，请谨慎投稿。", "notice": "建议使用【歌手名】曲目【风格】 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 100, "max_video_count": 50, "request_id": ""}, {"id": 29, "parent": 3, "parent_name": "", "name": "音乐现场", "description": "该分区收录户外或专业演出场所公开进行音乐表演的实况视频，包括但不限于官方/个人拍摄的综艺节目、音乐剧、音乐节、演唱会等音乐演出内容。", "desc": "该分区收录户外或专业演出场所公开进行音乐表演的实况视频，包括但不限于官方/个人拍摄的综艺节目、音乐剧、音乐节、演唱会等音乐演出内容。", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧!\n·请填写歌曲的详细制作人员信息，如词、曲作者以及所属版权公司等。\n·请根据实际情况慎重选择投稿类型（自制或转载）。", "intro_copy": "请认真填写您所上传视频的相关信息，如曲名，作者，来源出处地址等重要信息，以及其他有利于检索的关键信息，便于您的稿件能合理高效地检索及展示", "notice": "建议使用 【音乐人】曲目名【现场标题】 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 90, "max_video_count": 100, "request_id": ""}, {"id": 31, "parent": 3, "parent_name": "", "name": "翻唱", "description": "对曲目的人声再演绎视频。", "desc": "对曲目的人声再演绎视频。", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧!\n·请填写歌曲的详细制作人员信息，如后期、PV作者等。\n·请根据实际情况慎重选择投稿类型（自制或转载）。     ", "intro_copy": "请认真填写您所上传视频的相关信息，如曲名，作者，来源出处地址等重要信息，以及其他有利于检索的关键信息，便于您的稿件能合理高效地检索及展示", "notice": "建议使用【翻唱者】翻唱曲目 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 80, "max_video_count": 50, "request_id": ""}, {"id": 59, "parent": 3, "parent_name": "", "name": "演奏", "description": "乐器和非传统乐器器材的演奏作品。", "desc": "乐器和非传统乐器器材的演奏作品。", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧!\n·请填写歌曲的详细制作人员及所涉及到的乐器，如演奏、摄影人员等。\n·请根据实际情况慎重选择投稿类型（自制或转载）。", "intro_copy": "请认真填写您所上传视频的相关信息，如曲名，作者，来源出处地址等重要信息，以及其他有利于检索的关键信息，便于您的稿件能合理高效地检索及展示", "notice": "建议使用【乐器/器材名】曲名 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 70, "max_video_count": 50, "request_id": ""}, {"id": 243, "parent": 3, "parent_name": "", "name": "乐评盘点", "description": "该分区收录音乐资讯、音乐点评盘点、音乐故事等，包括但不限于音乐类新闻、盘点、点评、reaction、榜单、采访、幕后故事、唱片开箱等。", "desc": "该分区收录音乐资讯、音乐点评盘点、音乐故事等，包括但不限于音乐类新闻、盘点、点评、reaction、榜单、采访、幕后故事、唱片开箱等。", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 60, "max_video_count": 50, "request_id": ""}, {"id": 30, "parent": 3, "parent_name": "", "name": "VOCALOID·UTAU", "description": "以VOCALOID等歌声合成引擎为基础，运用各类音源进行的创作。", "desc": "以VOCALOID等歌声合成引擎为基础，运用各类音源进行的创作。", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧!\n·请填写歌曲的详细制作人员信息，如词、曲、调教作者等。\n·请根据实际情况慎重选择投稿类型（自制或转载）。     ", "intro_copy": "请认真填写您所上传视频的相关信息，如曲名，作者，来源出处地址等重要信息，以及其他有利于检索的关键信息，便于您的稿件能合理高效地检索及展示", "notice": "建议使用【歌手名】曲目【作者】 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 50, "max_video_count": 50, "request_id": ""}, {"id": 193, "parent": 3, "parent_name": "", "name": "MV", "description": "该分区收录为音乐作品配合拍摄或制作的音乐录影带（Music Video），包含但不限于官方MV或MV预告，以及自制拍摄、剪辑、翻拍的MV。", "desc": "该分区收录为音乐作品配合拍摄或制作的音乐录影带（Music Video），包含但不限于官方MV或MV预告，以及自制拍摄、剪辑、翻拍的MV。", "intro_original": "", "intro_copy": "", "notice": "建议使用 【音乐人】曲目名 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 40, "max_video_count": 50, "request_id": ""}, {"id": 266, "parent": 3, "parent_name": "", "name": "音乐粉丝饭拍", "description": "该分区收录在音乐演出现场由粉丝团体或个人拍摄的非官方记录视频，包括但不限于粉丝自制饭拍、直拍、Vlog以及衍生的内容混剪等", "desc": "该分区收录在音乐演出现场由粉丝团体或个人拍摄的非官方记录视频，包括但不限于粉丝自制饭拍、直拍、Vlog以及衍生的内容混剪等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 265, "parent": 3, "parent_name": "", "name": "AI音乐", "description": "以AI合成技术为基础，运用各类工具进行创作的AI音乐，包括AI作编曲、AI作词、AI语音、AI变声、AI翻唱、AI MV 等", "desc": "以AI合成技术为基础，运用各类工具进行创作的AI音乐，包括AI作编曲、AI作词、AI语音、AI变声、AI翻唱、AI MV 等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 267, "parent": 3, "parent_name": "", "name": "电台", "description": "音乐分享、歌曲推荐、广播剧等以听为主的播放内容。", "desc": "音乐分享、歌曲推荐、广播剧等以听为主的播放内容。", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 244, "parent": 3, "parent_name": "", "name": "音乐教学", "description": "该分区收录以音乐教学为目的的内容，包括但不限于声乐教学、乐器教学、编曲制作教学、乐器设备测评等。", "desc": "该分区收录以音乐教学为目的的内容，包括但不限于声乐教学、乐器教学、编曲制作教学、乐器设备测评等。", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 9, "max_video_count": 50, "request_id": ""}, {"id": 130, "parent": 3, "parent_name": "", "name": "音乐综合", "description": "该分区收录所有无法被收纳到其他音乐二级分区的音乐类视频。包括但不限于个人选集以及任何形式的曲包。", "desc": "该分区收录所有无法被收纳到其他音乐二级分区的音乐类视频。包括但不限于个人选集以及任何形式的曲包。", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧!\n·请填写选集中涉及歌曲的信息，如“歌手 - 歌曲名”。\n·对他人歌曲进行变速、升降调、倒放等处理而产生的稿件，属于音乐选集区。\n·请根据实际情况慎重选择投稿类型（自制或转载）。", "intro_copy": "请认真填写您所上传视频的相关信息，如曲名，作者，来源出处地址等重要信息，以及其他有利于检索的关键信息，便于您的稿件能合理高效地检索及展示", "notice": "", "copy_right": 0, "show": true, "rank": 8, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 1, "parent": 0, "parent_name": "", "name": "动画", "description": "动画", "desc": "动画", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 65, "children": [{"id": 24, "parent": 1, "parent_name": "", "name": "MAD·AMV", "description": "具有一定创作度的动/静画二次创作视频", "desc": "具有一定创作度的动/静画二次创作视频", "intro_original": "填写更全面的相关信息,让更多的人能找到你的视频吧! \nBGM 建议格式：音乐人-音乐名\n使用素材 举例：埃罗芒阿老师  Re:CREATORS\n使用别人的MAD作为自己作品的素材是不允许的！", "intro_copy": "转载稿件要注明出处哦！出处包括：\n源地址 如amvnews, YouTube, Nico的作品发布链接\n原作者 举例：Pluvia\n原标题 举例：蒼天の白鯨と始まりのゼロ\n填写更全面的相关信息,让更多的人能找到你的视频吧!\nBGM 建议格式：音乐人-音乐名\n使用素材 举例：从零开始的魔法书 小魔女学园", "notice": "建议使用【作品题材】+其他部分 的形式填写标题", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 25, "parent": 1, "parent_name": "", "name": "MMD·3D", "description": "使用MMD和其他3D软件制作的视频", "desc": "使用MMD和其他3D软件制作的视频", "intro_original": "借物表!借物表! 借物表! 重要的事情说三遍!模型！动作！场景！镜头！\n使用软件 举例：Maya，MMD(MikuMikuDance)，MME\n填写详细的借物信息,让更多的人看到吧!", "intro_copy": "转载稿件要注明出处哦!出处包括:\n源地址 如YouTube, Nico的作品发布链接\n原作者 举例：minusT\n原标题 举例：【3D東方】 Subterranean Stars", "notice": "建议使用【作品题材】+其他部分 的形式填写标题", "copy_right": 0, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 47, "parent": 1, "parent_name": "", "name": "同人·手书", "description": "追求个人特色和创意表达的手书（绘）、以及同人作品展示、宣传为主的内容", "desc": "追求个人特色和创意表达的手书（绘）、以及同人作品展示、宣传为主的内容", "intro_original": "（以下仅作参考）\n\n简介：……\n\n视频类型：原创动画、同人手书、配音、广播剧、有声漫等\n\n相关题材：非二次创作可填原创题材，题材可填写多个", "intro_copy": "转载稿件要注明出处哦!出处包括:\n源地址 如vimeo, YouTube, Nico的作品发布链接\n原作者ID\n来自多个短片的合集也要注明出处哦!", "notice": "标题建议标注类型和IP名称，如【初音未来】或者【OC】【原创同人】", "copy_right": 0, "show": true, "rank": 6, "max_video_count": 50, "request_id": ""}, {"id": 257, "parent": 1, "parent_name": "", "name": "配音", "description": "使用ACGN相关画面或台本素材进行人工配音创作的内容", "desc": "使用ACGN相关画面或台本素材进行人工配音创作的内容", "intro_original": "（以下仅作参考）\n\n相关题材：配音素材来源，如动画IP名称、站内授权稿件等\n\n简介：……", "intro_copy": "（以下仅作参考）\n\n相关题材：配音素材来源，如动画IP名称、站内授权稿件等\n\n简介：……", "notice": "标题建议提炼内容亮点", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 210, "parent": 1, "parent_name": "", "name": "模玩·周边", "description": "模玩、周边的测评、展示、改造或其他衍生内容", "desc": "模玩、周边的测评、展示、改造或其他衍生内容", "intro_original": "", "intro_copy": "", "notice": "标题建议提炼内容亮点", "copy_right": 0, "show": true, "rank": 4, "max_video_count": 50, "request_id": ""}, {"id": 86, "parent": 1, "parent_name": "", "name": "特摄", "description": "特摄相关衍生视频", "desc": "特摄相关衍生视频", "intro_original": "建议在简介和TAG中添加正确的特摄片名等信息，以便在分区和搜索中得到更好的展示。", "intro_copy": "建议在简介和TAG中添加正确的特摄片名等信息。搬运转载内容必须添加原作者、原链接地址信息。 ", "notice": "【特摄片名】+主要标题", "copy_right": 0, "show": true, "rank": 3, "max_video_count": 50, "request_id": ""}, {"id": 253, "parent": 1, "parent_name": "", "name": "动漫杂谈", "description": "ACGN文化圈杂谈内容", "desc": "ACGN文化圈杂谈内容", "intro_original": "（以下仅作参考）\n\n视频类型：如鉴赏、吐槽、评点、解说、推荐、科普、导视、总结回顾\n\n相关题材：如东方、JOJO、鬼灭、魔法少女小圆\n\n简介：……", "intro_copy": "转载稿件要注明出处哦!出处包括:\n源地址 如vimeo, YouTube, Nico的作品发布链接\n原作者ID\n来自多个短片的合集也要注明出处哦!", "notice": "标题建议提炼内容亮点", "copy_right": 0, "show": true, "rank": 2, "max_video_count": 50, "request_id": ""}, {"id": 27, "parent": 1, "parent_name": "", "name": "综合", "description": "以动画及相关内容为素材的创作", "desc": "以动画及相关内容为素材的创作", "intro_original": "你想对观众说些什么呢？（以下仅作参考）\n\n视频类型：如音频替换、恶搞改编、排行榜\n\n相关题材：如东方、JOJO、鬼灭、间谍过家家\n\n简介：……", "intro_copy": "转载稿件要注明出处哦！出处包括：\n• 源地址 如YouTube，Nico的作品发布链接\n• 原作者 举例：毒舌老外gigguk\n• 原作者创作不易,注明转载来源才是好孩子！", "notice": "标题建议提炼内容亮点", "copy_right": 0, "show": true, "rank": 1, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 155, "parent": 0, "parent_name": "", "name": "时尚", "description": "时尚", "desc": "时尚", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 60, "children": [{"id": 157, "parent": 155, "parent_name": "", "name": "美妆护肤", "description": "彩妆护肤、发型美甲、仿妆、美容美体、口腔、拍照技巧、颜值分析等变美相关内容分享或产品测评", "desc": "彩妆护肤、发型美甲、仿妆、美容美体、口腔、拍照技巧、颜值分析等变美相关内容分享或产品测评", "intro_original": "原创妆容/仿妆/cos妆；使用产品详细列表，如canmake花瓣腮红3#。请不要出现购买链接哟~", "intro_copy": "转载自【YouTube/优酷/腾讯视频等视频网站】的【美妆/服饰/健身】内容。注明原PO的ID及联系方式，如脸书、微博等。若获得原PO授权请特别注明。产品详细列表，如canmake花瓣腮红3#。请不要出现购买链接哟~", "notice": "清晰明了表明内容亮点的标题会更受众欢迎哟！", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 252, "parent": 155, "parent_name": "", "name": "仿妆cos", "description": "对明星、影视剧、文学作品、动漫、番剧、绘画作品、游戏等人物角色妆容或整体妆造进行模仿、还原、展示、演绎的内容", "desc": "对明星、影视剧、文学作品、动漫、番剧、绘画作品、游戏等人物角色妆容或整体妆造进行模仿、还原、展示、演绎的内容", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 18, "max_video_count": 50, "request_id": ""}, {"id": 158, "parent": 155, "parent_name": "", "name": "穿搭", "description": "穿搭风格、穿搭技巧的展示分享，涵盖衣服、鞋靴、箱包配件、配饰（帽子、钟表、珠宝首饰）等", "desc": "穿搭风格、穿搭技巧的展示分享，涵盖衣服、鞋靴、箱包配件、配饰（帽子、钟表、珠宝首饰）等", "intro_original": "穿搭风格，如日常、原宿、制服、lo装、汉服等。", "intro_copy": "转载自【YouTube/优酷/腾讯视频等视频网站】的【美妆/服饰/健身】内容。注明原PO的ID及联系方式，如脸书、微博等。若获得原PO授权请特别注明。产品详细列表，如canmake花瓣腮红3#。请不要出现购买链接哟~", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 159, "parent": 155, "parent_name": "", "name": "时尚潮流", "description": "时尚街拍、时装周、时尚大片，时尚品牌、潮流等行业相关记录及知识科普", "desc": "时尚街拍、时装周、时尚大片，时尚品牌、潮流等行业相关记录及知识科普", "intro_original": "建议注明品牌（系列）、时间、地点、模特名字，最好来一发解析哦", "intro_copy": "转载自【YouTube/优酷/腾讯视频等视频网站】的内容。品牌（系列）、时间、地点、模特名字，最好来一发解析❤请不要出现购买链接及和视频内容无关二维码", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 211, "parent": 0, "parent_name": "", "name": "美食", "description": "美食", "desc": "美食", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 55, "children": [{"id": 76, "parent": 211, "parent_name": "", "name": "美食制作", "description": "包括但不限于料理制作教程，各种菜系、甜点、速食、饮料、小吃制作等", "desc": "包括但不限于料理制作教程，各种菜系、甜点、速食、饮料、小吃制作等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 100, "max_video_count": 50, "request_id": ""}, {"id": 212, "parent": 211, "parent_name": "", "name": "美食侦探", "description": "包括但不限于探店、街边美食、饮食文化，发现特色地域美食、路边摊与热门网红食物等", "desc": "包括但不限于探店、街边美食、饮食文化，发现特色地域美食、路边摊与热门网红食物等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 95, "max_video_count": 50, "request_id": ""}, {"id": 213, "parent": 211, "parent_name": "", "name": "美食测评", "description": "包括但不限于边吃边聊、测评推荐或吐槽各种美食等", "desc": "包括但不限于边吃边聊、测评推荐或吐槽各种美食等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 90, "max_video_count": 50, "request_id": ""}, {"id": 214, "parent": 211, "parent_name": "", "name": "田园美食", "description": "包括但不限于乡野美食、三农采摘、钓鱼赶海等", "desc": "包括但不限于乡野美食、三农采摘、钓鱼赶海等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 85, "max_video_count": 50, "request_id": ""}, {"id": 215, "parent": 211, "parent_name": "", "name": "美食记录", "description": "记录一日三餐，美食vlog、料理、便当、饮品合集、美食小剧场等", "desc": "记录一日三餐，美食vlog、料理、便当、饮品合集、美食小剧场等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 80, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 223, "parent": 0, "parent_name": "", "name": "汽车", "description": "汽车", "desc": "汽车", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 52, "children": [{"id": 258, "parent": 223, "parent_name": "", "name": "汽车知识科普", "description": "关于汽车技术与文化的硬核科普，以及生活中学车、用车、养车的相关知识", "desc": "关于汽车技术与文化的硬核科普，以及生活中学车、用车、养车的相关知识", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 70, "max_video_count": 50, "request_id": ""}, {"id": 227, "parent": 223, "parent_name": "", "name": "购车攻略", "description": "新车、二手车测评试驾，购车推荐，交易避坑攻略等", "desc": "新车、二手车测评试驾，购车推荐，交易避坑攻略等", "intro_original": "", "intro_copy": "", "notice": "丰富详实的购车建议和新车体验", "copy_right": 0, "show": true, "rank": 65, "max_video_count": 50, "request_id": ""}, {"id": 247, "parent": 223, "parent_name": "", "name": "新能源车", "description": "电动汽车、混合动力汽车等新能源车型相关内容，包括新车资讯、试驾体验、专业评测等", "desc": "电动汽车、混合动力汽车等新能源车型相关内容，包括新车资讯、试驾体验、专业评测等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 63, "max_video_count": 50, "request_id": ""}, {"id": 245, "parent": 223, "parent_name": "", "name": "赛车", "description": "一切以汽车运动为代表的车手、汽车赛事、赛道、赛车模拟器、赛车、卡丁车及赛车衍生品相关的视频", "desc": "一切以汽车运动为代表的车手、汽车赛事、赛道、赛车模拟器、赛车、卡丁车及赛车衍生品相关的视频", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 60, "max_video_count": 50, "request_id": ""}, {"id": 246, "parent": 223, "parent_name": "", "name": "改装玩车", "description": "汽车改装、老车修复、硬核越野、车友聚会等相关内容", "desc": "汽车改装、老车修复、硬核越野、车友聚会等相关内容", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 56, "max_video_count": 50, "request_id": ""}, {"id": 240, "parent": 223, "parent_name": "", "name": "摩托车", "description": "与摩托车相关的视频，包括但不限于摩托车骑行、试驾、装备测评、教学、赛事、剪辑等相关内容", "desc": "与摩托车相关的视频，包括但不限于摩托车骑行、试驾、装备测评、教学、赛事、剪辑等相关内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 54, "max_video_count": 50, "request_id": ""}, {"id": 248, "parent": 223, "parent_name": "", "name": "房车", "description": "房车及营地相关内容，包括不限于产品介绍、驾驶体验、房车生活和房车旅行等内容", "desc": "房车及营地相关内容，包括不限于产品介绍、驾驶体验、房车生活和房车旅行等内容", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 176, "parent": 223, "parent_name": "", "name": "汽车生活", "description": "和汽车等交通工具相关的一切泛化内容的视频", "desc": "和汽车等交通工具相关的一切泛化内容的视频", "intro_original": "", "intro_copy": "", "notice": "分享汽车及出行相关的生活体验类视频", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 234, "parent": 0, "parent_name": "", "name": "运动", "description": "运动", "desc": "运动", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 51, "children": [{"id": 235, "parent": 234, "parent_name": "", "name": "篮球", "description": "与篮球相关的视频，包括但不限于篮球赛事、教学、评述、剪辑、剧情等相关内容", "desc": "与篮球相关的视频，包括但不限于篮球赛事、教学、评述、剪辑、剧情等相关内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 60, "max_video_count": 50, "request_id": ""}, {"id": 249, "parent": 234, "parent_name": "", "name": "足球", "description": "与足球相关的视频，包括但不限于足球赛事、教学、评述、剪辑、剧情等相关内容", "desc": "与足球相关的视频，包括但不限于足球赛事、教学、评述、剪辑、剧情等相关内容", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 55, "max_video_count": 50, "request_id": ""}, {"id": 164, "parent": 234, "parent_name": "", "name": "健身", "description": "与健身相关的视频，包括但不限于健身、健美、操舞、瑜伽、普拉提、跑步、街健、健康餐、健身小剧场等内容", "desc": "与健身相关的视频，包括但不限于健身、健美、操舞、瑜伽、普拉提、跑步、街健、健康餐、健身小剧场等内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 50, "max_video_count": 50, "request_id": ""}, {"id": 236, "parent": 234, "parent_name": "", "name": "竞技体育", "description": "与竞技体育相关的视频，包括但不限于乒乓、羽毛球、排球、赛车等竞技项目的赛事、评述、剪辑、剧情等相关内容", "desc": "与竞技体育相关的视频，包括但不限于乒乓、羽毛球、排球、赛车等竞技项目的赛事、评述、剪辑、剧情等相关内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 40, "max_video_count": 50, "request_id": ""}, {"id": 237, "parent": 234, "parent_name": "", "name": "运动文化", "description": "与运动文化相关的视频，包括但不限于球鞋、球衣、球星卡等运动衍生品的分享、解读，体育产业的分析、科普等相关内容", "desc": "与运动文化相关的视频，包括但不限于球鞋、球衣、球星卡等运动衍生品的分享、解读，体育产业的分析、科普等相关内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 238, "parent": 234, "parent_name": "", "name": "运动综合", "description": "与运动综合相关的视频，包括但不限于钓鱼、骑行、滑板等日常运动分享、教学、Vlog等相关内容", "desc": "与运动综合相关的视频，包括但不限于钓鱼、骑行、滑板等日常运动分享、教学、Vlog等相关内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 188, "parent": 0, "parent_name": "", "name": "科技", "description": "科技", "desc": "科技", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 50, "children": [{"id": 95, "parent": 188, "parent_name": "", "name": "数码", "description": "手机/电脑/相机/影音智能设备等", "desc": "手机/电脑/相机/影音智能设备等", "intro_original": "请注明产品名称，可对相关内容进行补充说明。\n不建议填写含贩售信息的相关网站地址。\n请勿加入涉政或具较大争议性的文字简介，否则将做打回处理。\n如是系列，可附带上期视频地址。", "intro_copy": "转载稿件需标明出处，请注明原作者、原作者频道名或原作者投稿地址。\n可对相关内容进行补充说明。\n请勿加入涉政或具较大争议性的文字简介，否则将做打回处理。\n如是系列，也可附带上期视频地址。", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 25, "max_video_count": 50, "request_id": ""}, {"id": 230, "parent": 188, "parent_name": "", "name": "软件应用", "description": "围绕各类系统软件、应用软件、网站的相关视频", "desc": "围绕各类系统软件、应用软件、网站的相关视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 231, "parent": 188, "parent_name": "", "name": "计算机技术", "description": "软硬件开发/人工智能/大数据/深度学习/IT运维等", "desc": "软硬件开发/人工智能/大数据/深度学习/IT运维等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 232, "parent": 188, "parent_name": "", "name": "科工机械", "description": "航空航天/工程建设/电子工程/机械制造/海洋工程等", "desc": "航空航天/工程建设/电子工程/机械制造/海洋工程等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 233, "parent": 188, "parent_name": "", "name": "极客DIY", "description": "硬核制作/发明创造/技术创新/极客文化等", "desc": "硬核制作/发明创造/技术创新/极客文化等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 217, "parent": 0, "parent_name": "", "name": "动物圈", "description": "动物圈", "desc": "动物圈", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 50, "children": [{"id": 218, "parent": 217, "parent_name": "", "name": "喵星人", "description": "与猫相关的视频，包括但不限于猫咪日常、猫咪喂养、猫咪知识、猫咪剧场、猫咪救助、猫咪娱乐相关的内容", "desc": "与猫相关的视频，包括但不限于猫咪日常、猫咪喂养、猫咪知识、猫咪剧场、猫咪救助、猫咪娱乐相关的内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 60, "max_video_count": 50, "request_id": ""}, {"id": 219, "parent": 217, "parent_name": "", "name": "汪星人", "description": "与狗相关的视频，包括但不限于狗狗日常、狗狗喂养、狗狗知识、狗狗剧场、狗狗救助、狗狗娱乐相关的内容", "desc": "与狗相关的视频，包括但不限于狗狗日常、狗狗喂养、狗狗知识、狗狗剧场、狗狗救助、狗狗娱乐相关的内容", "intro_original": "", "intro_copy": "", "notice": "晰明了表明内容亮点的标题会更受观众欢迎哟！\u0007", "copy_right": 0, "show": true, "rank": 55, "max_video_count": 50, "request_id": ""}, {"id": 222, "parent": 217, "parent_name": "", "name": "小宠异宠", "description": "非猫、狗的宠物。包括但不限于水族、爬宠、鸟类、鼠、兔等内容", "desc": "非猫、狗的宠物。包括但不限于水族、爬宠、鸟类、鼠、兔等内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 54, "max_video_count": 50, "request_id": ""}, {"id": 221, "parent": 217, "parent_name": "", "name": "野生动物", "description": "与野生动物相关的视频，包括但不限于狮子、老虎、狼、大熊猫等动物内容", "desc": "与野生动物相关的视频，包括但不限于狮子、老虎、狼、大熊猫等动物内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 51, "max_video_count": 50, "request_id": ""}, {"id": 220, "parent": 217, "parent_name": "", "name": "动物二创", "description": "以动物素材为主的包括但不限于配音、剪辑、解说、reaction的再创作内容", "desc": "以动物素材为主的包括但不限于配音、剪辑、解说、reaction的再创作内容", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 30, "max_video_count": 50, "request_id": ""}, {"id": 75, "parent": 217, "parent_name": "", "name": "动物综合", "description": "收录除上述子分区外，其余动物相关视频以及非动物主体或多个动物主体的动物相关延伸内容。如动物资讯、动物趣闻、动物知识等", "desc": "收录除上述子分区外，其余动物相关视频以及非动物主体或多个动物主体的动物相关延伸内容。如动物资讯、动物趣闻、动物知识等", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 129, "parent": 0, "parent_name": "", "name": "舞蹈", "description": "舞蹈", "desc": "舞蹈", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 48, "children": [{"id": 20, "parent": 129, "parent_name": "", "name": "宅舞", "description": "与ACG相关的翻跳、原创舞蹈", "desc": "与ACG相关的翻跳、原创舞蹈", "intro_original": "建议注明编舞、音源等出处信息", "intro_copy": "请注明转载作品的详细转载地址、作者、音源等信息，未添加正确信息的稿件可能被打回", "notice": "【舞者】曲名 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 25, "max_video_count": 100, "request_id": ""}, {"id": 154, "parent": 129, "parent_name": "", "name": "舞蹈综合", "description": "收录无法定义到其他舞蹈子分区的舞蹈视频", "desc": "收录无法定义到其他舞蹈子分区的舞蹈视频", "intro_original": "建议注明编舞、BGM等出处信息", "intro_copy": "请注明转载作品的详细转载地址、作者、BGM等信息，未添加正确信息的稿件可能被打回", "notice": "【舞者/舞蹈工作室】曲名 或者【赛事】曲名 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 255, "parent": 129, "parent_name": "", "name": "颜值·网红舞", "description": "手势舞及网红流行舞蹈、短视频舞蹈等相关视频", "desc": "手势舞及网红流行舞蹈、短视频舞蹈等相关视频", "intro_original": "", "intro_copy": "", "notice": "清晰明了表明内容亮点的标题会更受观众欢迎哟！", "copy_right": 0, "show": true, "rank": 18, "max_video_count": 50, "request_id": ""}, {"id": 198, "parent": 129, "parent_name": "", "name": "街舞", "description": "收录街舞相关内容，包括赛事现场、舞室作品、个人翻跳、FREESTYLE等", "desc": "收录街舞相关内容，包括赛事现场、舞室作品、个人翻跳、FREESTYLE等", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 199, "parent": 129, "parent_name": "", "name": "明星舞蹈", "description": "国内外明星发布的官方舞蹈及其翻跳内容", "desc": "国内外明星发布的官方舞蹈及其翻跳内容", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 200, "parent": 129, "parent_name": "", "name": "国风舞蹈", "description": "收录国风向舞蹈内容，包括中国舞、民族民间舞、汉唐舞、国风爵士等", "desc": "收录国风向舞蹈内容，包括中国舞、民族民间舞、汉唐舞、国风爵士等", "intro_original": "", "intro_copy": "", "notice": "【舞者】曲名的类似格式填写标题", "copy_right": 0, "show": true, "rank": 4, "max_video_count": 50, "request_id": ""}, {"id": 156, "parent": 129, "parent_name": "", "name": "舞蹈教程", "description": "动作分解，基础教程等具有教学意义的舞蹈视频", "desc": "动作分解，基础教程等具有教学意义的舞蹈视频", "intro_original": "建议在简介中注明编舞、音源等信息，在标签中注明舞种等信息", "intro_copy": "请注明转载作品的详细转载地址、作者、舞种等信息，在标签中注明舞种等信息，未添加正确信息的稿件可能被打回", "notice": "【舞者/工作室】曲名/舞种＋教学类别(如动作分解、基础教程) 的类似格式填写标题", "copy_right": 0, "show": true, "rank": 1, "max_video_count": 100, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 167, "parent": 0, "parent_name": "", "name": "国创", "description": "国创", "desc": "国创", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 45, "children": [{"id": 153, "parent": 167, "parent_name": "", "name": "国产动画", "description": "国产连载动画，国产完结动画", "desc": "国产连载动画，国产完结动画", "intro_original": "国产动画标题格式建议：剧集名称 00（集数）/预告。", "intro_copy": "转载稿件请标明转载出处，包括源地址,原作者,原标题。", "notice": "剧集名称+集数+分集标题；【合集】+剧集名称", "copy_right": 0, "show": true, "rank": 20, "max_video_count": 50, "request_id": ""}, {"id": 168, "parent": 167, "parent_name": "", "name": "国产原创相关", "description": "以国产动画、漫画、小说为素材的二次创作", "desc": "以国产动画、漫画、小说为素材的二次创作", "intro_original": "请注明BGM和使用素材。BGM格式：音乐人-音乐名 素材格式：片名列表。", "intro_copy": "转载稿件请标明转载出处，包括源地址,原作者,原标题。", "notice": "【广播剧、动态漫画】+总标题+集数+分集标题；【类型（MAD、杂谈等）】+标题", "copy_right": 0, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 169, "parent": 167, "parent_name": "", "name": "布袋戏", "description": "布袋戏以及相关剪辑节目", "desc": "布袋戏以及相关剪辑节目", "intro_original": "请认真填写您所上传视频的相关信息，如剧情简介，主要演员及制作人员。", "intro_copy": "转载稿件请标明转载出处，包括源地址,原作者,原标题。", "notice": "【合集】+剧集名称；【霹雳布袋戏、金光布袋戏等】+标题", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 100, "request_id": ""}, {"id": 170, "parent": 167, "parent_name": "", "name": "资讯", "description": "原创国产动画、漫画的相关资讯、宣传节目等", "desc": "原创国产动画、漫画的相关资讯、宣传节目等", "intro_original": "资讯区标题格式建议：【X月/剧场版】国产动画名称 PV1/CM2【清晰度】", "intro_copy": "转载稿件请标明转载出处，包括源地址,原作者,原标题。", "notice": "【类型（PV/OP/访谈等）】+标题；剧集名称+集数+预告", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 195, "parent": 167, "parent_name": "", "name": "动态漫·广播剧", "description": "国产动态漫画、有声漫画、广播剧", "desc": "国产动态漫画、有声漫画、广播剧", "intro_original": "", "intro_copy": "", "notice": "国产动态漫画、有声漫画、广播剧", "copy_right": 0, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 119, "parent": 0, "parent_name": "", "name": "鬼畜", "description": "鬼畜", "desc": "鬼畜", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 40, "children": [{"id": 22, "parent": 119, "parent_name": "", "name": "鬼畜调教", "description": "使用素材在音频、画面上做一定处理，达到与BGM具有一定同步感的视频", "desc": "使用素材在音频、画面上做一定处理，达到与BGM具有一定同步感的视频", "intro_original": "请正确的填写作品中使用的素材以及BGM（背景音乐），有助于视频在分区以及搜索中获得更好的展示。\n借用他人创作的画面内容时请注明借用作品的出处信息；借用他人创作的鬼畜音频时请先与原作者联系，获得作者授权许可后方可使用。", "intro_copy": "请正确的填写作品中使用的素材以及BGM（背景音乐），有助于视频在分区以及搜索中获得更好的展示。\n搬运（转载）作品请注明出处以及sm番号或者ytb短链，否则无法通过审核哦~", "notice": "【素材名】曲名/内容主题", "copy_right": 0, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 26, "parent": 119, "parent_name": "", "name": "音MAD", "description": "使用素材音频进行一定的二次创作来达到还原原曲的非商业性质稿件", "desc": "使用素材音频进行一定的二次创作来达到还原原曲的非商业性质稿件", "intro_original": "请正确的填写作品中使用的素材以及BGM（背景音乐），有助于视频在分区以及搜索中获得更好的展示。\n借用他人创作的画面内容时请注明借用作品的出处信息；借用他人创作的鬼畜音频时请先与原作者联系，获得作者授权许可后方可使用。", "intro_copy": "请正确的填写作品中使用的素材以及BGM（背景音乐），有助于视频在分区以及搜索中获得更好的展示。\n搬运（转载）作品请注明出处以及sm番号或者ytb短链，否则无法通过审核哦~", "notice": "【素材名】曲名/内容主题", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 126, "parent": 119, "parent_name": "", "name": "人力VOCALOID", "description": "将人物或者角色的无伴奏素材进行人工调音，使其就像VOCALOID一样歌唱的技术", "desc": "将人物或者角色的无伴奏素材进行人工调音，使其就像VOCALOID一样歌唱的技术", "intro_original": "请正确的填写作品中使用的素材以及BGM（背景音乐），有助于视频在分区以及搜索中获得更好的展示。\n借用他人创作的画面内容时请注明借用作品的出处信息；借用他人创作的鬼畜音频时请先与原作者联系，获得作者授权许可后方可使用。", "intro_copy": "请正确的填写作品中使用的素材以及BGM（背景音乐），有助于视频在分区以及搜索中获得更好的展示。\n搬运（转载）作品请注明出处以及sm番号或者ytb短链，否则无法通过审核哦~", "notice": "【素材名】曲名/内容主题", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 216, "parent": 119, "parent_name": "", "name": "鬼畜剧场", "description": "使用素材进行人工剪辑编排的有剧情的视频", "desc": "使用素材进行人工剪辑编排的有剧情的视频", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 4, "max_video_count": 50, "request_id": ""}, {"id": 127, "parent": 119, "parent_name": "", "name": "教程演示", "description": "鬼畜相关的科普和教程演示", "desc": "鬼畜相关的科普和教程演示", "intro_original": "非鬼畜相关的教程演示请投稿至科技-野生技术协会。", "intro_copy": "非鬼畜相关的教程演示请投稿至科技-野生技术协会。", "notice": "", "copy_right": 0, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 177, "parent": 0, "parent_name": "", "name": "纪录片", "description": "非电影性质的完整纪录片", "desc": "非电影性质的完整纪录片", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 30, "children": [{"id": 37, "parent": 177, "parent_name": "", "name": "人文·历史", "description": "除宣传片、影视剪辑外的，人文艺术历史纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "desc": "除宣传片、影视剪辑外的，人文艺术历史纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "intro_original": "", "intro_copy": "请注明制作片方（如国家地理、历史频道），并对视频内容进行简要叙述。可对相关知识进行补充说明。", "notice": "建议使用【频道名】节目名称-系列【生肉/字幕】【字幕组或其他信息】的类似格式填写标题", "copy_right": 2, "show": true, "rank": 15, "max_video_count": 50, "request_id": ""}, {"id": 178, "parent": 177, "parent_name": "", "name": "科学·探索·自然", "description": "除演讲、网课、教程外的，科学探索自然纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "desc": "除演讲、网课、教程外的，科学探索自然纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 179, "parent": 177, "parent_name": "", "name": "军事", "description": "除时政军事新闻外的，军事纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "desc": "除时政军事新闻外的，军事纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 180, "parent": 177, "parent_name": "", "name": "社会·美食·旅行", "description": "除VLOG、风光摄影外的，社会美食旅行纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "desc": "除VLOG、风光摄影外的，社会美食旅行纪录剧集或电影、预告、花絮、二创、5分钟以上纪录短片", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 13, "parent": 0, "parent_name": "", "name": "番剧", "description": "番剧", "desc": "番剧", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 25, "children": [{"id": 51, "parent": 13, "parent_name": "", "name": "资讯", "description": "以动画/轻小说/漫画/杂志为主的资讯内容，PV/CM/特报/冒头/映像/预告", "desc": "以动画/轻小说/漫画/杂志为主的资讯内容，PV/CM/特报/冒头/映像/预告", "intro_original": " ", "intro_copy": "【标题规范】\n*来源：轻小说，小说，漫画，杂志，长篇，，原创动画，TV动画（开播月份已知时可省略），剧场版，OVA，Blu-ray\n国家（非日本动画时填写）：美漫（美番），韩漫等；\n*开播年份：（为当年时开播可省略）；\n*开播月份：1-12月（开播月份未知时可省略）；\n*类别：PV，CM，TVCM，预告（预告前加集数），冒头，映像；\n清晰度：720P，1080P。\n注：*为必填项。", "notice": "【X月/TV动画/剧场版】番剧名称 PV/CM0", "copy_right": 2, "show": true, "rank": 10, "max_video_count": 50, "request_id": ""}, {"id": 152, "parent": 13, "parent_name": "", "name": "官方延伸", "description": "以动画番剧及声优为主的EVENT/生放送/DRAMA/RADIO/LIVE/特典/冒头等", "desc": "以动画番剧及声优为主的EVENT/生放送/DRAMA/RADIO/LIVE/特典/冒头等", "intro_original": " ", "intro_copy": "", "notice": " ", "copy_right": 0, "show": true, "rank": 5, "max_video_count": 50, "request_id": ""}, {"id": 32, "parent": 13, "parent_name": "", "name": "完结动画", "description": "已完结TV/WEB动画及其独立系列，旧剧场版/OVA/SP/未放送", "desc": "已完结TV/WEB动画及其独立系列，旧剧场版/OVA/SP/未放送", "intro_original": " ", "intro_copy": "【标题规范】\n*清晰度：360P，480P，720P，1080P（视频源分辨率高度）；\n来源：TVRip，DVDRip，BDRip；\n*内容属性：合集（对应TV版本内容），剧场版，OVA，特典，SP；\n*字幕组名称：当前稿件视频源出品字幕组名称，如为无字幕视频源，则填写“生肉”；如为自制字幕，则填写“中文字幕”或“中字”。\n注：*为必填项。", "notice": "【720P/BD】番剧名称 分季【XX字幕】", "copy_right": 2, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}, {"id": 33, "parent": 13, "parent_name": "", "name": "连载动画", "description": "连载中TV/WEB动画，新剧场版/OVA/SP/未放送/小剧场", "desc": "连载中TV/WEB动画，新剧场版/OVA/SP/未放送/小剧场", "intro_original": " ", "intro_copy": "【标题规范】\n*开播月份：1-12月；\n*所属国别（非日本动画时填写）：美漫（美番），韩漫等；\n*当前集数：当前稿件所更新的集数，如有多集，用“-”连接，如：01-05；\n*字幕组名称：当前稿件视频源出品字幕组名称，如为无字幕视频源，则填写“生肉”。\n清晰度：360P，480P，720P，1080P。\n注：*为必填项。", "notice": "【X月/OVA/剧场版】番剧名称【XX字幕】", "copy_right": 2, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 11, "parent": 0, "parent_name": "", "name": "电视剧", "description": "电视剧", "desc": "电视剧", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 20, "children": [{"id": 185, "parent": 11, "parent_name": "", "name": "国产剧", "description": "", "desc": "", "intro_original": "请投正片！请投正片！请投正片！【预告·花絮·剪辑·CUT·二创·短片·特摄等相关内容请投至“影视”分区】谢谢！", "intro_copy": "请投正片！请投正片！请投正片！【预告·花絮·剪辑·CUT·二创·短片·特摄等相关内容请投至“影视”分区】谢谢！", "notice": "请使用【类型】电视剧名称 年份【其他信息】的类型格式填写标题。", "copy_right": 0, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}, {"id": 23, "parent": 0, "parent_name": "", "name": "电影", "description": "电影", "desc": "电影", "intro_original": "", "intro_copy": "", "notice": "", "copy_right": 0, "show": true, "rank": 15, "children": [{"id": 147, "parent": 23, "parent_name": "", "name": "国产电影", "description": "", "desc": "", "intro_original": "请投正片！请投正片！请投正片！非正片请投至“影视”分区！谢谢！", "intro_copy": "请投正片！请投正片！请投正片！非正片请投至“影视”分区！谢谢！", "notice": "请使用【类型】电影名称 年份 【其他信息】 的类似格式填写标题。【合集投稿将不予审核通过】", "copy_right": 2, "show": true, "rank": 0, "max_video_count": 50, "request_id": ""}], "max_video_count": 50, "request_id": ""}]