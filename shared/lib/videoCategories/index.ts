import zhiHuCategories from './zhi-hu.json'
import wangYiHaoCategories from './wang-yi-hao.json'
import yiDianHaoCategories from './yi-dian-hao.json'
import aiQiYiCategories from './ai-qi-yi.json'
import bilibiliCategories from './bilibili.json'
import qiEHaoCategories from './qi-e-hao.json'
import souHuHaoCategories from './sou-hu-hao.json'
import type { PlatformNameKey } from '@/lib/platform'
import type {
  ZhiHuCategory,
  ZhiHuCategoryData,
  BilibiliCategory,
  BilibiliCategoryChild,
  WangYiHaoCategory,
  YiDianHaoCategories,
  AiQiYiCategory,
  QiEHaoCategory,
  SouHuHaoCategory,
  SouHuHaoSubItem,
  Option
} from './types'

// 转换知乎分类数据为Option格式
function transformZhiHuCategories(categories: ZhiHuCategory[]): Option<ZhiHuCategoryData>[] {
  return categories.map(category => {
    const option: Option<ZhiHuCategoryData> = {
      id: category.data.id.toString(),
      label: category.data.name,
      raw: category.data
    }
    
    if (category.children && category.children.length > 0) {
      option.children = category.children.map((child) => ({
        id: child.data.id.toString(),
        label: child.data.name,
        raw: child.data
      }))
    }
    
    return option
  })
}

// 转换B站分类数据为Option格式
function transformBilibiliCategories(categories: BilibiliCategory[]): Option<BilibiliCategory, BilibiliCategoryChild>[] {
  return categories.map(category => {
    const option: Option<BilibiliCategory, BilibiliCategoryChild> = {
      id: category.id.toString(),
      label: category.name,
      raw: category
    }
    
    if (category.children && category.children.length > 0) {
      option.children = category.children.map((child) => ({
        id: child.id.toString(),
        label: child.name,
        raw: child
      }))
    }
    
    return option
  })
}

// 转换网易号分类数据为Option格式
function transformWangYiHaoCategories(categories: WangYiHaoCategory[]): Option<WangYiHaoCategory>[] {
  return categories.map(category => {
    const option: Option<WangYiHaoCategory> = {
      id: category.categoryName,
      label: category.categoryName,
      raw: category
    }
    
    if (category.subCategoryList && category.subCategoryList.length > 0) {
      option.children = transformWangYiHaoCategories(category.subCategoryList)
    }
    
    return option
  })
}

// 转换一点号分类数据为Option格式 (键值对格式)
function transformYiDianHaoCategories(categoriesObj: YiDianHaoCategories): Option<{name: string}>[] {
  return Object.entries(categoriesObj).map(([key, values]) => {
    const option: Option<{name: string}> = {
      id: key,
      label: key,
      raw: { name: key }
    }
    
    if (values && values.length > 0) {
      option.children = values.map(value => ({
        id: value,
        label: value,
        raw: { name: value }
      }))
    }
    
    return option
  })
}

// 转换爱奇艺分类数据为Option格式
function transformAiQiYiCategories(categories: AiQiYiCategory[]): Option<AiQiYiCategory>[] {
  return categories.map(category => {
    const option: Option<AiQiYiCategory> = {
      id: category.classifyId,
      label: category.classifyName,
      raw: category
    }
    
    return option
  })
}

// 转换企鹅号分类数据为Option格式
function transformQiEHaoCategories(categories: QiEHaoCategory[]): Option<QiEHaoCategory, {name: string}>[] {
  return categories.map(category => {
    const option: Option<QiEHaoCategory, {name: string}> = {
      id: category.cat,
      label: category.cat,
      raw: category
    }
    
    if (category.subcats && category.subcats.length > 0) {
      option.children = category.subcats.map(subcat => ({
        id: subcat,
        label: subcat,
        raw: { name: subcat }
      }))
    }
    
    return option
  })
}

// 转换搜狐号分类数据为Option格式
function transformSouHuHaoCategories(categories: SouHuHaoCategory[]): Option<SouHuHaoCategory, SouHuHaoSubItem>[] {
  return categories.map(category => {
    const option: Option<SouHuHaoCategory, SouHuHaoSubItem> = {
      id: category.id.toString(),
      label: category.name,
      raw: category
    }
    
    if (category.subItems && category.subItems.length > 0) {
      option.children = category.subItems.map(subItem => ({
        id: subItem.id.toString(),
        label: subItem.name,
        raw: subItem
      }))
    }
    
    return option
  })
}

// 平台分类选项缓存
const platformCategoriesCache: Record<string, Option<unknown>[]> = {}

/**
 * 获取指定平台的分类选项
 * @param platform 平台标识
 * @returns 分类选项数组
 */
export function getVideoCategoriesByPlatform(platform: PlatformNameKey): Option<unknown>[] {
  // 如果缓存中已存在，直接返回
  if (platformCategoriesCache[platform]) {
    return platformCategoriesCache[platform]
  }
  
  let result: Option<unknown>[] = []
  
  switch (platform) {
    case 'ZhiHu':
      result = transformZhiHuCategories(zhiHuCategories as ZhiHuCategory[])
      break
    case 'BiliBili':
      result = transformBilibiliCategories(bilibiliCategories as BilibiliCategory[])
      break
    case 'WangYiHao':
      result = transformWangYiHaoCategories(wangYiHaoCategories as WangYiHaoCategory[])
      break
    case 'YiDianHao':
      result = transformYiDianHaoCategories(yiDianHaoCategories as YiDianHaoCategories)
      break
    case 'AiQiYi':
      result = transformAiQiYiCategories(aiQiYiCategories as AiQiYiCategory[])
      break
    case 'QiEHao':
      result = transformQiEHaoCategories(qiEHaoCategories as QiEHaoCategory[])
      break
    case 'SouHuHao':
      result = transformSouHuHaoCategories(souHuHaoCategories as SouHuHaoCategory[])
      break
    default:
      result = []
  }
  
  // 缓存结果
  platformCategoriesCache[platform] = result
  
  return result
}

// 延迟初始化所有平台选项映射
export const allPlatformCategories: Partial<Record<PlatformNameKey, Option<unknown>[]>> = {}

// 初始化常用平台分类
allPlatformCategories.ZhiHu = transformZhiHuCategories(zhiHuCategories as ZhiHuCategory[])
allPlatformCategories.BiliBili = transformBilibiliCategories(bilibiliCategories as BilibiliCategory[])
allPlatformCategories.WangYiHao = transformWangYiHaoCategories(wangYiHaoCategories as WangYiHaoCategory[])
allPlatformCategories.YiDianHao = transformYiDianHaoCategories(yiDianHaoCategories as YiDianHaoCategories)
allPlatformCategories.AiQiYi = transformAiQiYiCategories(aiQiYiCategories as AiQiYiCategory[])
allPlatformCategories.QiEHao = transformQiEHaoCategories(qiEHaoCategories as QiEHaoCategory[])
allPlatformCategories.SouHuHao = transformSouHuHaoCategories(souHuHaoCategories as SouHuHaoCategory[])

// 导出Option类型
export type { Option } from './types'

