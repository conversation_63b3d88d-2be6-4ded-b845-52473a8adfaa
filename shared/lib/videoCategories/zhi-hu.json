[{"data": {"id": 3014000, "name": "生活"}, "children": [{"data": {"id": 3014001, "name": "美食", "description": "美食制作、评测种草、探店分享等内容"}}, {"data": {"id": 3014002, "name": "家居", "description": "家居生活、装修装饰、手工制作等内容"}}, {"data": {"id": 3014003, "name": "动物宠物", "description": "各类动物宠物饲养日常、搞笑动物宠物、野生动物等"}}, {"data": {"id": 3014004, "name": "旅行", "description": "旅行体验、旅行目的地攻略等内容"}}, {"data": {"id": 3014005, "name": "校园", "description": "校园日常生活、学习、社交等内容"}}, {"data": {"id": 3014006, "name": "日常", "description": "与生活相关的日常类内容"}}, {"data": {"id": 3014007, "name": "兴趣", "description": "绘画、手工、书法"}}]}, {"data": {"id": 3007000, "name": "科技数码"}, "children": [{"data": {"id": 3007001, "name": "数码产品", "description": "手机、电脑、电子产品、家用电器评测体验等"}}, {"data": {"id": 3007002, "name": "摄影摄像", "description": "摄影摄像设备的导购、体验、创作经验等"}}, {"data": {"id": 3007003, "name": "极客 DIY", "description": "硬核电子 DIY、客制化、机器人、单片机内容等"}}, {"data": {"id": 3007004, "name": "软件应用", "description": "各类软件、App 的推荐、测评、技巧学习"}}, {"data": {"id": 3007005, "name": "编程开发", "description": "CS、编程、开发直接相关的教程"}}, {"data": {"id": 3007006, "name": "前沿科技", "description": "AI、云计算、大数据、5G 等前沿信息技术"}}, {"data": {"id": 3007007, "name": "互联网", "description": "互联网行业资讯相关内容"}}]}, {"data": {"id": 3016000, "name": "教育"}, "children": [{"data": {"id": 3016001, "name": "K12 教育", "description": "幼儿园至中小学阶段经验、方法、观点等分享"}}, {"data": {"id": 3016002, "name": "升学考试", "description": "中高考、考研、公考、留学及其他各类考试学习经验"}}, {"data": {"id": 3016003, "name": "语言学习", "description": "语言类学习考试经验分享"}}, {"data": {"id": 3016004, "name": "其他教育", "description": "艺术类、技能类教学，教育方法、观点分享等"}}]}, {"data": {"id": 3003000, "name": "影视"}, "children": [{"data": {"id": 3003001, "name": "电影", "description": "电影或其中角色相关解读、剪辑、杂谈、科普、资讯等"}}, {"data": {"id": 3003002, "name": "电视剧", "description": "电视剧或其中角色相关解读、剪辑、杂谈、科普、资讯等"}}, {"data": {"id": 3003003, "name": "综艺", "description": "综艺或其嘉宾相关杂谈、reaction、剪辑等"}}, {"data": {"id": 3003004, "name": "其他影视", "description": "短片、短剧、纪录片、剧情模仿、魔改配音等"}}]}, {"data": {"id": 3012000, "name": "商业财经"}, "children": [{"data": {"id": 3012001, "name": "财经热点", "description": "商业财经领域热点带动分享观点，事件解读类内容为主"}}, {"data": {"id": 3012002, "name": "投资理财", "description": "投资理念普及，投资方向判断，分享投资类产品"}}, {"data": {"id": 3012003, "name": "经济科普", "description": "以经济学知识科普为主要内容"}}, {"data": {"id": 3012004, "name": "商业解读", "description": "解析商业现象、商业模式，偏较为深度的分析"}}]}, {"data": {"id": 3005000, "name": "娱乐"}, "children": [{"data": {"id": 3005001, "name": "明星名人", "description": "明星表演片段、专访、饭制混剪、幕后花絮等"}}, {"data": {"id": 3005002, "name": "娱乐资讯", "description": "娱乐明星、网红为主体的新闻资讯等"}}, {"data": {"id": 3005003, "name": "音乐", "description": "各类乐器/歌曲表演、赏析、教程等"}}, {"data": {"id": 3005004, "name": "舞蹈", "description": "各类舞蹈表演、赏析、教程等"}}]}, {"data": {"id": 3010000, "name": "健康"}, "children": [{"data": {"id": 3010001, "name": "疾病防治", "description": "疾病科普、预防与诊疗介绍、用药指导等"}}, {"data": {"id": 3010002, "name": "日常保健", "description": "日常保健、健康常识、减肥等"}}, {"data": {"id": 3010003, "name": "食品营养", "description": "膳食健康、营养学、食品安全、食品科学等"}}, {"data": {"id": 3010004, "name": "医疗美容", "description": "医学整形、医疗美容等"}}]}, {"data": {"id": 3008000, "name": "科学技术"}, "children": [{"data": {"id": 3008001, "name": "科学科普", "description": "物理、化学、生物、数学等基础学科知识"}}, {"data": {"id": 3008002, "name": "航空航天", "description": "航空、航天类工程技术"}}, {"data": {"id": 3008003, "name": "地球自然", "description": "自然生态、环境保护、自然灾害、气象、地理、地质等"}}, {"data": {"id": 3008004, "name": "花卉农业", "description": "花卉种植、园艺、农业、畜牧养殖、水产等"}}, {"data": {"id": 3008005, "name": "机械电气", "description": "机械、电气、自动化、工业制造、国防军工等"}}, {"data": {"id": 3008006, "name": "建筑交通", "description": "建筑、土木、水利、交通、运输等"}}, {"data": {"id": 3008007, "name": "其他科学技术", "description": "其他不便分类的科学技术类内容"}}]}, {"data": {"id": 3011000, "name": "情感"}, "children": [{"data": {"id": 3011001, "name": "恋爱", "description": "探讨与「恋爱关系」相关的内容"}}, {"data": {"id": 3011002, "name": "婚姻家庭", "description": "探讨与「婚姻关系」或「家庭关系」相关的内容"}}, {"data": {"id": 3011003, "name": "人际交往", "description": "探讨与人际交往相关的内容"}}, {"data": {"id": 3011004, "name": "其他情感", "description": "其他与情感相关的内容"}}]}, {"data": {"id": 3006000, "name": "体育"}, "children": [{"data": {"id": 3006001, "name": "足球", "description": "与足球相关的比赛、训练、实战、解读、花絮花边等内容"}}, {"data": {"id": 3006002, "name": "篮球", "description": "与篮球相关的比赛、训练、实战、解读、花絮花边等内容"}}, {"data": {"id": 3006003, "name": "综合体育", "description": "羽毛球、网球、台球、棋牌、搏击等体育项目的相关视频"}}, {"data": {"id": 3006004, "name": "运动健身", "description": "室内外健身、跑步、户外运动、极限运动等项目相关视频"}}]}, {"data": {"id": 3002000, "name": "游戏"}, "children": [{"data": {"id": 3002001, "name": "单机游戏", "description": "有关单机游戏的流程实况、玩法教学及介绍视频"}}, {"data": {"id": 3002002, "name": "网络游戏", "description": "有关网络游戏的流程实况、玩法教学及介绍视频"}}, {"data": {"id": 3002003, "name": "手机游戏", "description": "有关手机游戏的流程实况、玩法教学及介绍视频"}}, {"data": {"id": 3002004, "name": "桌面游戏", "description": "桌面游戏、棋类牌类游戏的教学和介绍推荐视频"}}, {"data": {"id": 3002005, "name": "电子竞技", "description": "电子竞技类游戏集锦、片段、原创策划内容的相关视频"}}]}, {"data": {"id": 3013000, "name": "社科人文"}, "children": [{"data": {"id": 3013001, "name": "法律", "description": "案例分析、案件分析，法律知识分享、行业交流等"}}, {"data": {"id": 3013002, "name": "心理", "description": "日常中的心理学、事件中的心理学角度解读等"}}, {"data": {"id": 3013003, "name": "历史", "description": "历史观察、历史发现等内容"}}, {"data": {"id": 3013004, "name": "文学", "description": "文学作品解读、欣赏、讨论等内容"}}, {"data": {"id": 3013005, "name": "艺术", "description": "艺术评鉴、艺术史、艺术教学等内容"}}, {"data": {"id": 3013006, "name": "其他社科人文", "description": "社会、民俗、政治及其他社科人文内容"}}]}, {"data": {"id": 3004000, "name": "时尚"}, "children": [{"data": {"id": 3004001, "name": "彩妆化妆", "description": "化妆品种草测评，妆容的分享、教程、展示等"}}, {"data": {"id": 3004002, "name": "美容护肤", "description": "涵盖护肤、保养、个人护理等技巧及产品分享"}}, {"data": {"id": 3004003, "name": "造型搭配", "description": "服饰、发型等穿搭造型风格及技巧的展示分享"}}, {"data": {"id": 3004004, "name": "时尚资讯", "description": "时装走秀，时尚专访、潮流街拍购物等分享"}}]}, {"data": {"id": 3009000, "name": "汽车"}, "children": [{"data": {"id": 3009001, "name": "选购指南", "description": "汽车选购指南、体验评测等内容"}}, {"data": {"id": 3009002, "name": "汽车养护", "description": "养车经验、养护方法等内容"}}, {"data": {"id": 3009003, "name": "驾驶技巧", "description": "汽车驾驶经验、技巧等内容"}}, {"data": {"id": 3009004, "name": "行业资讯", "description": "汽车行业资讯、研发制造、智能科技等内容"}}]}, {"data": {"id": 3015000, "name": "母婴家庭"}, "children": [{"data": {"id": 3015001, "name": "孕育知识", "description": "备孕、怀孕、育儿相关的科普知识"}}, {"data": {"id": 3015002, "name": "儿童教育", "description": "早教、儿童心理、家庭教育、亲子关系经验"}}, {"data": {"id": 3015003, "name": "母婴好物", "description": "备孕、孕产、婴幼儿用品、早教产品等好物分享"}}, {"data": {"id": 3015004, "name": "妈妈成长", "description": "产后恢复、辣妈穿搭妆容、职场妈妈、全职妈妈身心成长"}}, {"data": {"id": 3015005, "name": "亲子日常", "description": "亲子游戏、亲子 DIY、亲子游、萌娃日常等"}}]}, {"data": {"id": 3018000, "name": "资讯"}, "children": [{"data": {"id": 3018001, "name": "国内", "description": "国内综合资讯相关的视频内容"}}, {"data": {"id": 3018002, "name": "国际", "description": "国际资讯相关的视频内容"}}, {"data": {"id": 3018003, "name": "军事", "description": "军事相关的视频内容"}}]}, {"data": {"id": 3017000, "name": "职场"}, "children": [{"data": {"id": 3017001, "name": "求职招聘", "description": "校招实习、社招相关，如准备简历、笔面试"}}, {"data": {"id": 3017002, "name": "职场技能", "description": "数据分析、编程、设计等职场硬技能"}}, {"data": {"id": 3017003, "name": "职场经验", "description": "职业规划、职场沟通、上下级管理等软性能力"}}, {"data": {"id": 3017004, "name": "其他职场", "description": "其他职场相关内容"}}]}, {"data": {"id": 3001000, "name": "动漫"}, "children": [{"data": {"id": 3001001, "name": "动画漫画", "description": "动画、漫画相关的剪辑和 MAD 类视频"}}, {"data": {"id": 3001002, "name": "手办模玩", "description": "有关模型和玩具的开箱、评价等视频"}}, {"data": {"id": 3001003, "name": "Cosplay", "description": "与 ACG 相关的扮装、仿妆类视频"}}, {"data": {"id": 3001004, "name": "动漫综合", "description": "动画、漫画杂谈和科普类视频"}}]}]