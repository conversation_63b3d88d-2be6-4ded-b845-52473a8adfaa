/**
 * 各平台视频分类数据的TypeScript类型定义
 */

// 知乎分类数据类型
export interface ZhiHuCategoryData {
  id: number
  name: string
  description?: string
}

export interface ZhiHuCategoryChild {
  data: ZhiHuCategoryData
}

export interface ZhiHuCategory {
  data: ZhiHuCategoryData
  children?: ZhiHuCategoryChild[]
}

// B站分类数据类型
export interface BilibiliCategoryChild {
  id: number
  parent: number
  parent_name: string
  name: string
  description: string
  desc: string
  intro_original: string
  intro_copy: string
  notice: string
  copy_right: number
  show: boolean
  rank: number
  max_video_count: number
  request_id: string
}

export interface BilibiliCategory {
  id: number
  parent: number
  parent_name: string
  name: string
  description: string
  desc: string
  intro_original: string
  intro_copy: string
  notice: string
  copy_right: number
  show: boolean
  rank: number
  children?: BilibiliCategoryChild[]
  max_video_count: number
  request_id: string
}

// 网易号分类数据类型
export interface WangYiHaoCategory {
  categoryName: string
  level: number
  subCategoryList: WangYiHaoCategory[] | null
}

// 一点号分类数据类型
export type YiDianHaoCategories = Record<string, string[]>

// 爱奇艺分类数据类型
export interface AiQiYiCategory {
  channelId: number
  classifyId: string
  classifyName: string
  showName: string
  twClassifyName: string
  twShowName: string
}

// 企鹅号分类数据类型
export interface QiEHaoCategory {
  cat: string
  subcats: string[]
}

// 搜狐号分类数据类型
export interface SouHuHaoSubItem {
  id: number
  name: string
  channelId: number
  status: number
  createdTime: number
}

export interface SouHuHaoCategory {
  id: number
  name: string
  cmsChannelId: number
  cmsPName: string
  status: number
  level: number
  subItems: SouHuHaoSubItem[]
}

// 统一的选项类型，T为主项类型，C为子项类型（可选，默认与T相同）
export interface Option<T = unknown, C = T> {
  id: string
  label: string
  children?: Option<C>[]
  raw: T
} 