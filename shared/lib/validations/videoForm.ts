import { z } from 'zod'
import { getPlatformByName } from '@/lib/platform'
import { getVideoSpecification } from '@/lib/specification/content-type/video/video-specification'
import type { FormDataSchema } from './formTypeSchema'
import {
  cascadingPlatformDataItemSchema,
  imageBaseSchema,
  platformAccountSchema,
  videoBaseSchema,
} from './formTypeSchema'

// 验证规则类型
type ValidationRule<T> = {
  test: (val: T) => boolean
  message: string
  platformName: string
}

// 构建验证Schema
export const buildVideoValidationSchema = (selectedPlatforms: string[]) => {
  // 初始化各个字段的验证规则
  const aPlatformRules: ValidationRule<FormDataSchema['aPlatform']>[] = []
  const bPlatformRules: ValidationRule<FormDataSchema['bPlatform']>[] = []
  const videoAccountsRules: ValidationRule<FormDataSchema['videoAccounts']>[] = []
  const timingRules: ValidationRule<FormDataSchema['timing']>[] = []
  const categoryRules: ValidationRule<FormDataSchema['categories']>[] = []

  // 收集每个平台的验证规则
  selectedPlatforms.forEach((platformName) => {
    const platform = getPlatformByName(platformName)
    const spec = getVideoSpecification(platform)
    const isModern = spec.isModern
    const platformDisplayName = platform.name

    const platformRules = isModern ? aPlatformRules : bPlatformRules

    // 根据平台类型确定规则应用的目标
    // === aPlatform 验证规则 ===

    // 标题验证
    if (spec.titleRequired) {
      platformRules.push({
        test: (val) => Boolean(val && val.title.length > 0),
        message: `【${platformDisplayName}】标题-不能为空`,
        platformName,
      })
    }

    if (spec.titleMinLength) {
      platformRules.push({
        test: (val) => !val || val.title.length >= (spec.titleMinLength || 0),
        message: `【${platformDisplayName}】标题-不能少于${spec.titleMinLength}个字`,
        platformName,
      })
    }

    if (spec.titleMaxLength) {
      platformRules.push({
        test: (val) => !val || val.title.length <= (spec.titleMaxLength || 0),
        message: `【${platformDisplayName}】标题-不能超过${spec.titleMaxLength}个字`,
        platformName,
      })
    }

    // 描述验证
    if (spec.descriptionMinLength) {
      platformRules.push({
        test: (val) => !val || val.description.length >= (spec.descriptionMinLength || 0),
        message: `【${platformDisplayName}】描述-不能少于${spec.descriptionMinLength}个字`,
        platformName,
      })
    }

    if (spec.descriptionMaxLength) {
      platformRules.push({
        test: (val) => !val || val.description.length <= (spec.descriptionMaxLength || 0),
        message: `【${platformDisplayName}】描述-不能超过${spec.descriptionMaxLength}个字`,
        platformName,
      })
    }

    // 话题验证
    if (spec.topicMaxCount) {
      aPlatformRules.push({
        test: (val) => !val || val.topics.length <= (spec.topicMaxCount || 0),
        message: `【${platformDisplayName}】话题-数量不能超过${spec.topicMaxCount}个`,
        platformName,
      })
    }
    if (spec.topicMaxLength) {
      aPlatformRules.push({
        test: (val) =>
          !val || !val.topics.some((topic) => topic.length > (spec.topicMaxLength || 0)),
        message: `【${platformDisplayName}】话题-单个长度不能超过${spec.topicMaxLength}个字`,
        platformName,
      })
    }
    // 标签验证
    if (spec.tagMinCount) {
      bPlatformRules.push({
        test: (val) => !val || val.tags.length >= (spec.tagMinCount || 0),
        message: `【${platformDisplayName}】标签-数量不能少于${spec.tagMinCount}个`,
        platformName,
      })
    }
    if (spec.tagMaxCount) {
      bPlatformRules.push({
        test: (val) => !val || val.tags.length <= (spec.tagMaxCount || 0),
        message: `【${platformDisplayName}】标签-数量不能超过${spec.tagMaxCount}个`,
        platformName,
      })
    }

    if (spec.tagMaxLength) {
      bPlatformRules.push({
        test: (val) => !val || !val.tags.some((tag) => tag.length > (spec.tagMaxLength || 0)),
        message: `【${platformDisplayName}】标签-单个长度不能超过${spec.tagMaxLength}个字`,
        platformName,
      })
    }

    // === 共享验证规则 ===

    // 封面验证
    if (spec.coverMaxByteSize) {
      const maxSize = spec.coverMaxByteSize.bytes
      videoAccountsRules.push({
        test: (val) => {
          if (val instanceof Array) {
            return val
              .filter((item) => item.platforms?.platformName === platform.name)
              .every((item) => {
                return (item.cover?.size ?? 0) <= maxSize
              })
          } else return (val.cover?.size ?? 0) <= maxSize
        },
        message: `【${platformDisplayName}】封面-图片大小不能超过${spec.coverMaxByteSize.toString()}`,
        platformName,
      })
    }

    // 视频验证
    if (spec.videoMaxByteSize) {
      const videoMaxSize = spec.videoMaxByteSize?.bytes || 0
      videoAccountsRules.push({
        test: (val) => {
          if (val instanceof Array) {
            return val
              .filter((item) => item.platforms?.platformName === platform.name)
              .every((item) => {
                return (item.video?.size ?? 0) <= videoMaxSize
              })
          } else return (val.video?.size ?? 0) <= videoMaxSize
        },
        message: `【${platformDisplayName}】视频-文件大小不能超过${spec.videoMaxByteSize?.toString() || ''}`,
        platformName,
      })
    }

    if (spec.videoMaxDuration) {
      const videoMaxDuration = spec.videoMaxDuration?.seconds || 0
      videoAccountsRules.push({
        test: (val) => {
          if (val instanceof Array) {
            return val
              .filter((item) => item.platforms?.platformName === platform.name)
              .every((item) => {
                return (item.video?.duration ?? 0) <= videoMaxDuration
              })
          } else return (val.video?.duration ?? 0) <= videoMaxDuration
        },
        message: `【${platformDisplayName}】视频-时长不能超过${spec.videoMaxDuration?.toString() || ''}`,
        platformName,
      })
    }

    // 发布时间验证
    if (spec.scheduledTimeSupport) {
      if (spec.scheduledTimeMinTimeSpan) {
        const minTime = new Date()
        minTime.setSeconds(minTime.getSeconds() + spec.scheduledTimeMinTimeSpan.seconds)

        timingRules.push({
          test: (val) => !val || val >= minTime.getTime(),
          message: `【${platformDisplayName}】发布时间-不能早于${spec.scheduledTimeMinTimeSpan.toString()}后`,
          platformName,
        })
      }

      if (spec.scheduledTimeMaxTimeSpan) {
        const maxTime = new Date()
        maxTime.setSeconds(maxTime.getSeconds() + spec.scheduledTimeMaxTimeSpan.seconds)

        timingRules.push({
          test: (val) => !val || val <= maxTime.getTime(),
          message: `【${platformDisplayName}】发布时间-不能晚于${spec.scheduledTimeMaxTimeSpan.toString()}后`,
          platformName,
        })
      }
    }

    // 分类验证
    if (spec.categoryRequired) {
      categoryRules.push({
        test: (val) => !!val?.[platform.key],
        message: `【${platformDisplayName}】分类-不能为空`,
        platformName,
      })
    }
  })

  // 创建基础Schema，为每个字段应用收集的验证规则
  return z.object({
    aPlatform: z
      .object({
        title: z.string(),
        description: z.string(),
        topics: z.array(z.string()),
      })
      .superRefine((val, ctx) => {
        aPlatformRules.forEach((rule) => {
          if (!rule.test(val)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: rule.message,
              params: {
                platformName: rule.platformName,
              },
            })
          }
        })
      }),
    bPlatform: z
      .object({
        title: z.string(),
        description: z.string(),
        tags: z.array(z.string()),
      })
      .superRefine((val, ctx) => {
        bPlatformRules.forEach((rule) => {
          if (!rule.test(val)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: rule.message,
              params: {
                platformName: rule.platformName,
              },
            })
          }
        })
      }),
    videoAccounts: z
      .union([
        z.object({
          cover: imageBaseSchema.optional(),
          video: videoBaseSchema.optional(),
          platforms: z.array(platformAccountSchema),
        }),
        z.array(
          z.object({
            cover: imageBaseSchema.optional(),
            video: videoBaseSchema.optional(),
            platforms: platformAccountSchema.optional(),
          }),
        ),
      ])
      .superRefine((val, ctx) => {
        if (val instanceof Array) {
          if (val.length === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `【${allPlatformName}】视频-请选择视频`,
            })
          }
          val.some((item) => {
            if (!item.platforms) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `【${allPlatformName}】账号-请选择账号`,
              })
            }
          })
        } else {
          if (!val.video) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `【${allPlatformName}】视频-请选择视频`,
            })
          }
          if (val.video && !val.cover) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `【${allPlatformName}】封面-请选择封面`,
            })
          }
          if (!val.platforms.length) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `【${allPlatformName}】账号-请选择账号`,
            })
          }
        }
        videoAccountsRules.forEach((rule) => {
          if (!rule.test(val)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: rule.message,
              params: {
                platformName: rule.platformName,
              },
            })
          }
        })
      }),
    isOriginal: z.boolean(),
    timing: z.number().superRefine((val, ctx) => {
      timingRules.forEach((rule) => {
        if (!rule.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            params: {
              platformName: rule.platformName,
            },
          })
        }
      })
    }),
    categories: z
      .record(z.string(), z.array(cascadingPlatformDataItemSchema).optional())
      .optional()
      .superRefine((val, ctx) => {
        categoryRules.forEach((rule) => {
          if (!rule.test(val)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: rule.message,
              params: {
                platformName: rule.platformName,
              },
            })
          }
        })
      }),
  })
}

export const allPlatformName = 'All'
