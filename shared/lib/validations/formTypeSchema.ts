import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

// 基础类型定义
export const imageBaseInfoSchema = z.object({
  url: z.string(),
  width: z.number(),
  height: z.number(),
  size: z.number(),
  type: z.string(),
})

export const imageBaseSchema = z.union([
  imageBaseInfoSchema.extend({ path: z.string() }),
  imageBaseInfoSchema.extend({ arrayBuffer: z.instanceof(ArrayBuffer) }),
])

export const videoBaseInfoSchema = z.object({
  url: z.string(),
  duration: z.number(),
  width: z.number(),
  height: z.number(),
  size: z.number(),
  type: z.string(),
})

export const videoBaseSchema = z.union([
  videoBaseInfoSchema.extend({ file: z.instanceof(File) }),
  videoBaseInfoSchema.extend({ path: z.string() }),
])

export const platformAccountSchema = z.object({
  id: z.string(),
  groups: z.array(z.string()),
  isFreeze: z.boolean(),
  isOperate: z.boolean(),
  platformAccountName: z.string(),
  platformAuthorId: z.string(),
  platformAvatar: z.string(),
  platformName: z.string(),
  remark: z.string(),
  status: z.number(),
})

// 前向声明递归类型
type CascadingPlatformDataItemType = z.ZodObject<{
  id: z.ZodString
  text: z.ZodString
  children: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodTypeAny>>>
  raw: z.ZodUnknown
}>

export const cascadingPlatformDataItemSchema: CascadingPlatformDataItemType = z.object({
  id: z.string(),
  text: z.string(),
  children: z.array(z.lazy(() => cascadingPlatformDataItemSchema)).optional(),
  raw: z.unknown(),
})

export const platformDataItemSchema = z.object({
  id: z.string(),
  text: z.string(),
  raw: z.unknown(),
}).optional()

export const musicPlatformDataItemSchema = z.object({
  yixiaoerId: z.string(),
  yixiaoerName: z.string(),
  authorName: z.string(),
  playUrl: z.string(),
  duration: z.number(),
  raw: z.unknown(),
})

export type MusicPlatformDataItem = z.infer<typeof musicPlatformDataItemSchema>

// 定义主表单Schema
export const formDataSchema = z.object({
  aPlatform: z.object({
    title: z.string(),
    description: z.string(),
    topics: z.array(z.string()),
  }),
  bPlatform: z.object({
    title: z.string(),
    description: z.string(),
    tags: z.array(z.string()),
  }),
  videoAccounts: z.union([
    z.object({
      cover: imageBaseSchema.optional(),
      video: videoBaseSchema.optional(),
      platforms: z.array(platformAccountSchema),
    }),
    z.array(
      z.object({
        cover: imageBaseSchema.optional(),
        video: videoBaseSchema.optional(),
        platforms: platformAccountSchema.optional(),
      }),
    ),
  ]),
  isOriginal: z.boolean(),
  timing: z.number(),
  categories: z.record(z.string(), z.array(cascadingPlatformDataItemSchema).optional()).optional(),
  location: z.record(z.string(), platformDataItemSchema.optional()).optional(),
})

// 添加类型导出
export type FormDataSchema = z.infer<typeof formDataSchema>

// 创建一个"宽松"版本的Schema，适合用于表单验证（允许undefined和null值）
export const formDataSchemaForValidation = formDataSchema.partial()

/**
 * 创建一个与FormData接口兼容的验证模式
 * 使用此函数可以避免在useForm中使用as any
 */
export function createFormDataCompatibleSchema<T extends z.ZodTypeAny>(schema: T) {
  return {
    schema,
    zodResolver: zodResolver(schema),
  }
}
