import { z } from 'zod'
import { getPlatformByName } from '@/lib/platform'
import { getImageTextSpecification } from '@/lib/specification/content-type/image-text/image-text-specification'
import { imageBaseSchema, musicPlatformDataItemSchema } from './formTypeSchema'
import type { ImageBase } from '@/types/media'
import { ByteSize } from '../byte-size'

// 验证规则类型
type ValidationRule<T> = {
  test: (val: T) => boolean
  message: string
  platformName: string
  params?: Record<string, unknown>
}

// 构建验证Schema
export const buildImageTextValidationSchema = (selectedPlatformNames: string[]) => {
  // 初始化各个字段的验证规则
  const titleRules: ValidationRule<string>[] = []
  const descriptionRules: ValidationRule<string>[] = []
  const topicsRules: ValidationRule<string[]>[] = []
  const imagesRules: ValidationRule<ImageBase[]>[] = []
  const coverRules: ValidationRule<ImageBase | undefined>[] = []
  const timingRules: ValidationRule<number>[] = []

  // 收集每个平台的验证规则
  selectedPlatformNames.forEach((platformName) => {
    const platform = getPlatformByName(platformName)
    const spec = getImageTextSpecification(platform)
    const platformDisplayName = platform.name

    // === 内容验证规则 ===

    // 标题验证
    if (spec.titleSupport && spec.titleRequired) {
      titleRules.push({
        test: (val) => Boolean(val && val.length > 0),
        message: `【${platformDisplayName}】标题-不能为空`,
        platformName,
      })
    }

    if (spec.titleSupport && spec.titleMaxLength) {
      titleRules.push({
        test: (val) => !val || val.length <= (spec.titleMaxLength || 0),
        message: `【${platformDisplayName}】标题-不能超过${spec.titleMaxLength}个字`,
        platformName,
      })
    }

    // 描述验证
    if (spec.descriptionRequired) {
      descriptionRules.push({
        test: (val) => Boolean(val && val.length > 0),
        message: `【${platformDisplayName}】描述-不能为空`,
        platformName,
      })
    }

    if (spec.descriptionMaxLength) {
      descriptionRules.push({
        test: (val) => !val || val.length <= (spec.descriptionMaxLength || 0),
        message: `【${platformDisplayName}】描述-不能超过${spec.descriptionMaxLength}个字`,
        platformName,
      })
    }

    // 话题验证
    if (spec.topicMaxCount) {
      topicsRules.push({
        test: (val) => !val || val.length <= (spec.topicMaxCount || 0),
        message: `【${platformDisplayName}】话题-数量不能超过${spec.topicMaxCount}个`,
        platformName,
      })
    }

    if (spec.topicMaxLength) {
      topicsRules.push({
        test: (val) => !val || !val.some((topic) => topic.length > (spec.topicMaxLength || 0)),
        message: `【${platformDisplayName}】话题-单个长度不能超过${spec.topicMaxLength}个字`,
        platformName,
      })
    }

    // === 媒体验证规则 ===

    // 图片验证
    if (spec.imageRequired) {
      imagesRules.push({
        test: (val) => val && val.length > 0,
        message: `【${platformDisplayName}】图片-至少需要上传一张图片`,
        platformName,
      })
    }

    if (spec.imageMaxByteSize) {
      const maxSize = spec.imageMaxByteSize.bytes
      imagesRules.push({
        test: (val) => !val || !val.some((image) => (image?.size ?? 0) > maxSize),
        message: `【${platformDisplayName}】图片-单张大小不能超过${spec.imageMaxByteSize.toString()}`,
        platformName,
        params: { maxSize: spec.imageMaxByteSize },
      })
    }

    // 封面验证
    if (spec.coverSupport && spec.coverRequired) {
      coverRules.push({
        test: (val) => Boolean(val),
        message: `【${platformDisplayName}】封面-不能为空`,
        platformName,
      })
    }

    // 发布时间验证
    if (spec.scheduledTimeSupport) {
      if (spec.scheduledTimeMinTimeSpan) {
        const minTime = new Date()
        minTime.setSeconds(minTime.getSeconds() + spec.scheduledTimeMinTimeSpan.seconds)

        timingRules.push({
          test: (val) => !val || val >= minTime.getTime(),
          message: `【${platformDisplayName}】发布时间-不能早于${spec.scheduledTimeMinTimeSpan.toString()}后`,
          platformName,
        })
      }

      if (spec.scheduledTimeMaxTimeSpan) {
        const maxTime = new Date()
        maxTime.setSeconds(maxTime.getSeconds() + spec.scheduledTimeMaxTimeSpan.seconds)

        timingRules.push({
          test: (val) => !val || val <= maxTime.getTime(),
          message: `【${platformDisplayName}】发布时间-不能晚于${spec.scheduledTimeMaxTimeSpan.toString()}后`,
          platformName,
        })
      }
    }
  })

  // 创建基础Schema，为每个字段应用收集的验证规则
  return z.object({
    title: z.string().superRefine((val, ctx) => {
      titleRules.forEach((rule) => {
        if (!rule.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            params: {
              platformName: rule.platformName,
            },
          })
        }
      })
    }),
    description: z.string().superRefine((val, ctx) => {
      descriptionRules.forEach((rule) => {
        if (!rule.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            params: {
              platformName: rule.platformName,
            },
          })
        }
      })
    }),
    topics: z.array(z.string()).superRefine((val, ctx) => {
      topicsRules.forEach((rule) => {
        if (!rule.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            params: {
              platformName: rule.platformName,
            },
          })
        }
      })
    }),
    images: z.array(imageBaseSchema).superRefine((val, ctx) => {
      imagesRules.forEach((rule) => {
        if (!rule.test(val)) {
          if (rule.params?.maxSize) {
            const maxSize = rule.params.maxSize as ByteSize
            const indexs = val.reduce((acc, image, index) => {
              if (image?.size && image.size > maxSize.bytes) {
                return [...acc, index + 1]
              } else {
                return acc
              }
            }, [] as number[])
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `【${rule.platformName}】图片[${indexs.join(',')}]-单张大小不能超过${maxSize.toString()}`,
              params: {
                platformName: rule.platformName,
                indexs,
              },
            })
          } else {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: rule.message,
              params: {
                platformName: rule.platformName,
              },
            })
          }
        }
      })
    }),
    cover: imageBaseSchema.superRefine((val, ctx) => {
      coverRules.forEach((rule) => {
        if (!rule.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            params: {
              platformName: rule.platformName,
            },
          })
        }
      })
    }),
    music: z.record(z.string(), musicPlatformDataItemSchema.optional()).optional(),
    timing: z.number().superRefine((val, ctx) => {
      timingRules.forEach((rule) => {
        if (!rule.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            params: {
              platformName: rule.platformName,
            },
          })
        }
      })
    }),
  })
}

export const allPlatformName = 'All'

export type ImageTextForm = z.infer<ReturnType<typeof buildImageTextValidationSchema>>
