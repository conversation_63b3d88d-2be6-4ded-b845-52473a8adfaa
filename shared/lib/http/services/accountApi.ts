/**
 * 账号相关API
 *
 * 提供平台账号和分组管理的API接口，包括获取账号列表、分组列表、
 * 以及微信账号锁定相关的操作。
 *
 * @module accountApi
 */
import { http } from "../client";
import type {
  PlatformAccount,
  Groups,
  AccountListQuery,
  PaginatedResult,
} from "@/types";

/**
 * 获取平台账号列表
 *
 * 根据查询参数获取平台账号列表，支持分页、分组和平台筛选。
 *
 * @param {AccountListQuery} [params={}] - 查询参数对象
 * @param {number} [params.page] - 页码，默认为1
 * @param {number} [params.size] - 每页数量，默认为1000
 * @param {string} [params.group] - 按分组ID筛选
 * @param {string} [params.platform] - 按平台名称筛选
 * @returns {Promise<PaginatedResult<PlatformAccount>>} 分页的平台账号列表
 */
export const getAccountList = (params: AccountListQuery = {}) => {
  return http.get<PaginatedResult<PlatformAccount>>("/platform-accounts", {
    page: params.page || 1,
    size: params.size || 1000,
    ...params,
  });
};

/**
 * 获取账号分组列表
 *
 * 获取所有账号分组，默认每页返回100条数据。
 *
 * @returns {Promise<PaginatedResult<Groups>>} 分页的分组列表
 */
export const getGroupList = () => {
  return http.get<PaginatedResult<Groups>>("/groups", { size: 100 });
};

/**
 * 生成锁定微信账号密钥
 *
 * 为指定的平台账号生成微信锁定密钥，用于锁定微信账号操作。
 *
 * @param {string[]} platformAccountIds - 平台账号ID数组
 * @returns {Promise<Array<{platformAccountId: string; wxkey: string}>>} 包含平台账号ID和对应微信密钥的数组
 */
export const generateWechatLockKey = (platformAccountIds: string[]) => {
  return http.post<{ platformAccountId: string; wxkey: string }[]>(
    "/platform-accounts/wechat/keys",
    {
      platformAccountIds,
    }
  );
};

/**
 * 解锁微信账号密钥
 *
 * 使用微信密钥解锁指定的平台账号，返回微信cookie信息。
 *
 * @param {string} platformAccountId - 平台账号ID
 * @param {string} wxkey - 微信锁定密钥
 * @returns {Promise<{cookie: string}>} 包含微信cookie的对象
 */
export const unlockWechatLockKey = (
  platformAccountId: string,
  wxkey: string
) => {
  return http.request<{ cookie: string }>({
    url: `/platform-accounts/${platformAccountId}/wechat/keys`,
    method: "DELETE",
    headers: {
      wxkey: wxkey,
    },
  });
};

/**
 * 更新微信账号锁定有效时间
 *
 * 延长微信账号锁定的有效期，需要提供有效的微信账号锁密钥。
 *
 * @param {string} platformAccountId - 平台账号ID
 * @param {string} wxkey - 微信锁定密钥
 * @returns {Promise<{cookie: string}>} 包含更新后微信cookie的对象
 *
 * @see PUT /platform-accounts/{platformAccountId}/wechat/keys/alive
 * @see https://app.apifox.com/link/project/4626224/apis/api-*********
 */
export const updateWechatLockKeyAlive = (
  platformAccountId: string,
  wxkey: string
) => {
  return http.request<{ cookie: string }>({
    url: `/platform-accounts/${platformAccountId}/wechat/keys/alive`,
    method: "PUT",
    headers: {
      wxkey: wxkey,
    },
  });
};
