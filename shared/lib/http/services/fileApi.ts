/**
 * 文件上传相关API
 */
import { http } from '../client';
import { isString } from 'lodash';

// 存储桶类型
export type BucketType = 
  | 'assets'
  | 'attachments'
  | 'site-spaces'
  | 'material-library'
  | 'wechat-pubish'
  | 'cloud-publish';

// 渠道类型
export type ChannelType = 'aliyun' | 'tianyiyun';

/**
 * 获取资源直传地址
 * @param bucket 团队资源存储桶名
 * @param channel 渠道
 * @param checksum 校验和
 * @param fileKey 文件键
 */
export const getUploadUrl = (
  bucket: BucketType,
  channel: ChannelType = 'aliyun',
  checksum?: string,
  fileKey?: string,
) => {
  return http.get<{
    serviceUrl: string;
    key: string;
  }>(`/storages/${bucket}/upload-url`, {
    fileKey,
    checksum,
    channel,
  });
};

/**
 * 获取访问URL
 * @param bucket 存储桶
 * @param fileKey 文件键
 * @param channel 渠道
 */
export const getAccessUrl = (
  bucket: 'assets' | 'attachments' | 'site-spaces',
  fileKey?: string,
  channel: ChannelType = 'aliyun',
) => {
  return http.get<string>(`/storages/${bucket}/access-url`, {
    fileKey,
    channel,
  });
};

/**
 * 获取头部URL
 * @param bucket 存储桶
 * @param fileKey 文件键
 * @param channel 渠道
 */
export const getHeadUrl = (
  bucket: 'assets' | 'attachments' | 'site-spaces',
  fileKey?: string,
  channel: ChannelType = 'aliyun',
) => {
  return http.get<string>(`/storages/${bucket}/head-url`, {
    fileKey,
    channel,
  });
};

/**
 * 上传文件到服务器
 * 注意：此函数依赖于浏览器环境，在非浏览器环境中需要提供替代实现
 */
export const uploadFile = async (
  file: File | ArrayBuffer | string,
  bucket: BucketType,
  channel: ChannelType = 'aliyun',
  onProgress?: (progress: number) => void,
): Promise<string> => {
  const { serviceUrl, key } = await getUploadUrl(bucket, channel);
  
  if (typeof fetch === 'undefined') {
    throw new Error('fetch API is not available in this environment');
  }

  if (isString(file)) {
    // 如果是字符串（URL），需要先获取文件内容
    const response = await fetch(file);
    const blob = await response.blob();
    
    const formData = new FormData();
    formData.append('file', blob);
    
    await fetch(serviceUrl, {
      method: 'POST',
      body: formData,
    });
  } else {
    // 如果是 File 或 ArrayBuffer
    const blob = file instanceof File ? file : new Blob([file]);
    
    const formData = new FormData();
    formData.append('file', blob);
    
    const xhr = new XMLHttpRequest();
    xhr.open('POST', serviceUrl, true);
    
    if (onProgress) {
      xhr.upload.onprogress = (event: ProgressEvent) => {
        if (event.lengthComputable) {
          const progress = event.loaded / event.total;
          onProgress(progress);
        }
      };
    }
    
    await new Promise<void>((resolve, reject) => {
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };
      xhr.onerror = () => reject(new Error('Upload failed'));
      xhr.send(formData);
    });
  }
  
  return key;
};
