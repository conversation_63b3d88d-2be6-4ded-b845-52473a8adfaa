/**
 * 用户相关API
 */
import { http } from '../client';
import type { Auth, User, VerifyCodeRequest, AuthRequest } from '@/types';

/**
 * 发送短信验证码
 * @param params 验证码请求参数
 * @returns 验证码（仅开发环境返回）
 */
export const sendVerifyCode = (params: VerifyCodeRequest) => {
  return http.post<string | null>('/users/sms-code', params);
};

/**
 * 授权登录
 * @param params 授权请求参数
 * @returns 授权信息
 */
export const authorize = (params: AuthRequest) => {
  return http.post<Auth>('/users/auth', params);
};

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = () => {
  return http.get<User>('/users/info');
};

/**
 * 获取在线节点数
 * @returns 在线节点数
 */
export const getOnlineCount = () => {
  return http.get<number>('/app-tasks/onlines');
};
