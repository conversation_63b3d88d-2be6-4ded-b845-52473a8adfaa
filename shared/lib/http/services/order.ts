import { http } from '../client'
import type { Payinfo, OrderStatus } from '@/types'

/**
 * 获取订单支付信息
 * @param orderNo 订单号
 * @returns 订单支付信息
 */
export const getOrderPayInfo = (orderNo: string) => {
  return http.get<Payinfo>(`/orders/${orderNo}/payinfo`)
}

/**
 * 获取订单状态
 * @param orderNo 订单号
 * @returns 订单状态
 */
export const getOrderStatus = (orderNo: string) => {
  return http.get<{ orderStatus: OrderStatus }>(`/orders/${orderNo}/status`)
}
