/**
 * 团队相关API
 */
import { http } from '../client';
import type { Auth, Team, TeamListItem, PaginatedResult } from '@/types';

/**
 * 获取团队详情
 * @param teamId 团队ID
 * @returns 团队详情
 */
export const getTeamDetail = (teamId: string) => {
  return http.get<Team>(`/teams/${teamId}`);
};

/**
 * 获取团队列表
 * @returns 团队列表
 */
export const getTeamList = () => {
  return http.get<PaginatedResult<TeamListItem>>('/teams', { size: 100 });
};

/**
 * 切换团队
 * @param teamId 团队ID
 * @returns 授权信息
 */
export const authTeam = (teamId: string) => {
  return http.post<Auth>(`/teams/${teamId}/auth`, {});
};
