// src/lib/http/interceptors.ts
import type { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ApiConfig } from './config'
import * as auth from './authManager'
import type { ApiResult } from '@/types/api'
import { ApiError, ApiStatus, ResponseStatus } from '@/types/api'
import { toast } from 'sonner'

// --- Notification State and Helper ---
let lastNotificationTime = 0
let lastNotificationStatus: ResponseStatus | null = null
const notificationDebounceMs = ApiConfig.notificationDebounceMs

const showErrorNotification = (title: string, text: string, status: ResponseStatus | null) => {
  const now = Date.now()
  if (
    status !== null &&
    status === lastNotificationStatus &&
    now - lastNotificationTime < notificationDebounceMs
  ) {
    console.warn('Debouncing notification for status:', status)
    return
  }
  // f7.notification.create({ title, text, closeTimeout: 3000, cssClass: 'color-red' }).open()
  toast.error(text)
  lastNotificationTime = now
  lastNotificationStatus = status
}

// --- Interceptor Setup ---

/**
 * 设置请求拦截器
 * @param instance Axios 实例
 */
const setupRequestInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // 使用 InternalAxiosRequestConfig 获得更精确的类型
      const token = auth.getToken()
      if (token && token.length > 0) {
        // Axios > v1.x headers 类型处理
        if (!config.url?.startsWith('https')) {
          config.headers.authorization = token
        }
      }
      return config
    },
    (error) => {
      console.error('Request Error:', error)
      showErrorNotification(
        '请求错误',
        '无法发送请求，请检查网络或配置。',
        ResponseStatus.BAD_REQUEST,
      )
      return Promise.reject(new ApiError(ResponseStatus.BAD_REQUEST, '请求发送失败'))
    },
  )
}

/**
 * 设置响应拦截器
 * @param instance Axios 实例
 */
const setupResponseInterceptor = <T = unknown>(instance: AxiosInstance) => {
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResult<T>>) => {
      if (
        response.data?.statusCode !== undefined &&
        response.data?.statusCode !== ApiStatus.SUCCESS
      ) {
        const errorMessage = response.data?.message || '操作失败'
        const statusCode = response.data?.statusCode || ResponseStatus.SERVER_ERROR
        showErrorNotification('请求失败', errorMessage, statusCode as ResponseStatus)
        return Promise.reject(new ApiError(statusCode, errorMessage))
      }
      return response
    },
    (error) => {
      let apiError: ApiError
      let notificationText = '发生未知错误'
      let notificationTitle = '错误'
      let currentStatus: ResponseStatus | null = null

      if (error.response) {
        const statusCode = error.response.status as ResponseStatus
        currentStatus = statusCode
        const errorData = error.response.data as Partial<ApiResult<T>> | undefined
        const errorMessage = errorData?.message

        switch (statusCode) {
          case ResponseStatus.UNAUTHORIZED:
            notificationTitle = '未授权'
            notificationText = errorMessage || '请重新登录'
            auth.logout()
            break
          case ResponseStatus.FORBIDDEN:
            notificationTitle = '访问受限'
            notificationText = errorMessage || '您没有权限执行此操作'
            break
          case ResponseStatus.NOT_FOUND:
            notificationTitle = '资源不存在'
            notificationText = errorMessage || '请求的资源不存在'
            break
          case ResponseStatus.SERVER_ERROR:
            notificationTitle = '服务器错误'
            notificationText = errorMessage || '服务器处理请求时出错'
            break
          default:
            notificationTitle = '请求失败'
            notificationText = errorMessage || '请求失败'
        }
        apiError = new ApiError(statusCode, errorMessage || '请求失败')
      } else if (error.request) {
        currentStatus = ResponseStatus.SERVER_ERROR
        notificationTitle = '无响应'
        notificationText = '服务器未响应，请检查网络连接'
        apiError = new ApiError(ResponseStatus.SERVER_ERROR, '服务器无响应')
      } else {
        currentStatus = ResponseStatus.BAD_REQUEST
        notificationTitle = '请求配置错误'
        notificationText = error.message || '请求配置错误'
        apiError = new ApiError(ResponseStatus.BAD_REQUEST, notificationText)
      }

      showErrorNotification(notificationTitle, notificationText, currentStatus)
      return Promise.reject(apiError)
    },
  )
}

/**
 * 为 Axios 实例统一设置所有拦截器
 * @param instance Axios 实例
 */
export const setupInterceptors = (instance: AxiosInstance) => {
  setupRequestInterceptor(instance)
  setupResponseInterceptor(instance)
}
