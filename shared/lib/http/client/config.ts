/**
 * API配置
 */

import { Capacitor } from '@capacitor/core'

// API配置
export const ApiConfig = {
  // Set baseUrl based on environment
  // In development, use relative path /api which will be proxied
  // In production/staging, use the full URL from env variables
  baseUrl: import.meta.env.DEV ? 'api' : import.meta.env.VITE_API_BASE_URL + '/api',
  responseSuccess: 0,
  invalidToken: 401,
  timeout: 50000,
  headers: {
    'Content-Type': 'application/json',
    // Directly access Vite env variables
    'x-client': import.meta.env.VITE_APP_CLIENT_ID || '',
    'x-version': import.meta.env.VITE_APP_VERSION || '',
    'x-platform': Capacitor.getPlatform(),
  },
  notificationDebounceMs: 5000, // 错误通知去抖动时间 (ms)
}
