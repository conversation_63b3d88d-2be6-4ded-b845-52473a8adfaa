/**
 * 授权管理工具
 */

import { f7 } from 'framework7-react';

// 本地存储键
const TOKEN_KEY = 'token';

/**
 * 设置令牌
 * @param token 授权令牌
 */
export const setToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 获取令牌
 * @returns 令牌字符串
 */
export const getToken = (): string => {
  return localStorage.getItem(TOKEN_KEY) || '';
};

/**
 * 检查是否已登录
 * @returns 是否已登录
 */
export const checkLogin = (): boolean => {
  return Boolean(getToken());
};

/**
 * 登录成功处理
 * @param token 授权令牌
 * @param redirectUrl 可选的重定向URL
 */
export const loginSuccess = (token: string): void => {
  setToken(token);
};

/**
 * 退出登录
 * @param redirectUrl 可选的重定向URL
 */
export const logout = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  f7.views.main.router.navigate('/login/', {
    history: true,
  });
};
