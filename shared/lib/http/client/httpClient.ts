/**
 * HTTP客户端
 */
import type { AxiosInstance, AxiosRequestConfig } from 'axios'
import axios from 'axios'
import { ApiConfig } from './config'
import { setupInterceptors } from './interceptors' // 导入拦截器设置函数
import type { ApiResult } from '@/types/api'
import { ApiError } from '@/types/api' // ApiError 仍需在此处用于 baseRequest 的 catch 类型

// 直接创建和配置 Axios 实例
const axiosInstance: AxiosInstance = axios.create({
  baseURL: ApiConfig.baseUrl,
  timeout: ApiConfig.timeout,
  headers: ApiConfig.headers,
})

// 设置拦截器
setupInterceptors(axiosInstance)

/**
 * 发送请求的基础函数
 */
async function baseRequest<T>(config: AxiosRequestConfig): Promise<T> {
  try {
    const response = await axiosInstance.request<ApiResult<T>>(config)
    // 成功响应数据已在拦截器中校验，这里直接返回 data 字段
    // 注意：需要确保后端返回的 T 确实在 data 字段中
    return response.data.data as T
  } catch (error) {
    // 错误已在拦截器中处理并显示通知 (或被去抖动阻止)
    // 拦截器会 reject 一个 ApiError 实例，或者其他 Axios 错误
    // 为了类型安全，可以检查 error 是否为 ApiError 实例
    if (error instanceof ApiError) {
      throw error // 直接将拦截器 reject 的 ApiError 抛出
    } else {
      // 对于非 ApiError (例如网络错误、配置错误等)，也需要处理
      // 拦截器应该已经处理了这些情况并 reject 了 ApiError，
      // 但作为最后的保障，可以创建一个通用的 ApiError
      console.error('Caught non-ApiError in baseRequest:', error)
      throw new ApiError(500, 'An unexpected error occurred')
    }
  }
}

// --- 导出 API 方法 ---
// 可以直接导出配置好的 axios 实例，让调用者使用
export const httpClient = axiosInstance

// 或者，如果想保持之前的 get/post/put/delete 接口，可以这样封装：
export const http = {
  /**
   * GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @returns 响应数据
   */
  get: <T>(url: string, params?: Record<string, unknown>): Promise<T> => {
    return baseRequest<T>({ url, method: 'GET', params })
  },

  /**
   * POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns 响应数据
   */
  post: <T>(url: string, data?: unknown): Promise<T> => {
    return baseRequest<T>({ url, method: 'POST', data })
  },

  /**
   * PUT 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns 响应数据
   */
  put: <T>(url: string, data?: Record<string, unknown>): Promise<T> => {
    return baseRequest<T>({ url, method: 'PUT', data })
  },

  /**
   * DELETE 请求
   * @param url 请求地址
   * @param params 查询参数
   * @returns 响应数据
   */
  delete: <T>(url: string, params?: Record<string, unknown>): Promise<T> => {
    return baseRequest<T>({ url, method: 'DELETE', params })
  },

  /**
   * 自定义请求
   */
  request: baseRequest,
}
