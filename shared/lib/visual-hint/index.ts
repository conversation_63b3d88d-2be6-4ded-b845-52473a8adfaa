import type { ArticleSpecification } from '@/lib/specification/content-type/article/article-specification'
import type { ImageTextSpecification } from '@/lib/specification/content-type/image-text/image-text-specification'
import type { VideoSpecification } from '@/lib/specification/content-type/video/video-specification'
import type { PlatformSpecifications } from '@/lib/specification'
import { useMemo } from 'react'

export function useAnyTrue<
  T extends ArticleSpecification | ImageTextSpecification | VideoSpecification,
>(platformSpecs: PlatformSpecifications<T>[], condition: (specification: T) => boolean): boolean {
  return useMemo(() => {
    return platformSpecs.some((spec) => condition(spec.specification))
  }, [condition, platformSpecs])
}

export function usePlatforms<
  T extends ArticleSpecification | ImageTextSpecification | VideoSpecification,
>(platformSpecs: PlatformSpecifications<T>[], condition: (specification: T) => boolean) {
  return useMemo(() => {
    return platformSpecs
      .filter((spec) => condition(spec.specification))
      .map((spec) => spec.platform)
  }, [condition, platformSpecs])
}
