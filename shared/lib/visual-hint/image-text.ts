import type { Platform } from '@/lib/platform'
import type { ImageTextSpecification } from '@/lib/specification/content-type/image-text/image-text-specification'
import { getImageTextPlatformSpecifications } from '@/lib/specification/content-type/image-text/image-text-specification'
import { useMemo } from 'react'
import { useAnyTrue, usePlatforms } from './index'

// 方法放到外部是因为不会导致useXXX循环计算
const imageRequiredCondition = (specification: ImageTextSpecification) =>
  specification.imageRequired
const locationSupportCondition = (specification: ImageTextSpecification) =>
  specification.locationSupport
const coverSupportCondition = (specification: ImageTextSpecification) => specification.coverSupport
const coverRequiredCondition = (specification: ImageTextSpecification) =>
  specification.coverRequired
const descriptionRequiredCondition = (specification: ImageTextSpecification) =>
  specification.descriptionRequired
const titleSupportCondition = (specification: ImageTextSpecification) => specification.titleSupport
const titleRequiredCondition = (specification: ImageTextSpecification) =>
  specification.titleRequired
const musicSupportCondition = (specification: ImageTextSpecification) => specification.musicSupport
const scheduledTimeSupportCondition = (specification: ImageTextSpecification) =>
  specification.scheduledTimeSupport

export function useImageTextVisualHint(platforms: Platform[]) {
  const platformSpecifications = useMemo(() => {
    return getImageTextPlatformSpecifications(platforms)
  }, [platforms])

  const imageRequired = useAnyTrue(platformSpecifications, imageRequiredCondition)
  const locationSupport = useAnyTrue(platformSpecifications, locationSupportCondition)
  const locationPlatforms = usePlatforms(platformSpecifications, locationSupportCondition)
  const coverSupport = useAnyTrue(platformSpecifications, coverSupportCondition)
  const coverRequired = useAnyTrue(platformSpecifications, coverRequiredCondition)
  const descriptionRequired = useAnyTrue(platformSpecifications, descriptionRequiredCondition)
  const titleSupport = useAnyTrue(platformSpecifications, titleSupportCondition)
  const titleRequired = useAnyTrue(platformSpecifications, titleRequiredCondition)
  const musicSupport = useAnyTrue(platformSpecifications, musicSupportCondition)
  const scheduledTimeSupport = useAnyTrue(platformSpecifications, scheduledTimeSupportCondition)

  return useMemo(
    () => ({
      imageRequired,
      coverSupport,
      coverRequired,
      descriptionRequired,
      titleSupport,
      titleRequired,
      musicSupport,
      scheduledTimeSupport,
      locationSupport,
      locationPlatforms,
    }),
    [
      coverRequired,
      coverSupport,
      descriptionRequired,
      imageRequired,
      musicSupport,
      scheduledTimeSupport,
      titleRequired,
      titleSupport,
      locationSupport,
      locationPlatforms,
    ],
  )
}
