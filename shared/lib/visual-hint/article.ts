import type { Platform } from '@/lib/platform'
import { useMemo } from 'react'
import type { ArticleSpecification } from '@/lib/specification/content-type/article/article-specification'
import { getArticlePlatformSpecifications } from '@/lib/specification/content-type/article/article-specification'
import { useAnyTrue, usePlatforms } from './index'

export interface ArticleVisualHint {
  scheduledTimeSupport: boolean
  verticalCoverRequired: boolean
  firstPublishSupport: boolean
  categorySupport: boolean
  locationSupport: boolean
  coverRequired: boolean
  tagsSupport: boolean
  tagsRequired: boolean
  tagsPlatforms: Platform[]
  draftPublishSupport: boolean
}

// 方法放到外部是因为不会导致useXXX循环计算
const scheduledTimeSupportCondition = (spec: ArticleSpecification) => spec.scheduledTimeSupport
const firstPublishSupportCondition = (spec: ArticleSpecification) => spec.firstPublishSupport
const categorySupportCondition = (spec: ArticleSpecification) => spec.categorySupport
const locationSupportCondition = (spec: ArticleSpecification) => spec.locationSupport
const verticalCoverRequiredCondition = (spec: ArticleSpecification) => spec.verticalCoverRequired
const coverRequiredCondition = (spec: ArticleSpecification) => spec.coverRequired
const topicSupportCondition = (spec: ArticleSpecification) => spec.topicSupport
const topicRequiredCondition = (spec: ArticleSpecification) => spec.topicRequired
const draftPublishSupportCondition = (spec: ArticleSpecification) => spec.draftPublishSupport

export function useArticleVisualHint(platforms: Platform[]) {
  const platformSpecifications = useMemo(() => {
    return getArticlePlatformSpecifications(platforms)
  }, [platforms])

  const scheduledTimeSupport = useAnyTrue(platformSpecifications, scheduledTimeSupportCondition)
  const firstPublishSupport = useAnyTrue(platformSpecifications, firstPublishSupportCondition)
  const categorySupport = useAnyTrue(platformSpecifications, categorySupportCondition)
  const locationSupport = useAnyTrue(platformSpecifications, locationSupportCondition)
  const verticalCoverRequired = useAnyTrue(platformSpecifications, verticalCoverRequiredCondition)
  const coverRequired = useAnyTrue(platformSpecifications, coverRequiredCondition)
  const tagsSupport = useAnyTrue(platformSpecifications, topicSupportCondition)
  const tagsRequired = useAnyTrue(platformSpecifications, topicRequiredCondition)
  const tagsPlatforms = usePlatforms(platformSpecifications, topicRequiredCondition)
  const draftPublishSupport = useAnyTrue(platformSpecifications, draftPublishSupportCondition)

  return useMemo(
    () =>
      ({
        scheduledTimeSupport,
        verticalCoverRequired,
        firstPublishSupport,
        categorySupport,
        locationSupport,
        coverRequired,
        tagsSupport,
        tagsRequired,
        tagsPlatforms,
        draftPublishSupport,
      }) satisfies ArticleVisualHint as ArticleVisualHint,
    [
      scheduledTimeSupport,
      verticalCoverRequired,
      firstPublishSupport,
      categorySupport,
      locationSupport,
      coverRequired,
      tagsSupport,
      tagsRequired,
      tagsPlatforms,
      draftPublishSupport,
    ],
  )
}
