import type { Platform } from '@/lib/platform'
import type { VideoSpecification } from '@/lib/specification/content-type/video/video-specification'
import { getVideoPlatformSpecifications } from '@/lib/specification/content-type/video/video-specification'
import { useMemo } from 'react'
import { useAnyTrue, usePlatforms } from './index'

export interface VideoVisualHint {
  titleSupport: boolean
  modernTitleRequired: boolean
  classicTitleRequired: boolean
  scheduledTimeSupport: boolean
  originalSupport: boolean
  locationSupport: boolean
  locationPlatforms: Platform[]
  categorySupport: boolean
  categoryRequired: boolean
  categoryPlatforms: Platform[]
  tagSupport: boolean
  tagRequired: boolean
  modernVideoPlatforms: Platform[]
  hasModernVideoPlatforms: boolean
  classicVideoPlatforms: Platform[]
  hasClassicVideoPlatforms: boolean
  draftPublishSupport: boolean
}

// 方法放到外部是因为不会导致useXXX循环计算
const titleSupportCondition = (specification: VideoSpecification) => specification.titleSupport
const modernTitleRequiredCondition = (specification: VideoSpecification) =>
  specification.isModern &&
  (specification.titleRequired ||
    (specification.titleMinLength !== null && specification.titleMinLength > 0))

const classicTitleRequiredCondition = (specification: VideoSpecification) =>
  !specification.isModern &&
  (specification.titleRequired ||
    (specification.titleMinLength !== null && specification.titleMinLength > 0))

const scheduledTimeSupportCondition = (specification: VideoSpecification) =>
  specification.scheduledTimeSupport
const originalSupportCondition = (specification: VideoSpecification) =>
  specification.originalSupport
const locationSupportCondition = (specification: VideoSpecification) =>
  specification.locationSupport
const categorySupportCondition = (specification: VideoSpecification) =>
  specification.categorySupport
const categoryRequiredCondition = (specification: VideoSpecification) =>
  specification.categoryRequired
const tagSupportCondition = (specification: VideoSpecification) => specification.tagSupport
const tagRequiredCondition = (specification: VideoSpecification) =>
  specification.tagMinCount !== null && specification.tagMinCount > 0

const modernVideoPlatformCondition = (specification: VideoSpecification) => specification.isModern
const classicVideoPlatformCondition = (specification: VideoSpecification) => !specification.isModern

// 支持草稿发布的平台（部分是通过发布到私密支持的）
const draftPublishSupportCondition = (specification: VideoSpecification) =>
  specification.draftPublishSupport || specification.privatePublishSupport

export function useVideoVisualHint(platforms: Platform[]) {
  const platformSpecifications = useMemo(() => {
    return getVideoPlatformSpecifications(platforms)
  }, [platforms])
  const titleSupport = useAnyTrue(platformSpecifications, titleSupportCondition)
  const modernTitleRequired = useAnyTrue(platformSpecifications, modernTitleRequiredCondition)
  const classicTitleRequired = useAnyTrue(platformSpecifications, classicTitleRequiredCondition)

  const scheduledTimeSupport = useAnyTrue(platformSpecifications, scheduledTimeSupportCondition)
  const originalSupport = useAnyTrue(platformSpecifications, originalSupportCondition)
  const locationSupport = useAnyTrue(platformSpecifications, locationSupportCondition)
  const locationPlatforms = usePlatforms(platformSpecifications, locationSupportCondition)
  const categorySupport = useAnyTrue(platformSpecifications, categorySupportCondition)
  const categoryPlatforms = usePlatforms(platformSpecifications, categorySupportCondition)
  const categoryRequired = useAnyTrue(platformSpecifications, categoryRequiredCondition)
  const tagSupport = useAnyTrue(platformSpecifications, tagSupportCondition)
  const tagRequired = useAnyTrue(platformSpecifications, tagRequiredCondition)
  const modernVideoPlatforms = usePlatforms(platformSpecifications, modernVideoPlatformCondition)
  const hasModernVideoPlatforms = modernVideoPlatforms.length > 0
  const classicVideoPlatforms = usePlatforms(platformSpecifications, classicVideoPlatformCondition)
  const hasClassicVideoPlatforms = classicVideoPlatforms.length > 0
  const draftPublishSupport = useAnyTrue(platformSpecifications, draftPublishSupportCondition)

  return useMemo(
    () =>
      ({
        titleSupport,
        modernTitleRequired,
        classicTitleRequired,
        scheduledTimeSupport,
        originalSupport,
        locationSupport,
        locationPlatforms,
        categorySupport,
        categoryRequired,
        categoryPlatforms,
        tagSupport,
        tagRequired,
        modernVideoPlatforms,
        hasModernVideoPlatforms,
        classicVideoPlatforms,
        hasClassicVideoPlatforms,
        draftPublishSupport,
      }) satisfies VideoVisualHint as VideoVisualHint,
    [
      titleSupport,
      modernTitleRequired,
      classicTitleRequired,
      scheduledTimeSupport,
      originalSupport,
      locationSupport,
      locationPlatforms,
      categorySupport,
      categoryRequired,
      categoryPlatforms,
      tagSupport,
      tagRequired,
      modernVideoPlatforms,
      hasModernVideoPlatforms,
      classicVideoPlatforms,
      hasClassicVideoPlatforms,
      draftPublishSupport,
    ],
  )
}
