import DouYinIcon from '@/assets/images/platforms/dou-yin.png'
import KuaiShouIcon from '@/assets/images/platforms/kuai-shou.png'
import WeiXinShiPinHaoIcon from '@/assets/images/platforms/wei-xin-shi-pin-hao.png'
import XiaoHongShuIcon from '@/assets/images/platforms/xiao-hong-shu.png'
import BilibiliIcon from '@/assets/images/platforms/bilibili.png'
import BaiJiaHaoIcon from '@/assets/images/platforms/bai-jia-hao.png'
import TouTiaoHaoIcon from '@/assets/images/platforms/tou-tiao-hao.png'
import XinLangWeiBoIcon from '@/assets/images/platforms/xin-lang-wei-bo.png'
import ZhiHuIcon from '@/assets/images/platforms/zhihu.png'
import DaYuHaoIcon from '@/assets/images/platforms/da-yu-hao.png'
import QiEHaoIcon from '@/assets/images/platforms/qi-e-hao.png'
import SouHuHaoIcon from '@/assets/images/platforms/sou-hu-hao.png'
import YiDianHaoIcon from '@/assets/images/platforms/yi-dian-hao.png'
import WangYiHaoIcon from '@/assets/images/platforms/wang-yi-hao.png'
import AiQiYiIcon from '@/assets/images/platforms/ai-qi-yi.png'
import TengXunWeiShiIcon from '@/assets/images/platforms/teng-xun-wei-shi.png'
import UnknownPlatformIcon from '@/assets/images/platforms/unknow.png'
import WeiXinGongZhongHaoIcon from '@/assets/images/platforms/wei-xin-gong-zhong-hao.png'
import WeiXinIcon from '@/assets/images/platforms/wei-xin.png'
import WeiXinGongZhongHaoBigIcon from '@/assets/images/platforms/wei-xin-gong-zhong-hao-BIG.png'
import WeiXinShiPinHao3rdPartyIcon from '@/assets/images/platforms/wei-xin-shi-pin-hao-3rd-party.png'

export const platformNames = {
  DouYin: '抖音',
  KuaiShou: '快手',
  WeiXinShiPinHao: '视频号',
  BiliBili: '哔哩哔哩',
  XiaoHongShu: '小红书',
  BaiJiaHao: '百家号',
  TouTiaoHao: '头条号',
  XiGuaShiPin: '西瓜视频',
  ZhiHu: '知乎',
  QiEHao: '企鹅号',
  XinLangWeiBo: '新浪微博',
  SouHuHao: '搜狐号',
  YiDianHao: '一点号',
  DaYuHao: '大鱼号',
  WangYiHao: '网易号',
  AiQiYi: '爱奇艺',
  TengXunWeiShi: '腾讯微视',
  WeiXinGongZhongHao: '微信公众号',
  WeiXin: '微信',
  Unknown: '未知',
} as const

export type PlatformNameKey = keyof typeof platformNames
export type PlatformName = (typeof platformNames)[PlatformNameKey] | '未知'

interface PlatformConfig {
  name: PlatformName
  icon: string
  bigIcon: string
  displayOrder: number
  key: PlatformNameKey
}

export class Platform {
  constructor(
    public name: PlatformName,
    public icon: string,
    public bigIcon: string,
    public displayOrder: number = Number.MAX_SAFE_INTEGER,
    public key: PlatformNameKey,
  ) {}
}

const platformConfigs: Record<string, PlatformConfig> = {
  DouYin: {
    name: platformNames.DouYin,
    icon: DouYinIcon,
    bigIcon: DouYinIcon,
    displayOrder: 1,
    key: 'DouYin',
  },
  KuaiShou: {
    name: platformNames.KuaiShou,
    icon: KuaiShouIcon,
    bigIcon: KuaiShouIcon,
    displayOrder: 2,
    key: 'KuaiShou',
  },
  WeiXinShiPinHao: {
    name: platformNames.WeiXinShiPinHao,
    icon: WeiXinShiPinHaoIcon,
    bigIcon: WeiXinShiPinHaoIcon,
    displayOrder: 3,
    key: 'WeiXinShiPinHao',
  },
  XiaoHongShu: {
    name: platformNames.XiaoHongShu,
    icon: XiaoHongShuIcon,
    bigIcon: XiaoHongShuIcon,
    displayOrder: 4,
    key: 'XiaoHongShu',
  },
  Bilibili: {
    name: platformNames.BiliBili,
    icon: BilibiliIcon,
    bigIcon: BilibiliIcon,
    displayOrder: 5,
    key: 'BiliBili',
  },
  BaiJiaHao: {
    name: platformNames.BaiJiaHao,
    icon: BaiJiaHaoIcon,
    bigIcon: BaiJiaHaoIcon,
    displayOrder: 6,
    key: 'BaiJiaHao',
  },
  TouTiaoHao: {
    name: platformNames.TouTiaoHao,
    icon: TouTiaoHaoIcon,
    bigIcon: TouTiaoHaoIcon,
    displayOrder: 7,
    key: 'TouTiaoHao',
  },
  XinLangWeiBo: {
    name: platformNames.XinLangWeiBo,
    icon: XinLangWeiBoIcon,
    bigIcon: XinLangWeiBoIcon,
    displayOrder: 8,
    key: 'XinLangWeiBo',
  },
  ZhiHu: {
    name: platformNames.ZhiHu,
    icon: ZhiHuIcon,
    bigIcon: ZhiHuIcon,
    displayOrder: 9,
    key: 'ZhiHu',
  },
  QiEHao: {
    name: platformNames.QiEHao,
    icon: QiEHaoIcon,
    bigIcon: QiEHaoIcon,
    displayOrder: 10,
    key: 'QiEHao',
  },
  SouHuHao: {
    name: platformNames.SouHuHao,
    icon: SouHuHaoIcon,
    bigIcon: SouHuHaoIcon,
    displayOrder: 11,
    key: 'SouHuHao',
  },
  YiDianHao: {
    name: platformNames.YiDianHao,
    icon: YiDianHaoIcon,
    bigIcon: YiDianHaoIcon,
    displayOrder: 12,
    key: 'YiDianHao',
  },
  DaYuHao: {
    name: platformNames.DaYuHao,
    icon: DaYuHaoIcon,
    bigIcon: DaYuHaoIcon,
    displayOrder: 13,
    key: 'DaYuHao',
  },
  WangYiHao: {
    name: platformNames.WangYiHao,
    icon: WangYiHaoIcon,
    bigIcon: WangYiHaoIcon,
    displayOrder: 14,
    key: 'WangYiHao',
  },
  AiQiYi: {
    name: platformNames.AiQiYi,
    icon: AiQiYiIcon,
    bigIcon: AiQiYiIcon,
    displayOrder: 15,
    key: 'AiQiYi',
  },
  TengXunWeiShi: {
    name: platformNames.TengXunWeiShi,
    icon: TengXunWeiShiIcon,
    bigIcon: TengXunWeiShiIcon,
    displayOrder: 16,
    key: 'TengXunWeiShi',
  },
  WeiXin: {
    name: platformNames.WeiXin,
    icon: WeiXinIcon,
    bigIcon: WeiXinIcon,
    displayOrder: 9,
    key: 'WeiXin',
  },
  WeiXinGongZhongHao: {
    name: platformNames.WeiXinGongZhongHao,
    icon: WeiXinGongZhongHaoIcon,
    bigIcon: WeiXinGongZhongHaoBigIcon,
    displayOrder: 9,
    key: 'WeiXinGongZhongHao',
  },
}

export const platforms = Object.entries(platformConfigs).reduce(
  (acc, [key, config]) => ({
    ...acc,
    [key]: new Platform(config.name, config.icon, config.bigIcon, config.displayOrder, config.key),
  }),
  {} as Record<string, Platform>,
)

const UnKnownPlatform = new Platform(
  '未知',
  UnknownPlatformIcon,
  UnknownPlatformIcon,
  1000,
  'Unknown',
)

const WeiXinGongZhongHaoSub = new Platform(
  '微信公众号',
  WeiXinShiPinHao3rdPartyIcon,
  WeiXinShiPinHao3rdPartyIcon,
  1000,
  'WeiXinGongZhongHao',
)

export const allPlatform = Object.values(platforms)

export function getPlatformByName(name: string, isSubWeixin = false): Platform {
  if (isSubWeixin) return WeiXinGongZhongHaoSub
  return allPlatform.find((platform) => platform.name === name) ?? UnKnownPlatform
}
