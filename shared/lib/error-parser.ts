import { allPlatformName } from './validations/videoForm'

/**
 * 解析错误消息，从格式为【平台名称】错误信息的字符串中提取平台名称和实际错误消息
 * @param errorMessage 完整的错误消息，如"【抖音】请选择封面"
 * @returns 包含平台名称和错误信息的对象，如果解析失败则返回原始消息
 */
export interface ParsedError {
  platformName: string
  message: string
  original: string
}

export function parseErrorMessage(errorMessage: string): ParsedError {
  // 匹配【平台名称】错误信息的格式
  const regex = /【(.*?)】(.*)/
  const matches = errorMessage.match(regex)

  if (matches && matches.length === 3) {
    return {
      platformName: getPlatformNameBykey(matches[1]), // 平台名称
      message: matches[2].trim(), // 错误信息，去除可能的前后空格
      original: errorMessage, // 保留原始消息
    }
  }

  // 如果没有匹配到预期格式，返回原始消息
  return {
    platformName: '',
    message: errorMessage,
    original: errorMessage,
  }
}

// 根据平台名称key获取平台名称
const getPlatformNameBykey = (key: string) => {
  if (key === allPlatformName) {
    return '通用'
  }
  return key
}

/**
 * 按平台名称对错误消息进行分组
 * @param errorMessages 错误消息数组
 * @returns 按平台分组的错误消息对象
 */
export interface GroupedErrors {
  [platformName: string]: string[]
}

export function groupErrorsByPlatform(errorMessages: string[]): GroupedErrors {
  const result: GroupedErrors = {}

  errorMessages.forEach((errorMessage) => {
    const parsedError = parseErrorMessage(errorMessage)
    const platform = parsedError.platformName || '未分类'

    if (!result[platform]) {
      result[platform] = []
    }

    result[platform].push(parsedError.message)
  })

  return result
}

/**
 * 提取并合并多个错误中的平台名称
 * @param errorMessages 错误消息数组
 * @returns 所有涉及的平台名称数组（去重）
 */
export function extractPlatforms(errorMessages: string[]): string[] {
  const platforms = new Set<string>()

  errorMessages.forEach((errorMessage) => {
    const parsedError = parseErrorMessage(errorMessage)
    if (parsedError.platformName) {
      platforms.add(parsedError.platformName)
    }
  })

  return Array.from(platforms)
}

/**
 * 示例用法:
 *
 * const error = parseErrorMessage("【抖音】请选择封面");
 * console.log(error);
 * // 输出: { platformName: "抖音", message: "请选择封面", original: "【抖音】请选择封面" }
 *
 * const error2 = parseErrorMessage("【All】视频时长不能超过60秒");
 * console.log(error2);
 * // 输出: { platformName: "All", message: "视频时长不能超过60秒", original: "【All】视频时长不能超过60秒" }
 *
 * const errors = [
 *   "【抖音】请选择封面",
 *   "【抖音】标题不能为空",
 *   "【快手】视频时长不能超过60秒",
 *   "【All】请选择账号"
 * ];
 *
 * const grouped = groupErrorsByPlatform(errors);
 * console.log(grouped);
 * // 输出:
 * // {
 * //   "抖音": ["请选择封面", "标题不能为空"],
 * //   "快手": ["视频时长不能超过60秒"],
 * //   "All": ["请选择账号"]
 * // }
 *
 * const platforms = extractPlatforms(errors);
 * console.log(platforms);
 * // 输出: ["抖音", "快手", "All"]
 */
