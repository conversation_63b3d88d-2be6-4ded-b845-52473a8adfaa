import { loadingManager } from '@/stores'
import type { ImageBase, VideoBase } from '@/types/media'
import { Capacitor } from '@capacitor/core'
import { Filesystem } from '@capacitor/filesystem'
import { FilePicker } from '@capawesome/capacitor-file-picker'
import { toast } from 'sonner'

/**
 * 从视频中捕获指定时间点的帧
 * @param videoUrl 视频URL
 * @param second 要捕获的时间点（秒）
 * @returns 返回捕获的图片信息
 */
export const captureVideoFrame = async (
  videoUrl: string,
  second: number = 0.1,
): Promise<ImageBase | undefined> => {
  loadingManager.show()
  try {
    const result = await new Promise<ImageBase>((resolve, reject) => {
      const video = document.createElement('video')
      video.style.display = 'none'
      document.body.appendChild(video)

      video.src = videoUrl

      const cleanup = () => {
        document.body.removeChild(video)
      }

      const handleError = (error: Error) => {
        cleanup()
        reject(error)
      }

      video.addEventListener('error', () => handleError(new Error('视频加载失败')))

      video.addEventListener('loadeddata', () => {
        video.currentTime = second

        video.addEventListener('seeked', async () => {
          try {
            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')

            if (!context) {
              throw new Error('无法获取canvas上下文')
            }

            canvas.width = video.videoWidth
            canvas.height = video.videoHeight
            context.drawImage(video, 0, 0, canvas.width, canvas.height)

            const blob = await new Promise<Blob>((resolve, reject) => {
              canvas.toBlob((blob) => {
                if (blob) {
                  resolve(blob)
                } else {
                  reject(new Error('无法生成图片'))
                }
              })
            })

            cleanup()
            resolve({
              url: URL.createObjectURL(blob),
              width: video.videoWidth,
              height: video.videoHeight,
              size: blob.size,
              type: 'image/jpeg',
              arrayBuffer: await blob.arrayBuffer(),
            })
          } catch (error) {
            handleError(error as Error)
          }
        })
      })

      video.load()
    })

    return result
  } catch (error) {
    toast.error('获取封面失败', {
      description: error instanceof Error ? error.message : '未知错误',
    })
  } finally {
    loadingManager.hide()
  }
}

export const handleFileSelect = async (accept: string) => {
  try {
    // 判断是否在原生环境中运行
    // 在 Web 环境中使用原生文件选择器
    return await new Promise<File>((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = accept
      input.multiple = false
      input.webkitdirectory = false
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (file) {
          resolve(file)
        } else {
          reject(new Error('未选择文件'))
        }
      }
      input.click()
    })
  } catch (error) {
    console.error('选择文件失败:', error)
    return null
  }
}

export const convertToCapacitorPath = (path: string) => {
  return Capacitor.convertFileSrc(path)
}

export const handleVideoSelect = async (): Promise<VideoBase | null> => {
  try {
    loadingManager.show()
    const result = await FilePicker.pickVideos({
      limit: 1,
    })
    const file = result.files[0]
    const videoBase = {
      duration: file.duration ?? 0,
      width: file.width ?? 0,
      height: file.height ?? 0,
      size: file.size,
      type: file.mimeType,
    }
    if (file.blob) {
      const rawFile = new File([file.blob], file.name, {
        type: file.mimeType,
      })
      return {
        ...videoBase,
        file: rawFile,
        url: URL.createObjectURL(rawFile),
      } satisfies VideoBase
    } else {
      return {
        ...videoBase,
        path: file.path ?? '',
        url: convertToCapacitorPath(file.path ?? ''),
      } satisfies VideoBase
    }
  } catch (error) {
    if (error instanceof Error) {
      console.log(error.message)
      if (error.message.includes('canceled')) {
        return null
      }
      toast.error('选择视频失败', {
        description: error.message,
      })
    } else {
      toast.error('选择视频失败', {
        description: '未知错误',
      })
    }
    return null
  } finally {
    loadingManager.hide()
  }
}

// 选择图片
export const handleImageSelect = async (limit?: number): Promise<ImageBase[]> => {
  try {
    loadingManager.show()
    const result = await FilePicker.pickImages({
      limit: limit ?? 1,
    })
    return await Promise.all(
      result.files.map(async (file) => {
        const imageBase = {
          width: file.width ?? 0,
          height: file.height ?? 0,
          size: file.size,
          type: file.mimeType,
        }
        if (file.blob) {
          const arrayBuffer = await file.blob.arrayBuffer()
          return {
            ...imageBase,
            arrayBuffer,
            url: URL.createObjectURL(file.blob),
          } satisfies ImageBase
        } else {
          // 路径获取arrayBuffer
          const url = convertToCapacitorPath(file.path ?? '')
          return {
            ...imageBase,
            path: file.path!,
            url,
          } satisfies ImageBase
        }
      }),
    )
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('canceled')) {
        return []
      }
      toast.error('选择图片失败', {
        description: error.message,
      })
    } else {
      toast.error('选择图片失败', {
        description: '未知错误',
      })
    }
    return []
  } finally {
    loadingManager.hide()
  }
}

export function base64ToArrayBuffer(base64String: string) {
  // 提取 Base64 数据和 MIME 类型
  const bstr = atob(base64String)
  let n = bstr.length
  const u8arr = new Uint8Array(n)

  // 将 Base64 数据转换为字节数组
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }

  // 创建 File 对象
  return u8arr
}

export const pathToArrayBuffer = async (path: string) => {
  const contents = await Filesystem.readFile({
    path,
  })
  return base64ToArrayBuffer(contents.data as string)
}
