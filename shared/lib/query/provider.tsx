/**
 * React Query 配置
 *
 * 注意：使用前需要先安装依赖：
 * npm install @tanstack/react-query
 * 或
 * yarn add @tanstack/react-query
 */
import {
  QueryClient,
  QueryClientProvider as TanQueryClientProvider,
} from "@tanstack/react-query";

// 创建默认错误处理函数
const defaultErrorHandler = (error: unknown) => {
  console.error("API 请求错误:", error);

  // 使用 Framework7 显示错误提示
};

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 设置 React Query 的默认选项
      retry: 1, // 失败重试次数
      retryDelay: (attemptIndex: number) =>
        Math.min(1000 * 2 ** attemptIndex, 30000), // 重试延迟时间，指数级增长
      refetchOnWindowFocus: false, // 窗口获得焦点时不重新获取数据
    },
    mutations: {
      // 设置 mutation 的默认选项
      retry: 0, // 失败不重试
      onError: defaultErrorHandler, // 默认错误处理
    },
  },
});

export const QueryClientProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  return (
    <TanQueryClientProvider client={queryClient}>
      {children}
    </TanQueryClientProvider>
  );
};
