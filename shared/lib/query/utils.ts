import {
  useMutation,
  UseMutationOptions,
  useQuery,
  UseQueryOptions,
  QueryKey,
  QueryFunction,
} from "@tanstack/react-query";

/**
 * 工厂方法：创建一个 mutation hook
 *
 * 该工厂方法接收一个 mutationFn 参数，返回一个可复用的 hook 函数。
 * 返回的 hook 函数接收除 mutationFn 之外的所有 UseMutationOptions 参数。
 *
 * @template TData - mutation 返回的数据类型
 * @template TError - 错误类型，默认为 Error
 * @template TVariables - mutation 函数的参数类型，默认为 void
 * @template TContext - mutation 上下文类型，默认为 unknown
 *
 * @param mutationFn - mutation 函数，接收 TVariables 类型参数，返回 Promise<TData>
 * @returns 返回一个 hook 函数，该函数接收 UseMutationOptions（排除 mutationFn）
 */
export function createMutationHook<
  TData,
  TError = Error,
  TVariables = void,
  TContext = unknown,
>(mutationFn: (variables: TVariables) => Promise<TData>) {
  return function useMutationHook(
    options?: Omit<
      UseMutationOptions<TData, TError, TVariables, TContext>,
      "mutationFn"
    >
  ) {
    return useMutation<TData, TError, TVariables, TContext>({
      mutationFn,
      ...options,
    });
  };
}

/**
 * 工厂方法：创建一个 query hook
 *
 * 该工厂方法接收一个创建 queryKey 和 queryFn 的函数，返回一个可复用的 hook 函数。
 * 返回的 hook 函数接收参数并传递给 queryKey 和 queryFn 创建函数，以及其他 UseQueryOptions 参数。
 *
 * @template TQueryFnData - query 函数返回的数据类型
 * @template TError - 错误类型，默认为 Error
 * @template TData - 最终返回的数据类型，默认与 TQueryFnData 相同
 * @template TQueryKey - query key 的类型，默认为 QueryKey
 *
 * @param createQuery - 创建 queryKey 和 queryFn 的函数，接收参数并返回配置对象
 * @returns 返回一个 hook 函数，该函数接收参数和其他 UseQueryOptions
 */

// 重载：无参数版本 - 直接传递 queryKey 和 queryFn
export function createQueryHook<
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  queryKey: TQueryKey,
  queryFn: QueryFunction<TQueryFnData, TQueryKey>
): (
  options?: Omit<
    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
    "queryKey" | "queryFn"
  >
) => ReturnType<typeof useQuery<TQueryFnData, TError, TData, TQueryKey>>;

// 重载：有参数版本 - 传递创建函数
export function createQueryHook<
  TParams,
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  createQuery: (params: TParams) => {
    queryKey: TQueryKey;
    queryFn: QueryFunction<TQueryFnData, TQueryKey>
  }
): (
  params: TParams,
  options?: Omit<
    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
    "queryKey" | "queryFn"
  >
) => ReturnType<typeof useQuery<TQueryFnData, TError, TData, TQueryKey>>;

// 实现
export function createQueryHook<
  TParams = void,
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  queryKeyOrCreateQuery: TQueryKey | ((params: TParams) => {
    queryKey: TQueryKey;
    queryFn: QueryFunction<TQueryFnData, TQueryKey>
  }),
  queryFn?: QueryFunction<TQueryFnData, TQueryKey>
) {
  // 检查是否为无参数版本（直接传递 queryKey 和 queryFn）
  const isNoParams = typeof queryKeyOrCreateQuery !== 'function';

  if (isNoParams) {
    // 无参数版本：直接使用传入的 queryKey 和 queryFn
    const staticQueryKey = queryKeyOrCreateQuery as TQueryKey;
    const staticQueryFn = queryFn as QueryFunction<TQueryFnData, TQueryKey>;

    return function useQueryHook(
      options?: Omit<
        UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
        "queryKey" | "queryFn"
      >
    ) {
      return useQuery<TQueryFnData, TError, TData, TQueryKey>({
        queryKey: staticQueryKey,
        queryFn: staticQueryFn,
        ...options,
      });
    };
  } else {
    // 有参数版本：使用创建函数
    const createQuery = queryKeyOrCreateQuery as (params: TParams) => {
      queryKey: TQueryKey;
      queryFn: QueryFunction<TQueryFnData, TQueryKey>
    };

    return function useQueryHook(
      params: TParams,
      options?: Omit<
        UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
        "queryKey" | "queryFn"
      >
    ) {
      const { queryKey, queryFn } = createQuery(params);
      return useQuery<TQueryFnData, TError, TData, TQueryKey>({
        queryKey,
        queryFn,
        ...options,
      });
    };
  }
}

export { useQueryClient } from "@tanstack/react-query";
