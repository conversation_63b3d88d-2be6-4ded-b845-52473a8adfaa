/**
 * 媒体文件处理工具
 */

import type { VideoBase, ImageBase } from './types'

export type { VideoBase, ImageBase }

/**
 * Base64转ArrayBuffer
 * @param base64String Base64字符串
 */
export function base64ToArrayBuffer(base64String: string): Uint8Array {
  const bstr = atob(base64String)
  let n = bstr.length
  const u8arr = new Uint8Array(n)

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }

  return u8arr
}

/**
 * ArrayBuffer转Base64
 * @param buffer ArrayBuffer
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

/**
 * 创建文件选择器
 * @param accept 接受的文件类型
 * @param multiple 是否允许多选
 */
export function createFileSelector(accept: string, multiple: boolean = false): Promise<FileList | null> {
  return new Promise((resolve) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = accept
    input.multiple = multiple
    input.webkitdirectory = false
    
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files
      resolve(files)
    }
    
    input.oncancel = () => {
      resolve(null)
    }
    
    input.click()
  })
}

/**
 * 选择单个文件
 * @param accept 接受的文件类型
 */
export async function selectSingleFile(accept: string): Promise<File | null> {
  const files = await createFileSelector(accept, false)
  return files && files.length > 0 ? files[0] : null
}

/**
 * 选择多个文件
 * @param accept 接受的文件类型
 */
export async function selectMultipleFiles(accept: string): Promise<File[]> {
  const files = await createFileSelector(accept, true)
  return files ? Array.from(files) : []
}

/**
 * 读取文件为DataURL
 * @param file 文件对象
 */
export function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(reader.error)
    reader.readAsDataURL(file)
  })
}

/**
 * 读取文件为ArrayBuffer
 * @param file 文件对象
 */
export function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as ArrayBuffer)
    reader.onerror = () => reject(reader.error)
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 读取文件为文本
 * @param file 文件对象
 * @param encoding 编码格式，默认为UTF-8
 */
export function readFileAsText(file: File, encoding: string = 'UTF-8'): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(reader.error)
    reader.readAsText(file, encoding)
  })
}

/**
 * 获取图片尺寸
 * @param file 图片文件或URL
 */
export function getImageDimensions(file: File | string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    if (typeof file === 'string') {
      img.src = file
    } else {
      img.src = URL.createObjectURL(file)
    }
  })
}

/**
 * 获取视频时长和尺寸
 * @param file 视频文件或URL
 */
export function getVideoMetadata(file: File | string): Promise<{
  duration: number
  width: number
  height: number
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    
    video.onloadedmetadata = () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight
      })
    }
    
    video.onerror = () => {
      reject(new Error('Failed to load video'))
    }
    
    if (typeof file === 'string') {
      video.src = file
    } else {
      video.src = URL.createObjectURL(file)
    }
  })
}

/**
 * 压缩图片
 * @param file 图片文件
 * @param quality 压缩质量 (0-1)
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 */
export function compressImage(
  file: File,
  quality: number = 0.8,
  maxWidth?: number,
  maxHeight?: number
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      let { width, height } = img
      
      // 计算新尺寸
      if (maxWidth && width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (maxHeight && height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }
      
      canvas.width = width
      canvas.height = height
      
      ctx?.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        file.type,
        quality
      )
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    img.src = URL.createObjectURL(file)
  })
}
