/**
 * 字节大小处理工具
 */

export class ByteSize {
  private static readonly threshold = 1024
  private static readonly unit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  constructor(public bytes: number) {}

  toString(precision = 2) {
    let size = this.bytes
    let i = 0
    while (size >= ByteSize.threshold) {
      size /= ByteSize.threshold
      i++
    }
    return `${formatNumber(size, precision)}${ByteSize.unit[i]}`
  }

  valueOf() {
    return this.bytes
  }

  static fromB(capacity: number): ByteSize {
    return new ByteSize(capacity)
  }

  static fromKB(capacity: number): ByteSize {
    return new ByteSize(capacity * ByteSize.threshold)
  }

  static fromMB(capacity: number): ByteSize {
    return new ByteSize(capacity * ByteSize.threshold * ByteSize.threshold)
  }

  static fromGB(capacity: number): ByteSize {
    return new ByteSize(capacity * ByteSize.threshold * ByteSize.threshold * ByteSize.threshold)
  }
}

const formatNumber = (size: number, precision = 2) => {
  // Convert to number and check if it's an integer
  const num = Number(size)
  return Number.isInteger(num) ? num.toString() : num.toFixed(precision)
}
