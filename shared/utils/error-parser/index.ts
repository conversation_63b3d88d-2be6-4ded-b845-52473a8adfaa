/**
 * 错误消息解析器
 * 用于解析带有平台标识的错误消息
 */

import type { ParsedError, GroupedErrors } from './types'

export type { ParsedError, GroupedErrors }

/**
 * 解析错误消息，提取平台名称和错误内容
 * 
 * @param errorMessage - 错误消息字符串，格式如 "【平台名】错误内容"
 * @returns 解析后的错误对象，包含平台名称、错误消息和原始消息
 * 
 * @example
 * parseErrorMessage("【抖音】请选择封面")
 * // 返回: { platformName: "抖音", message: "请选择封面", original: "【抖音】请选择封面" }
 */
export function parseErrorMessage(errorMessage: string): ParsedError {
  // 匹配【平台名】格式的正则表达式
  const platformRegex = /^【(.+?)】(.+)$/
  const match = errorMessage.match(platformRegex)
  
  if (match) {
    const [, platformName, message] = match
    return {
      platformName: platformName.trim(),
      message: message.trim(),
      original: errorMessage
    }
  }
  
  // 如果不匹配格式，返回默认值
  return {
    platformName: 'Unknown',
    message: errorMessage,
    original: errorMessage
  }
}

/**
 * 将错误消息数组按平台分组
 * 
 * @param errorMessages - 错误消息数组
 * @returns 按平台分组的错误消息对象
 * 
 * @example
 * groupErrorsByPlatform([
 *   "【抖音】请选择封面",
 *   "【抖音】标题不能为空",
 *   "【快手】视频时长不能超过60秒"
 * ])
 * // 返回: {
 * //   "抖音": ["请选择封面", "标题不能为空"],
 * //   "快手": ["视频时长不能超过60秒"]
 * // }
 */
export function groupErrorsByPlatform(errorMessages: string[]): GroupedErrors {
  const grouped: GroupedErrors = {}
  
  errorMessages.forEach(errorMessage => {
    const parsed = parseErrorMessage(errorMessage)
    
    if (!grouped[parsed.platformName]) {
      grouped[parsed.platformName] = []
    }
    
    grouped[parsed.platformName].push(parsed.message)
  })
  
  return grouped
}

/**
 * 从错误消息数组中提取所有平台名称
 * 
 * @param errorMessages - 错误消息数组
 * @returns 平台名称数组（去重）
 * 
 * @example
 * extractPlatforms([
 *   "【抖音】请选择封面",
 *   "【快手】视频时长不能超过60秒",
 *   "【抖音】标题不能为空"
 * ])
 * // 返回: ["抖音", "快手"]
 */
export function extractPlatforms(errorMessages: string[]): string[] {
  const platforms = new Set<string>()
  
  errorMessages.forEach(errorMessage => {
    const parsed = parseErrorMessage(errorMessage)
    platforms.add(parsed.platformName)
  })
  
  return Array.from(platforms)
}

/**
 * 格式化错误消息为易读的字符串
 * 
 * @param errorMessages - 错误消息数组
 * @param separator - 分隔符，默认为换行符
 * @returns 格式化后的错误字符串
 * 
 * @example
 * formatErrors([
 *   "【抖音】请选择封面",
 *   "【快手】视频时长不能超过60秒"
 * ])
 * // 返回: "抖音: 请选择封面\n快手: 视频时长不能超过60秒"
 */
export function formatErrors(errorMessages: string[], separator: string = '\n'): string {
  return errorMessages
    .map(errorMessage => {
      const parsed = parseErrorMessage(errorMessage)
      return `${parsed.platformName}: ${parsed.message}`
    })
    .join(separator)
}
