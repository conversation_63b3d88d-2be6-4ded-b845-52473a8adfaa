/**
 * 格式化工具函数
 */

/**
 * 格式化数字，添加千分位分隔符
 * @param num 数字
 * @param separator 分隔符，默认为逗号
 */
export function formatNumber(num: number, separator: string = ','): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator)
}

/**
 * 格式化货币
 * @param amount 金额
 * @param currency 货币符号，默认为¥
 * @param decimals 小数位数，默认为2
 */
export function formatCurrency(
  amount: number,
  currency: string = '¥',
  decimals: number = 2,
): string {
  const formatted = amount.toFixed(decimals)
  const withSeparator = formatNumber(parseFloat(formatted))
  return `${currency}${withSeparator}`
}

/**
 * 格式化百分比
 * @param value 数值（0-1之间）
 * @param decimals 小数位数，默认为1
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${(value * 100).toFixed(decimals)}%`
}

/**
 * 格式化文件大小（使用二进制单位）
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`
}

/**
 * 格式化文件大小（使用十进制单位）
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 */
export function formatFileSizeDecimal(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B'

  const k = 1000
  const sizes = ['B', 'kB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`
}

/**
 * 格式化手机号码
 * @param phone 手机号码
 * @param separator 分隔符，默认为空格
 */
export function formatPhone(phone: string, separator: string = ' '): string {
  const cleaned = phone.replace(/\D/g, '')

  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, `$1${separator}$2${separator}$3`)
  }

  return phone
}

/**
 * 格式化身份证号码
 * @param idCard 身份证号码
 * @param maskMiddle 是否遮蔽中间部分
 */
export function formatIdCard(idCard: string, maskMiddle: boolean = false): string {
  const cleaned = idCard.replace(/\s/g, '')

  if (cleaned.length === 18) {
    if (maskMiddle) {
      return cleaned.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    }
    return cleaned.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
  }

  if (cleaned.length === 15) {
    if (maskMiddle) {
      return cleaned.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
    }
    return cleaned.replace(/(\d{6})(\d{6})(\d{3})/, '$1 $2 $3')
  }

  return idCard
}

/**
 * 格式化银行卡号
 * @param cardNumber 银行卡号
 * @param separator 分隔符，默认为空格
 * @param groupSize 分组大小，默认为4
 */
export function formatBankCard(
  cardNumber: string,
  separator: string = ' ',
  groupSize: number = 4,
): string {
  const cleaned = cardNumber.replace(/\D/g, '')
  const regex = new RegExp(`(\\d{${groupSize}})`, 'g')
  return cleaned.replace(regex, `$1${separator}`).trim()
}

/**
 * 截断文本并添加省略号
 * @param text 文本
 * @param maxLength 最大长度
 * @param ellipsis 省略号，默认为...
 */
export function truncateText(text: string, maxLength: number, ellipsis: string = '...'): string {
  if (text.length <= maxLength) {
    return text
  }

  return text.slice(0, maxLength - ellipsis.length) + ellipsis
}

/**
 * 首字母大写
 * @param str 字符串
 */
export function capitalize(str: string): string {
  if (!str) return str
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰命名转换为短横线命名
 * @param str 驼峰命名字符串
 */
export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
}

/**
 * 短横线命名转换为驼峰命名
 * @param str 短横线命名字符串
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 蛇形命名转换为驼峰命名
 * @param str 蛇形命名字符串
 */
export function snakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 驼峰命名转换为蛇形命名
 * @param str 驼峰命名字符串
 */
export function camelToSnake(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1_$2').toLowerCase()
}

// 月份格式化 12个月为1年
export function formatMount(mount: number) {
  const year = Math.floor(mount / 12)
  const month = mount % 12
  return year ? `${year}年` + (month ? `${month}个月` : '') : `${month}个月`
}

// 保留两位小数，不满两位补零
export function formatPrice(price: number) {
  const absPrice = Math.abs(price)
  const sign = price < 0 ? '-' : ''
  return sign + absPrice.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}
