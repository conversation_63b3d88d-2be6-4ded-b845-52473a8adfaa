/**
 * @yixiaoer/utils - 通用工具函数库
 *
 * 提供各种实用的工具类和函数，包括：
 * - 字节大小格式化
 * - 时间跨度处理
 * - 像素尺寸比较
 * - 日期时间工具
 * - 错误解析器
 * - 媒体文件处理
 * - 类型工具和守卫
 * - 通用工具函数
 * - 格式化工具
 */

// 字节大小处理
export { ByteSize } from './byte-size'

// 时间跨度处理
export { TimeSpan } from './time-span'

// 像素尺寸处理
export { PixelSize } from './pixel-size'

// 日期时间工具
export { DateUtils, type TimeUnit, type TimeConversion, type DateRange } from './date-utils'

// 错误解析器
export {
  parseErrorMessage,
  groupErrorsByPlatform,
  extractPlatforms,
  formatErrors,
  type ParsedError,
  type GroupedErrors,
} from './error-parser'

// 类型工具
export {
  isStatus,
  toStatus,
  isNullish,
  isNotNullish,
  isEmpty,
  isNotEmpty,
  isNumber,
  isString,
  isBoolean,
  isObject,
  isArray,
  safeJsonParse,
  safeJsonStringify,
  type Status,
  type SafeArea,
} from './type-utils'

// 通用工具函数
export {
  delay,
  wait,
  getSafeAreaInsetsFromCSS,
  debounce,
  randomString,
  uuid,
  deepClone,
  clamp,
  mapRange,
} from './common-utils'

// 格式化工具
export * from './format-utils'

// 媒体工具
export {
  base64ToArrayBuffer,
  arrayBufferToBase64,
  createFileSelector,
  selectSingleFile,
  selectMultipleFiles,
  readFileAsDataURL,
  readFileAsArrayBuffer,
  readFileAsText,
  getImageDimensions,
  getVideoMetadata,
  compressImage,
  type VideoBase,
  type ImageBase,
} from './media-utils'

export * from './identifier'
