/**
 * 时间跨度处理工具
 */

export class TimeSpan {
  private constructor(public milliseconds: number) {}

  static fromMilliseconds(milliseconds: number) {
    return new TimeSpan(milliseconds)
  }

  static fromSeconds(seconds: number) {
    return new TimeSpan(seconds * 1000)
  }

  static fromMinutes(minutes: number) {
    return new TimeSpan(minutes * 60 * 1000)
  }

  static fromHours(hours: number) {
    return new TimeSpan(hours * 60 * 60 * 1000)
  }

  static fromDays(days: number) {
    return new TimeSpan(days * 24 * 60 * 60 * 1000)
  }

  toString() {
    if (this.milliseconds === 0) {
      return '0毫秒'
    }

    let remainingMs = this.milliseconds
    const parts: string[] = []

    // 提取天数
    const days = Math.floor(remainingMs / (24 * 60 * 60 * 1000))
    if (days > 0) {
      parts.push(`${days}天`)
      remainingMs -= days * 24 * 60 * 60 * 1000
    }

    // 提取小时
    const hours = Math.floor(remainingMs / (60 * 60 * 1000))
    if (hours > 0) {
      parts.push(`${hours}小时`)
      remainingMs -= hours * 60 * 60 * 1000
    }

    // 提取分钟
    const minutes = Math.floor(remainingMs / (60 * 1000))
    if (minutes > 0) {
      parts.push(`${minutes}分钟`)
      remainingMs -= minutes * 60 * 1000
    }

    // 提取秒
    const seconds = Math.floor(remainingMs / 1000)
    if (seconds > 0) {
      parts.push(`${seconds}秒`)
      remainingMs -= seconds * 1000
    }

    // 剩余毫秒
    if (remainingMs > 0) {
      parts.push(`${remainingMs}毫秒`)
    }

    return parts.join('')
  }

  valueOf(): number {
    return this.milliseconds
  }

  add(other: TimeSpan): TimeSpan {
    return new TimeSpan(this.milliseconds + other.milliseconds)
  }

  subtract(other: TimeSpan): TimeSpan {
    return new TimeSpan(this.milliseconds - other.milliseconds)
  }

  multiply(factor: number): TimeSpan {
    return new TimeSpan(this.milliseconds * factor)
  }

  divide(divisor: number): TimeSpan {
    return new TimeSpan(this.milliseconds / divisor)
  }

  get seconds() {
    return this.milliseconds / 1000
  }

  get minutes() {
    return this.seconds / 60
  }

  get hours() {
    return this.minutes / 60
  }

  get days() {
    return this.hours / 24
  }
}
