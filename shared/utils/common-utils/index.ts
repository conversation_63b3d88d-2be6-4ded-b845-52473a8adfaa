/**
 * 通用工具函数
 */

import type { SafeArea } from "../type-utils/types";

/**
 * 延迟执行函数
 * @param ms 延迟时间（毫秒）
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 等待函数（delay的别名）
 * @param time 等待时间（毫秒）
 */
export function wait(time = 1000): Promise<void> {
  return delay(time);
}

/**
 * 从CSS变量获取安全区域插入值
 */
export function getSafeAreaInsetsFromCSS(): SafeArea {
  const resolveCssVarPx = (cssVar: string): number => {
    const el = document.createElement("div");
    el.style.position = "absolute";
    el.style.visibility = "hidden";
    el.style.height = `var(${cssVar})`;
    document.body.appendChild(el);
    const px = parseFloat(getComputedStyle(el).height);
    document.body.removeChild(el);
    return isNaN(px) ? 0 : px;
  };

  const top = resolveCssVarPx("--safe-area-inset-top");
  const bottom = resolveCssVarPx("--safe-area-inset-bottom");
  const left = resolveCssVarPx("--safe-area-inset-left");
  const right = resolveCssVarPx("--safe-area-inset-right");

  return { top, bottom, left, right };
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param immediate 是否立即执行
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };

    const callNow = immediate && !timeout;

    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func(...args);
  };
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @param chars 可用字符集
 */
export function randomString(
  length: number = 8,
  chars: string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
): string {
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成UUID v4
 */
export function uuid(): string {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as T;
  }

  if (typeof obj === "object") {
    const cloned = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }

  return obj;
}

/**
 * 限制数值在指定范围内
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * 将数值映射到新的范围
 * @param value 原始值
 * @param inMin 原始范围最小值
 * @param inMax 原始范围最大值
 * @param outMin 目标范围最小值
 * @param outMax 目标范围最大值
 */
export function mapRange(
  value: number,
  inMin: number,
  inMax: number,
  outMin: number,
  outMax: number
): number {
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
}
