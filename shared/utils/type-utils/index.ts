/**
 * 类型工具和守卫函数
 */

import type { Status, SafeArea } from './types'

export type { Status, SafeArea }

/**
 * 类型守卫函数
 */
export const isStatus = (value: string): value is Status => {
  return ['pending', 'delivering', 'successful', 'failed', 'auditing', 'rejected', 'approved'].includes(value)
}

/**
 * 类型转换工具
 */
export const toStatus = (value: string): Status => {
  return isStatus(value) ? value : 'pending'
}

/**
 * 检查值是否为null或undefined
 */
export const isNullish = (value: unknown): value is null | undefined => {
  return value === null || value === undefined
}

/**
 * 检查值是否不为null或undefined
 */
export const isNotNullish = <T>(value: T | null | undefined): value is T => {
  return value !== null && value !== undefined
}

/**
 * 检查值是否为空字符串、null或undefined
 */
export const isEmpty = (value: unknown): value is null | undefined | '' => {
  return value === null || value === undefined || value === ''
}

/**
 * 检查值是否不为空
 */
export const isNotEmpty = <T>(value: T | null | undefined | ''): value is T => {
  return value !== null && value !== undefined && value !== ''
}

/**
 * 检查是否为数字
 */
export const isNumber = (value: unknown): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value)
}

/**
 * 检查是否为字符串
 */
export const isString = (value: unknown): value is string => {
  return typeof value === 'string'
}

/**
 * 检查是否为布尔值
 */
export const isBoolean = (value: unknown): value is boolean => {
  return typeof value === 'boolean'
}

/**
 * 检查是否为对象（非null）
 */
export const isObject = (value: unknown): value is Record<string, unknown> => {
  return typeof value === 'object' && value !== null && !Array.isArray(value)
}

/**
 * 检查是否为数组
 */
export const isArray = (value: unknown): value is unknown[] => {
  return Array.isArray(value)
}

/**
 * 安全的JSON解析
 */
export const safeJsonParse = <T = unknown>(json: string, fallback?: T): T | undefined => {
  try {
    return JSON.parse(json)
  } catch {
    return fallback
  }
}

/**
 * 安全的JSON字符串化
 */
export const safeJsonStringify = (value: unknown, fallback: string = '{}'): string => {
  try {
    return JSON.stringify(value)
  } catch {
    return fallback
  }
}
