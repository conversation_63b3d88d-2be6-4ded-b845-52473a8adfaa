import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

type UpdateStatus = 'latest' | 'available' | 'downloading' | 'pending' | 'exception' | 'checking'

type UpdateState = {
  status: UpdateStatus
  progress: number
  newVersion: string
  exceptionMsg: string
  onlineDirPath: string
  scriptRetileInfo: {
    isReptileUpdate: boolean
    localReptile: string
    reptileUrl: string
    reptile: string
  }
  scriptRpaInfo: {
    rpa: string
    rpaUrl: string
    localRpa: string
    isRpaUpdate: boolean
  }
  scriptProgress: number
  setOnlineDirPath: (onlineDirPath: string) => void
  setScriptRpaInfo: (scriptInfo: {
    rpa: string
    rpaUrl: string
    localRpa: string
    isRpaUpdate: boolean
  }) => void
  setScriptRetileInfo: (scriptInfo: {
    isReptileUpdate: boolean
    localReptile: string
    reptileUrl: string
    reptile: string
  }) => void
  setScriptProgress: (scriptProgress: number) => void
  setStatus: (status: UpdateStatus) => void
  setProgress: (progress: number) => void
  setNewVersion: (newVersion: string) => void
  setExceptionMsg: (exceptionMsg: string) => void
}

export const useUpdateStore = create<UpdateState>()(
  devtools(
    (set) => ({
      status: 'latest',
      progress: 0,
      newVersion: '',
      exceptionMsg: '',
      scriptRpaInfo: {
        rpa: '',
        rpaUrl: '',
        localRpa: '',
        isRpaUpdate: false,
      },
      scriptRetileInfo: {
        isReptileUpdate: false,
        localReptile: '',
        reptileUrl: '',
        reptile: '',
      },
      onlineDirPath: '',
      scriptProgress: 0,
      setOnlineDirPath: (onlineDirPath) => set({ onlineDirPath }),
      setScriptRpaInfo: (scriptInfo) => set({ scriptRpaInfo: scriptInfo }),
      setScriptRetileInfo: (scriptInfo) => set({ scriptRetileInfo: scriptInfo }),
      setScriptProgress: (scriptProgress) => set({ scriptProgress }),
      setStatus: (status) => set({ status }),
      setProgress: (progress) => set({ progress }),
      setNewVersion: (newVersion) => set({ newVersion }),
      setExceptionMsg: (exceptionMsg) => set({ exceptionMsg }),
    }),
    {
      name: 'UpdateStore',
    },
  ),
)
