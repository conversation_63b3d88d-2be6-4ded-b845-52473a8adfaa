import { create } from 'zustand'
import type { User, Team } from '@/types'

interface AppState {
  team: Team
  user: User
  navIndex: number
  onlineCount: number
  platformAccountNum: number

  // 操作方法
  setTeam: (team: Team) => void
  setUser: (user: User) => void
  setOnlineCount: (onlineCount: number) => void
  setNavIndex: (index: number) => void
  setPlatformAccountNum: (num: number) => void
}

export const useAppStore = create<AppState>((set) => ({
  team: {} as Team,
  user: {} as User,
  navIndex: 2,
  onlineCount: 0,
  platformAccountNum: 0,

  setTeam: (team) => set({ team }),
  setUser: (user) => set({ user }),
  setOnlineCount: (onlineCount) => set({ onlineCount }),
  setNavIndex: (navIndex) => {
    set({ navIndex })
  },
  setPlatformAccountNum: (platformAccountNum) => set({ platformAccountNum }),
}))
