import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface SafeArea {
  top: number
  bottom: number
  left: number
  right: number
}

type SystemState = {
  safeArea: SafeArea
  setSafeArea: (safeArea: SafeArea) => void
  // 是否同意隐私政策
  isAgreePrivacyPolicy: boolean
  setIsAgreePrivacyPolicy: (isAgreePrivacyPolicy: boolean) => void
  // 全局loading
  isLoading: boolean
  // loading text
  loadingOption: {
    text: string
  }
  setIsLoading: (isLoading: boolean) => void
}

export type { SystemState, SafeArea }

export const useSystemStore = create<SystemState>()(
  persist(
    (set) => ({
      safeArea: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
      },
      setSafeArea: (safeArea) => set({ safeArea }),
      isAgreePrivacyPolicy: false,
      setIsAgreePrivacyPolicy: (isAgreePrivacyPolicy) => set({ isAgreePrivacyPolicy }),
      isLoading: false,
      loadingOption: {
        text: '加载中',
      },
      setIsLoading: (isLoading) => set({ isLoading }),
    }),
    {
      name: 'yixiaoer-system',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        isAgreePrivacyPolicy: state.isAgreePrivacyPolicy,
      }),
    },
  ),
)

export const loadingManager = {
  show: (text?: string) => {
    useSystemStore.getState().loadingOption.text = text || '加载中'
    useSystemStore.getState().setIsLoading(true)
  },
  hide: () => useSystemStore.getState().setIsLoading(false),
}
