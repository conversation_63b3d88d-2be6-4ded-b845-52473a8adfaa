import { useQuery } from '@tanstack/react-query';
import { useAppStore } from '@/stores';
import { useShallow } from 'zustand/react/shallow';
import { getOnlineCount, getTeamDetail, getUserInfo } from '@/lib/http';

export const useInitData = (enabled: boolean = true) => {
  const { setUser, setTeam, setOnlineCount } = useAppStore(
    useShallow((state) => ({
      setUser: state.setUser,
      setTeam: state.setTeam,
      setOnlineCount: state.setOnlineCount,
    }))
  );

  const query = useQuery({
    queryKey: ['initData'],
    queryFn: async () => {
      // 先获取用户信息
      const userData = await getUserInfo();

      // 并行请求团队信息、账号列表和在线节点数
      const [teamData, onlineCountData] = await Promise.all([
        userData.latestTeamId ? getTeamDetail(userData.latestTeamId) : null,
        getOnlineCount(),
      ]);

      // 在 queryFn 中直接更新 onlineCount 状态
      setOnlineCount(onlineCountData);
      if (teamData) {
        setTeam(teamData);
      }
      setUser(userData);

      return {
        user: userData,
        team: teamData,
        onlineCount: onlineCountData,
      };
    },
    enabled,
  });

  return query;
};
