import { createQueryHook } from '@/lib/query'
import { getLatestVersion } from '@/lib/http'
import { isWin } from '@common/protocol'
import { useMemo } from 'react'
import { version } from '@/constants/common'

export const useLatestVersionQuery = createQueryHook(
  (params: Parameters<typeof getLatestVersion>[0]) => ({
    queryKey: ['getLatestVersion', params],
    queryFn: () => getLatestVersion(params),
  }),
)

export const useVersionQuery = (enable?: boolean) => {
  const query = useLatestVersionQuery(
    {
      desktopType: isWin ? 'windows' : 'macos',
      version: version,
    },
    {
      staleTime: 0,
      enabled: enable,
    },
  )
  const isForceUpdate = useMemo(() => {
    const isForce = query.data?.isForce ?? false
    return isForce
  }, [query])
  return [query, isForceUpdate] as const
}
