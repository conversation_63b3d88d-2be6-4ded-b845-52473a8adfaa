import { getLatestVersion } from '@/lib/http'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'
import { Browser } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import { useMutation } from '@tanstack/react-query'

export const useUpdate = (autoUpdate = false) => {
  const updateMutation = useMutation({
    mutationFn: () =>
      getLatestVersion({
        desktopType: Capacitor.getPlatform(),
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      }),
    onSuccess: (response) => {
      if (response.isUpdate) {
        alertBaseManager.open({
          title: '发现新版本 v' + response.version,
          description: response.notice || '发现新版本，是否立即更新？',
          okText: '立即更新',
          cancelText: '稍后更新',
          buttons: response.isForce ? [] : undefined,
          onSubmit: async () => {
            await Browser.open({ url: response.url })
          },
          onEscapeKeyDown: response.isForce ? (e) => {
            e.preventDefault()
          } : undefined,
        })
      } else {
        if (!autoUpdate) {
          alertBaseManager.open({
            title: '当前已是最新版本',
          })
        }
      }
    },
    onError: () => {
      if (!autoUpdate) {
        alertBaseManager.open({
          title: '检查更新失败',
          description: '请稍后重试',
        })
      }
    },
  })
  return updateMutation
}
