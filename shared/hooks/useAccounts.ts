import { useState, useMemo } from 'react'
import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { getAccountList } from '@/lib/http'
import { getPlatformByName } from '@/lib/platform'
import type { PaginatedResult, PlatformAccount } from '@/types'

interface UseAccountsOptions {
  initialFilters?: {
    platform?: string
    group?: string
  }
}

interface UseAccountsResult {
  // 账号列表相关
  accounts: PlatformAccount[]
  isLoading: boolean
  isError: boolean
  error: Error | undefined

  // 筛选相关
  filters: {
    platform?: string
    group?: string
  }
  setFilters: (filters: { platform?: string; group?: string }) => void

  // 平台信息
  platforms: ReturnType<typeof getPlatformByName>[]
  allAccounts: PlatformAccount[]
  query: UseQueryResult<PaginatedResult<PlatformAccount>, Error>
}

export const useAccounts = (
  { initialFilters = {} }: UseAccountsOptions = {},
  filter?: (item: PlatformAccount) => boolean,
): UseAccountsResult => {
  // 筛选条件状态
  const [filters, setFilters] = useState(initialFilters)

  // 获取全量账号列表（用于提取平台信息）
  const query = useQuery({
    queryKey: ['accounts', 'all'],
    queryFn: () => getAccountList(),
  })

  const {
    data: allFetchAccountsData,
    isLoading: isLoadingAllAccounts,
    isError: isErrorAllAccounts,
    error: errorAllAccounts,
  } = query

  // 获取筛选后的账号列表
  const {
    data: filteredAccountsData,
    isLoading: isLoadingFilteredAccounts,
    isError: isErrorFilteredAccounts,
    error: errorFilteredAccounts,
  } = useQuery({
    queryKey: ['accounts', 'filtered', filters],
    queryFn: () => getAccountList(filters),
    enabled: !!filters.platform || !!filters.group, // 只有在有筛选条件时才请求
  })

  const allAccountsData = useMemo(() => {
    return allFetchAccountsData?.data.filter(filter ?? (() => true)) ?? []
  }, [allFetchAccountsData, filter])

  // 从全量数据中提取唯一的平台信息
  const platforms = useMemo(() => {
    if (!allAccountsData.length) return []
    const platformNames = [
      ...new Set(allAccountsData.map((acc: PlatformAccount) => acc.platformName)),
    ]
    return platformNames
      .map((name) => getPlatformByName(name))
      .sort((a, b) => a.displayOrder - b.displayOrder)
  }, [allAccountsData])

  // 获取当前显示的账号列表
  const accounts = useMemo(() => {
    if (filters.platform || filters.group) {
      return filteredAccountsData?.data.filter(filter ?? (() => true)) ?? []
    }
    return allAccountsData
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, filteredAccountsData, allAccountsData])

  return {
    accounts,
    isLoading: isLoadingAllAccounts || isLoadingFilteredAccounts,
    isError: isErrorAllAccounts || isErrorFilteredAccounts,
    error: (errorAllAccounts || errorFilteredAccounts) as Error | undefined,
    filters,
    setFilters,
    platforms,
    allAccounts: allAccountsData,
    query,
  }
}
