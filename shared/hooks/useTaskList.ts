import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { getTaskList } from '@/lib/http';

const TASK_PAGE_SIZE = 10;

export const useTaskList = () => {
  const queryClient = useQueryClient();
  const query = useInfiniteQuery({
    queryKey: ['taskList'],
    queryFn: ({ pageParam = 1 }) => 
      getTaskList(pageParam, TASK_PAGE_SIZE),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const currentPage = lastPage.page || 1;
      if (lastPage.data && lastPage.data.length === TASK_PAGE_SIZE) {
        return currentPage + 1;
      }
      return undefined;
    },
  });

  const tasks = query.data?.pages.flatMap(page => page.data) || [];

  // 专门用于刷新第一页的方法
  const refreshFirstPage = async () => {
    // 获取第一页数据
    const firstPage = await getTaskList(1, TASK_PAGE_SIZE);
    // 更新缓存，只保留第一页数据
    queryClient.setQueryData(['taskList'], {
      pages: [firstPage],
      pageParams: [1],
    });
  };

  return {
    ...query,
    tasks,
    refreshFirstPage,
  };
}; 