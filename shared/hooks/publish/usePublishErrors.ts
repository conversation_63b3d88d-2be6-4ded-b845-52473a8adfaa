import { isArray } from 'lodash'
import { FieldErrors } from 'react-hook-form'
import { useDeepCompareMemo } from 'use-deep-compare'

export const usePublishErrors = (errors: FieldErrors<FormData>) => {
  const errList = useDeepCompareMemo(() => {
    return Object.entries(errors).reduce((acc, [, value]) => {
      if (value?.types && 'custom' in value.types) {
        const custom = value.types.custom
        acc.push(...(isArray(custom) ? custom : [custom as string]))
      }
      return acc
    }, [] as string[])
  }, [JSON.stringify(errors)])

  return errList
}
