import { useEffect, useMemo, useState } from 'react'
import type { FieldValues } from 'react-hook-form'
import { useForm } from 'react-hook-form'
import type { CreateTaskRequest, PlatformAccount } from '@/types'
import type { ImageBase, VideoBase } from '@/types/media'
import type { CascadingPlatformDataItem, PlatformDataItem } from '@/types/platform-data-item'
import { createFormDataCompatibleSchema } from '@/lib/validations/formTypeSchema'
import { buildVideoValidationSchema } from '@/lib/validations/videoForm'
import { uploadFile } from '@/lib/http'
import { isArray } from 'lodash'
import { htmlService } from '@/services/html-service'
import { useWechatAccountLock } from './useWechatAccountLock'
import { usePublishMutation } from './usePublishMutation'

export interface VideoFormData extends FieldValues {
  aPlatform: {
    title: string
    description: string
    topics: string[]
  }
  bPlatform: {
    title: string
    description: string
    tags: string[]
  }
  videoAccounts:
    | {
        cover?: ImageBase
        video?: VideoBase
        platforms: PlatformAccount[]
      }
    | {
        cover?: ImageBase
        video?: VideoBase
        platforms?: PlatformAccount
        id?: string
      }[]
  isOriginal: boolean
  timing: number
  categories?: {
    [key: string]: CascadingPlatformDataItem[] | undefined
  }
  location?: {
    [key: string]: PlatformDataItem | undefined
  }
}

export function useVideoPublish() {
  // 选中的平台状态
  const [selectedAccounts, setSelectedAccounts] = useState<PlatformAccount[]>([])
  const selectedPlatformsNames = useMemo(() => {
    return [...new Set(selectedAccounts.map((account) => account.platformName))]
  }, [selectedAccounts])

  // 根据选择的平台动态构建验证Schema
  const validationSchema = useMemo(
    () => buildVideoValidationSchema(selectedPlatformsNames),
    [selectedPlatformsNames],
  )

  // 使用兼容适配器
  const { zodResolver: compatibleResolver } = createFormDataCompatibleSchema(validationSchema)

  // 使用微信账号锁定 hook
  const { isUnlockedRef } = useWechatAccountLock(selectedAccounts, setSelectedAccounts)

  // 使用react-hook-form + zod
  const formMethods = useForm<VideoFormData>({
    resolver: compatibleResolver,
    mode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      videoAccounts: {
        cover: undefined,
        video: undefined,
        platforms: [],
      },
      isOriginal: false,
      timing: 0,
      categories: {},
      location: {},
      aPlatform: {
        title: '',
        description: '',
        topics: [],
      },
      bPlatform: {
        title: '',
        description: '',
        tags: [],
      },
    },
  })

  const {
    trigger,
    getValues,
    formState: { isValid },
  } = formMethods

  useEffect(() => {
    trigger()
  }, [validationSchema, trigger])

  const { publishMutation, publishDialogJsx, setPublishDescription, setPublishProgress, setOpen } =
    usePublishMutation(isUnlockedRef)

  const handlePublish = async (type: 'local' | 'cloud') => {
    console.log('发布类型:', type, getValues())
    trigger()
    if (isValid) {
      try {
        setOpen(true)
        setPublishDescription('上传视频中...')
        const { videoAccounts, aPlatform, bPlatform, categories, timing } = getValues()
        // 整理数据
        const req = {
          publishType: 'video',
          publishChannel: type,
          isTimed: timing,
          desc: htmlService.getTextFromHtml(aPlatform.description) || bPlatform.description,
          isDraft: false,
          isAppContent: true,
        } as CreateTaskRequest
        let taskCover = {} as ImageBase
        const videoKeys: string[] = []
        const coverKeys: string[] = []

        // 计算总文件大小
        let totalSize = 0
        let uploadedSize = 0
        if (isArray(videoAccounts)) {
          // 计算所有视频和封面的大小
          videoAccounts.forEach((account) => {
            const video = account.video!
            const videoSize = 'size' in video ? video.size : 0
            totalSize += videoSize
            totalSize += account.cover!.size
          })
          // 加上任务封面大小
          totalSize += videoAccounts[0].cover!.size
        } else {
          const video = videoAccounts.video!
          const videoSize = 'size' in video ? video.size : 0
          totalSize += videoSize
          totalSize += videoAccounts.cover!.size
          totalSize += videoAccounts.cover!.size // 任务封面
        }

        if (isArray(videoAccounts)) {
          // 同步上传视频
          for (let i = 0; i < videoAccounts.length; i++) {
            setPublishDescription(`正在上传第 ${i + 1}/${videoAccounts.length} 个视频...`)
            const video = videoAccounts[i].video!
            const file = 'file' in video ? video.file : video.path
            const videoSize = 'size' in video ? video.size : 0
            const videoKey = await uploadFile(file, 'cloud-publish', 'tianyiyun', (progress) => {
              const currentFileProgress = (progress / 100) * videoSize
              setPublishProgress(
                Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100),
              )
            })
            uploadedSize += videoSize
            videoKeys.push(videoKey)
          }

          // 同步上传封面
          for (let i = 0; i < videoAccounts.length; i++) {
            setPublishDescription(`正在上传第 ${i + 1}/${videoAccounts.length} 个封面...`)
            const cover = videoAccounts[i].cover!
            const coverSize = cover.size
            const coverFile = 'arrayBuffer' in cover ? cover.arrayBuffer : cover.path
            const coverKey = await uploadFile(
              coverFile,
              'cloud-publish',
              'tianyiyun',
              (progress) => {
                const currentFileProgress = (progress / 100) * coverSize
                setPublishProgress(
                  Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100),
                )
              },
            )
            uploadedSize += coverSize
            coverKeys.push(coverKey)
          }

          taskCover = videoAccounts[0].cover!
          req.coverKey = coverKeys[0]
          req.platformAccounts = videoAccounts.map((account, index) => ({
            platformAccountId: account.platforms!.id,
            coverKey: coverKeys[index],
            videoKey: videoKeys[index],
          }))
          req.platforms = videoAccounts.map((account) => account.platforms!.platformName)
          req.publishArgs = {
            accounts: req.platformAccounts.map((account, index) => {
              const video = videoAccounts[index].video
              const cover = videoAccounts[index].cover
              return {
                accountId: account.platformAccountId,
                cover: {
                  size: cover?.size,
                  key: account.coverKey,
                  width: cover?.width,
                  height: cover?.height,
                  type: cover?.type,
                },
                video: {
                  size: video?.size,
                  key: account.videoKey,
                  width: video?.width,
                  height: video?.height,
                  duration: video?.duration,
                },
              }
            }),
          }
        } else {
          setPublishDescription('正在上传视频...')
          const video = videoAccounts.video!
          const file = 'file' in video ? video.file : video.path
          const videoSize = 'size' in video ? video.size : 0
          const videoKey = await uploadFile(file, 'cloud-publish', 'tianyiyun', (progress) => {
            const currentFileProgress = (progress / 100) * videoSize
            setPublishProgress(Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100))
          })
          uploadedSize += videoSize

          setPublishDescription('正在上传封面...')
          const cover = videoAccounts.cover!
          const coverSize = cover.size
          const coverFile = 'arrayBuffer' in cover ? cover.arrayBuffer : cover.path
          const coverKey = await uploadFile(coverFile, 'cloud-publish', 'tianyiyun', (progress) => {
            const currentFileProgress = (progress / 100) * coverSize
            setPublishProgress(Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100))
          })
          uploadedSize += coverSize

          taskCover = videoAccounts.cover!
          req.platformAccounts = videoAccounts.platforms.map((platform) => ({
            platformAccountId: platform.id,
            coverKey,
            videoKey,
          }))
          req.platforms = videoAccounts.platforms.map((platform) => platform.platformName)
          req.publishArgs = {
            accounts: req.platformAccounts.map((account) => {
              const video = videoAccounts.video
              const cover = videoAccounts.cover
              return {
                accountId: account.platformAccountId,
                cover: {
                  size: cover?.size,
                  key: account.coverKey,
                  width: cover?.width,
                  height: cover?.height,
                  type: cover?.type,
                },
                video: {
                  size: video?.size,
                  key: account.videoKey,
                  width: video?.width,
                  height: video?.height,
                  duration: video?.duration,
                },
              }
            }),
          }
        }

        // 上传任务封面
        setPublishDescription('正在上传任务封面...')
        const taskCoverSize = taskCover.size
        const coverFile = 'arrayBuffer' in taskCover ? taskCover.arrayBuffer : taskCover.path
        const coverKey = await uploadFile(coverFile, 'material-library', 'aliyun', (progress) => {
          const currentFileProgress = (progress / 100) * taskCoverSize
          setPublishProgress(Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100))
        })
        req.coverKey = coverKey
        req.publishArgs.aPlatform = aPlatform
        req.publishArgs.bPlatform = bPlatform
        req.publishArgs.categories = categories
        req.publishArgs.isOriginal = false
        req.publishArgs.timing = timing

        setPublishDescription('正在发布...')
        publishMutation.mutate(req)
      } catch (error) {
        console.error(error)
        setOpen(false)
      }
    }
  }

  return {
    handlePublish,
    selectedAccounts,
    setSelectedAccounts,
    formMethods,
    publishDialogJsx,
  }
}
