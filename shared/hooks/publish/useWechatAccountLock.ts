import { useCallback, useEffect, useMemo, useRef } from 'react'
import {
  generateWechatLockKey,
  unlockWechat<PERSON>ock<PERSON>ey,
  updateWechatLockKeyAlive,
} from '@/lib/http'
import type { PlatformAccount } from '@/types/account'
import { platformNames } from '@/lib/platform'
import { difference } from 'lodash'

interface WechatLockInfo {
  accountId: string
  wxkey: string
}

export const useWechatAccountLock = (
  selectedAccounts: PlatformAccount[],
  setSelectedAccounts: (accounts: PlatformAccount[]) => void,
) => {
  const lockInfoRef = useRef<WechatLockInfo[]>([])
  const heartbeatTimerRef = useRef<NodeJS.Timeout | null>(null)
  const isUnlockedRef = useRef(true)
  const oldSubAccountParentIdsRef = useRef<string[]>([])

  // 获取所有的子账号parentId 去重列表
  const subAccountParentIds = useMemo(() => {
    return [
      ...new Set(
        selectedAccounts
          .filter(
            (account) => account.platformName === platformNames.WeiXinShiPinHao && account.parentId,
          )
          .map((account) => account.parentId!),
      ),
    ]
  }, [selectedAccounts])

  // 处理账号锁定
  const handleAccountLock = useCallback(async () => {
    if (subAccountParentIds.length === 0) {
      return []
    }

    try {
      // 获取锁定密钥
      const lockResults = await generateWechatLockKey(subAccountParentIds)

      // 更新锁定信息
      lockInfoRef.current = lockResults.map((result) => ({
        accountId: result.platformAccountId,
        wxkey: result.wxkey,
      }))

      // 启动心跳
      startHeartbeat()
      return []
    } catch (error) {
      console.error('账号锁定失败:', error)
      // 锁定失败，从列表中移除该账号
      const failedAccountIds = subAccountParentIds
      setSelectedAccounts(
        selectedAccounts.filter(
          (account) => !account.parentId || !failedAccountIds.includes(account.parentId),
        ),
      )
      return failedAccountIds
    }
  }, [selectedAccounts, setSelectedAccounts, subAccountParentIds])

  // 启动心跳
  const startHeartbeat = () => {
    // 清除已存在的心跳
    if (heartbeatTimerRef.current) {
      clearInterval(heartbeatTimerRef.current)
    }

    // 每30秒发送一次心跳
    heartbeatTimerRef.current = setInterval(async () => {
      try {
        await Promise.all(
          lockInfoRef.current.map(({ accountId, wxkey }) =>
            updateWechatLockKeyAlive(accountId, wxkey),
          ),
        )
      } catch (error) {
        console.error('心跳更新失败:', error)
      }
    }, 30000)
  }

  // 解锁账号
  const unlockAccounts = async () => {
    try {
      if (isUnlockedRef.current) {
        await Promise.all(
          lockInfoRef.current.map(({ accountId, wxkey }) => unlockWechatLockKey(accountId, wxkey)),
        )
      }
    } catch (error) {
      console.error('解锁失败:', error)
    } finally {
      // 清理心跳
      if (heartbeatTimerRef.current) {
        clearInterval(heartbeatTimerRef.current)
      }
      // 清空锁定信息
      lockInfoRef.current = []
    }
  }

  // 监听选中账号变化
  useEffect(() => {
    const handleAccountsChange = async () => {
      // 先解锁
      await unlockAccounts()
      // 再锁定
      await handleAccountLock()
    }
    if (
      oldSubAccountParentIdsRef.current.length !== subAccountParentIds.length ||
      difference(oldSubAccountParentIdsRef.current, subAccountParentIds).length > 0
    ) {
      handleAccountsChange()
      oldSubAccountParentIdsRef.current = subAccountParentIds
    }
  }, [handleAccountLock, subAccountParentIds])

  useEffect(() => {
    return () => {
      console.log('清理')
      unlockAccounts()
    }
  }, [])

  return {
    isUnlockedRef,
  }
}
