import { createTask } from '@/lib/http'
import { LoadingContainer } from '@/components/loading'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useAppStore } from '@/stores'
import { Progress } from '@/components/ui/progress'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { f7 } from 'framework7-react'
import { useState } from 'react'

export const usePublishMutation = (isUnlockedRef: React.RefObject<boolean>) => {
  const [open, setOpen] = useState(false)
  const setNavIndex = useAppStore((state) => state.setNavIndex)
  const [publishDescription, setPublishDescription] = useState('')
  const [publishProgress, setPublishProgress] = useState(0)
  const queryClient = useQueryClient()
  const publishMutation = useMutation({
    mutationFn: createTask,
    onSuccess: () => {
      isUnlockedRef.current = false
      setOpen(false)
      queryClient.resetQueries({
        queryKey: ['taskList'],
      })
      setNavIndex(2)
      f7.views.main.router.back()
    },
    onError: () => {
      setOpen(false)
    },
  })
  const publishDialogJsx = (
    <AlertDialog open={open}>
      <AlertDialogContent
        className="w-10/12 rounded-lg"
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <AlertDialogHeader>
          <AlertDialogTitle>作品发布</AlertDialogTitle>
          <AlertDialogDescription>{publishDescription}</AlertDialogDescription>
          {publishProgress > 0 ? <Progress value={publishProgress} /> : <LoadingContainer />}
        </AlertDialogHeader>
      </AlertDialogContent>
    </AlertDialog>
  )

  return {
    setPublishDescription,
    setPublishProgress,
    publishMutation,
    publishDialogJsx,
    setOpen,
  } as const
}
