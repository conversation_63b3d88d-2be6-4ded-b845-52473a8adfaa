import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useAuthorizeMutation } from './user'
import { loginSuccess } from '@/lib/http'
import { SubmitHand<PERSON>, useController, useForm } from 'react-hook-form'
import { LoginFormInputs, loginSchema } from '@/types'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'
import { zodResolver } from '@hookform/resolvers/zod'

export const useLogin = (next: () => void) => {
  const [isAgree, setIsAgree] = useState(false)
  const queryClient = useQueryClient()
  const loginMutation = useAuthorizeMutation({
    onSuccess: (data) => {
      loginSuccess(data.authorization)
      queryClient.clear()
      // 登录成功后回到上一页
      next()
      //   f7router.navigate('/', {
      //     reloadAll: true,
      //   })
    },
  })

  // Initialize react-hook-form
  const form = useForm<LoginFormInputs>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
    defaultValues: {
      phone: '',
      code: '',
      password: '',
      loginType: 'code',
    },
  })

  const {
    control,
    watch,
    handleSubmit,
    formState: { isValid },
  } = form

  // 监听登录类型
  const loginType = useController({
    name: 'loginType',
    control,
  })

  const phone = watch('phone')

  // 按钮禁用状态
  const isLoginButtonDisabled = !isValid || loginMutation.isPending

  // New submit handler using react-hook-form
  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    const next = () => {
      const { loginType, code, phone, password } = data
      if (loginType === 'code') {
        loginMutation.mutate({
          phone,
          code,
        })
      } else {
        loginMutation.mutate({
          phone,
          password,
        })
      }
    }
    if (!isAgree) {
      showConfirmAgreementDialog(next)
      return
    }

    // 使用 React Query mutation 进行登录
  }

  // 未勾选用户协议提示
  const showConfirmAgreementDialog = (next: () => void) => {
    alertBaseManager.open({
      title: '提示',
      description: '请阅读并同意《蚂蚁小二隐私政策》',
      okText: '同意并继续',
      cancelText: '取消',
      onSubmit: () => {
        setIsAgree(true)
        next()
      },
    })
  }

  return {
    form,
    isLoginButtonDisabled,
    handleSubmit: handleSubmit(onSubmit),
    isAgree,
    setIsAgree,
    phone,
    loginType: loginType.field,
    isPending: loginMutation.isPending,
  }
}
