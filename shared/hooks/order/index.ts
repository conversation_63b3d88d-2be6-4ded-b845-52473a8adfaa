import { getOrderPayInfo, getOrderStatus } from '@/lib/http'
import { createQueryHook } from '@/lib/query'

const useOrderPayInfoQuery = createQueryHook((orderNo: string) => ({
  queryKey: ['getOrderPayInfo', orderNo],
  queryFn: () => getOrderPayInfo(orderNo),
}))

const useOrderStatusQuery = createQueryHook((orderNo: string) => ({
  queryKey: ['getOrderStatus', orderNo],
  queryFn: () => getOrderStatus(orderNo),
}))

export { useOrderPayInfoQuery, useOrderStatusQuery }
