import { useState } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import React from 'react'

export const PasswordInput = React.forwardRef<
  React.ElementRef<typeof Input>,
  React.ComponentPropsWithoutRef<typeof Input> & { asChild?: boolean; butClass?: string }
>(({ className, butClass, asChild = false, ...props }, ref) => {
  const [show, setShow] = useState(false)
  const inputElement = (
    <>
      <Input
        ref={ref}
        className={cn('h-14 pr-12 text-base', className)}
        type={show ? 'text' : 'password'}
        placeholder="请输入密码"
        {...props}
      />
      <Button
        onClick={() => setShow(!show)}
        size="icon"
        type="button"
        variant="ghost"
        className={cn('absolute right-3 top-1/2 -translate-y-1/2', butClass)}
      >
        {show ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />}
      </Button>
    </>
  )
  return asChild ? inputElement : <div className="relative">{inputElement}</div>
})
