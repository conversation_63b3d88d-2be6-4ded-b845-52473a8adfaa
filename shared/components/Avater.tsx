import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import type { HTMLAttributes } from 'react'
import AvatarDefault from '@/assets/svg/avatar_default.svg?react'
export function AvatarBase({
  src,
  alt,
  fallback = <AvatarDefault />,
  ...props
}: { src: string; alt: string; fallback?: React.ReactNode } & HTMLAttributes<HTMLDivElement>) {
  return (
    <Avatar {...props}>
      <AvatarImage src={src} alt={alt} />
      <AvatarFallback>{fallback}</AvatarFallback>
    </Avatar>
  )
}
