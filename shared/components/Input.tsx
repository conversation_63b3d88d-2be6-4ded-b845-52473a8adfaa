import { cn } from '@/lib/utils'
import { Input as ShadcnInput } from '@/components/ui/input'
import React from 'react'

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<typeof ShadcnInput>>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnInput
        className={cn(
          'border-none text-base font-medium shadow-none ring-0 focus-visible:ring-0',
          className,
        )}
        ref={ref}
        {...props}
      />
    )
  },
)

Input.displayName = 'Input'

export { Input }
