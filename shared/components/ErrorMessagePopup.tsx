import React, { useMemo, useState } from 'react'
import { Too<PERSON>bar } from 'framework7-react'
import { TriangleAlert } from 'lucide-react'
import { parseErrorMessage } from '@/utils'
import { cn } from '@/lib/utils'
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/Drawer'
import { Button } from '@/components/Button'
import { Drag } from '@nutui/nutui-react'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'

interface ErrorFabProps {
  errors: string[]
}

export const ErrorFab: React.FC<ErrorFabProps> = ({ errors }) => {
  const [showErrorPopup, setShowErrorPopup] = useState(false)

  const platformErrors = useMemo(() => {
    return errors.reduce(
      (acc, error) => {
        const { platformName, message } = parseErrorMessage(error)
        if (!acc[platformName]) {
          acc[platformName] = []
        }
        acc[platformName].push(message)
        return acc
      },
      {} as Record<string, string[]>,
    )
  }, [errors])

  if (!errors.length) {
    return null
  }

  return (
    <Drawer open={showErrorPopup} onOpenChange={setShowErrorPopup} setBackgroundColorOnScale>
      <DrawerTrigger asChild>
        <Drag
          boundary={{ top: 100, left: 0, bottom: 0, right: 0 }}
          attract
          style={{ bottom: '100px', right: '120px' }}
        >
          <Button variant={'outline'} className="rounded-full px-3">
            <div className="flex items-center gap-2 text-destructive">
              <TriangleAlert className="h-4 w-4" />
              <span>
                <span className="mr-0.5 font-semibold">{errors.length}</span>项错误
              </span>
            </div>
          </Button>
        </Drag>
      </DrawerTrigger>
      <DrawerContent>
        <VisuallyHidden>
          <DrawerTitle></DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </VisuallyHidden>
        <Toolbar className="border-b border-border">
          <div className="left">
            <div className="flex items-center gap-2 px-3 py-2 text-destructive">
              <TriangleAlert className="h-4 w-4" />
              <span>
                <span className="mr-0.5 font-semibold">{errors.length}</span>项错误
              </span>
            </div>
          </div>
          <div className="right">
            <Button variant="link" onClick={() => setShowErrorPopup(false)}>
              关闭
            </Button>
          </div>
        </Toolbar>
        <div className="flex max-h-[80vh] flex-col overflow-y-auto p-4">
          {Object.entries(platformErrors).map(([platform, errors], index) => (
            <div
              key={platform}
              className={cn('py-3', {
                'border-t border-border': index,
              })}
            >
              <div className="mb-1 text-base font-semibold">
                {platform}({errors.length})
              </div>
              <ul>
                {errors.map((error, index) => (
                  <li key={index}>
                    <div className="flex items-center gap-2">
                      <div className="h-1 w-1 rounded-full bg-destructive"></div>
                      <span className="text-sm text-secondary-foreground">{error}</span>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </DrawerContent>
    </Drawer>
  )
}
