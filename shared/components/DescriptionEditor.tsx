import type { Editor } from '@tiptap/react'
import { EditorProvider } from '@tiptap/react'
import Placeholder from '@tiptap/extension-placeholder'
import { pasteRegex, Topic } from '@/components/TiptapEditor/extensions/topic'
import StarterKit from '@tiptap/starter-kit'
import { useEffect, useRef } from 'react'
import { Button } from '@/components/Button'
import type { Node } from '@tiptap/pm/model'

export function DescriptionEditor({
  description,
  onChange,
  isSupportTopic = true,
  editorRef,
}: {
  description: string
  onChange: (description: string) => void
  // 是否支持话题
  isSupportTopic?: boolean
  editorRef?: React.MutableRefObject<Editor | null>
}) {
  const contentEditorRef = useRef<Editor | undefined>(undefined)
  function insertTopic() {
    contentEditorRef.current?.commands.focus()
    contentEditorRef.current?.commands.insertContent('#')
  }

  const oldDescription = useRef(description)

  useEffect(() => {
    if (!oldDescription.current) {
      oldDescription.current = description
      contentEditorRef.current?.commands.setContent(description)
    }
  }, [description])

  return (
    <div className="flex w-full flex-col gap-3">
      <EditorProvider
        extensions={[
          StarterKit,
          Placeholder.configure({ placeholder: '请输入描述...' }),
          ...(isSupportTopic ? [Topic] : []),
        ]}
        onUpdate={({ editor }) => {
          const html = isSupportTopic ? editor.getHTML() : editor.getText()
          onChange(html)
        }}
        onCreate={({ editor }) => {
          contentEditorRef.current = editor
          if (editorRef) {
            editorRef.current = editor
          }
        }}
        content={description}
        parseOptions={{
          preserveWhitespace: true,
        }}
        editorProps={{
          attributes: {
            class:
              'h-20 pt-2 text-sm transition-colors focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 rounded-md overflow-y-auto  focus:outline-none',
          },
          handlePaste: (view, event) => {
            const { state, dispatch } = view
            const text = event.clipboardData?.getData('text/plain') || ''
            // 判定是否有换行符
            if (!text.includes('\n')) {
              return false
            }
            event.preventDefault()

            // 按换行符拆分文本，每个段落作为一个 paragraph
            const paragraphs = text.split('\n').map((line) => {
              const nodes: Node[] = []
              const formattedLine = line + ' '
              // 解析 #topic 语法
              const match = formattedLine.match(pasteRegex)
              if (match) {
                if (match) {
                  match.forEach((m) => {
                    nodes.push(
                      state.schema.nodes.topic.create({
                        text: m.replace('#', '').trim(),
                      }), // 解析成 topic 节点
                    )
                    line = formattedLine.replace(pasteRegex, '') // 移除原始的 #topic
                  })
                }
                // 移除原始的 #topic
              }

              // 处理剩余文本
              if (line.trim() !== '') {
                nodes.push(state.schema.text(line))
              }

              // 返回一个 paragraph
              return state.schema.nodes.paragraph.create(null, nodes)
            })

            const { selection } = state
            const transaction = state.tr.replaceRangeWith(
              selection.from - 1,
              selection.to + 1,
              state.schema.nodes.doc.create(null, paragraphs),
            )

            dispatch(transaction)

            return true
          },
          handleClick: () => {
            console.log('click')
            return true
          },
        }}
      />
      {isSupportTopic && (
        <div className="flex gap-3">
          <Button variant="outline" size="sm" className="" onClick={insertTopic}>
            #添加话题
          </Button>
          {/* <TopicsDialog
            onSubmit={(value) => {
              if (!value.length) return;
              // 增加话题
              value.forEach((item) => {
                editerRef.current?.commands.addTopic({ text: `${item}` });
              });
            }}
          /> */}
        </div>
      )}
    </div>
  )
}
