import { Search } from 'lucide-react'
import { debounce } from 'lodash'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

export default function SearchInput({
  className,
  onSearch,
  onChange,
  warpClass,
  ...props
}: React.ComponentProps<typeof Input> & {
  onSearch?: (value: string) => void
  warpClass?: string
}) {
  // onsearch防抖处理
  const debounceSearch = onSearch ? debounce(onSearch, 500) : undefined
  return (
    <div className={cn('relative ml-auto flex-1', warpClass)}>
      <div className="absolute left-2 flex h-full items-center text-muted-foreground">
        <Search className="h-4 w-4" />
      </div>
      <Input
        type="search"
        placeholder="Search..."
        className={cn('w-full pl-8', className)}
        onChange={(e) => {
          onChange?.(e)
          debounceSearch?.(e.target.value)
        }}
        {...props}
      />
    </div>
    // <div className={cn("px-1.5 flex items-center rounded-md border border-input text-gray-500 gap-2", className)}>
    //    <Search className="h-4 w-4" />
    //    <Input className="flex-1 px-0 border-none shadow-none focus-visible:ring-0" type="search" {...props}/>
    // </div>
  )
}
