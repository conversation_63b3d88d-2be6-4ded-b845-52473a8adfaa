import { useRef } from 'react'
import { Clock, ChevronRight } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { DateUtils } from '@/utils'
import { XBtn } from '@/components/XBtn'
import { Capacitor } from '@capacitor/core'

interface TimingPublishProps {
  value: number
  onChange: (value: number) => void
}

export const TimingPublish = ({ value, onChange }: TimingPublishProps) => {
  const timeInputRef = useRef<HTMLInputElement>(null)

  return (
    <div
      className="flex cursor-pointer items-center justify-between gap-4"
      onClick={() => {
        timeInputRef.current?.focus()
      }}
    >
      <span className="flex items-center gap-3">
        <Clock className="h-5 w-5" />
        <span className="text-base font-medium">定时发布</span>
      </span>

      <div className="flex items-center">
        <Input
          ref={timeInputRef}
          type="datetime-local"
          className="h-auto w-auto min-w-24 border-none text-muted-foreground shadow-none"
          value={value ? DateUtils.formatDate(value) : ''}
          onChange={(e) => {
            const value = e.target.value
            if (value) {
              const date = DateUtils.parseDate(value)
              onChange(date?.getTime() ?? 0)
            } else {
              onChange(0)
            }
          }}
        />
        {value ? (
          <XBtn
            onClick={(e) => {
              e.stopPropagation()
              onChange(0)
              if (timeInputRef.current) {
                timeInputRef.current.value = ''
              }
            }}
          />
        ) : (
          Capacitor.getPlatform() !== 'android' && <ChevronRight />
        )}
      </div>
    </div>
  )
}
