import { cn } from '@/lib/utils'
import { HTMLAttributes, useState } from 'react'
import errorImage from '@/assets/svg/error_image.svg'
export const ImageBase = ({
  src,
  className,
  ...props
}: { src: string } & HTMLAttributes<HTMLDivElement>) => {
  const [image, setImage] = useState<string>(src)
  return (
    <div {...props} className={cn('h-full w-full', className)}>
      <img
        className="h-full w-full object-cover"
        src={image}
        onError={() => setImage(errorImage)}
        alt="image"
      />
    </div>
  )
}
