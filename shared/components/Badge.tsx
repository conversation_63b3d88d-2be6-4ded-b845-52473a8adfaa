import { cn } from '@/lib/utils'
import type { HTMLAttributes } from 'react'
import { XBtn } from './XBtn'

type BadgeProps = HTMLAttributes<HTMLDivElement> & {
  onDelete?: () => void
}

export const Badge = ({ children, className, onDelete, ...props }: BadgeProps) => {
  return (
    <div
      className={cn('flex h-9 items-center gap-2 rounded-md bg-secondary px-2', className)}
      {...props}
    >
      {children}
      {onDelete && <XBtn onClick={onDelete}></XBtn>}
    </div>
  )
}
