import { cn } from '@/lib/utils'
import { useSystemStore } from '@/stores'
import { useShallow } from 'zustand/react/shallow'
import React from 'react'

type LoadingProps = React.HTMLProps<HTMLDivElement>
export function Loading({ className, ...props }: LoadingProps) {
  return (
    <div
      {...props}
      className={cn(
        'size-7 animate-spin rounded-full border-[3px] border-muted-foreground border-l-transparent',
        className,
      )}
    />
  )
}

export function LoadingContainer({ className, ...props }: LoadingProps) {
  return (
    <div className="flex w-full flex-1 items-center justify-center">
      <Loading className={cn('', className)} {...props} />
    </div>
  )
}

export function LoadingToast({ className, ...props }: LoadingProps) {
  const [isLoading, { text }] = useSystemStore(
    useShallow((state) => [state.isLoading, state.loadingOption]),
  )
  return (
    <div
      className={cn(
        'fixed bottom-1/4 left-1/2 z-[13000] flex translate-x-[-50%] items-center justify-center gap-2 rounded-full bg-foreground/80 py-1.5 pl-2 pr-3 text-xs font-semibold text-background transition-opacity duration-200 ease-in-out',
        {
          'opacity-0': !isLoading,
        },
      )}
    >
      <Loading className={cn('size-4 border-2 border-background border-l-transparent', className)} {...props} />
      <span>{text}</span>
    </div>
  )
}
