import React, { useMemo, useRef } from 'react'
import { X } from 'lucide-react'
import { Button } from '@/components/Button'
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/Drawer'
import { useControllableState } from '@/hooks/common/use-controllable-state'
import { VisuallyHidden } from '@/components/VisuallyHidden'

const Popup = <T = void,>({
  open,
  defaultOpen = false,
  onOpenChange,
  children,
  onConfirm,
  title,
}: {
  open?: boolean
  defaultOpen?: boolean
  onOpenChange?: (open: boolean) => void
  children:
    | React.ReactNode
    | ((helpers: { setData: (val: T) => void; data?: T }) => React.ReactNode)
  onConfirm: (data: T | undefined) => void
  title: string
}) => {
  const [isOpen, setIsOpen] = useControllableState<boolean>({
    prop: open,
    defaultProp: defaultOpen,
    onChange: onOpenChange,
  })

  const dataRef = useRef<T | undefined>(undefined)

  const content = useMemo(() => {
    if (typeof children === 'function') {
      return children({
        setData: (val: T) => {
          dataRef.current = val
        },
        data: dataRef.current,
      })
    }
    return children
  }, [children])

  return (
    <Drawer open={isOpen} repositionInputs={false} onOpenChange={setIsOpen}>
      <DrawerContent hideHandle>
        <VisuallyHidden>
          <DrawerTitle></DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </VisuallyHidden>
        <div className="relative flex h-[50px] items-center justify-center">
          <div className="absolute left-0 top-0 flex h-full w-12 items-center justify-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setIsOpen(false)
              }}
            >
              <X className="h-5 w-5 rounded-full text-secondary-foreground" />
            </Button>
          </div>
          <span className="text-base font-medium">{title}</span>
          <div className="absolute right-0 top-0 flex h-full items-center justify-center">
            <Button
              className="rounded-full text-primary"
              variant="ghost"
              onClick={() => {
                setIsOpen(false)
                setTimeout(() => {
                  onConfirm(dataRef.current)
                  dataRef.current = undefined
                }, 300)
              }}
            >
              确定
            </Button>
          </div>
        </div>
        <div className="flex h-[85vh] flex-col">{content}</div>
      </DrawerContent>
    </Drawer>
  )
}

export { Popup as default }
