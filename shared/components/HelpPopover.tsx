import type { FC } from 'react';
import { Link, Popover } from 'framework7-react';
import { HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HelpPopoverProps {
  children: React.ReactNode;
  popoverKey: string;
  className?: string;
}

export const HelpPopover: FC<HelpPopoverProps> = ({
  children,
  className = '',
  popoverKey,
}) => {
  return (
    <>
      <Link
        popoverOpen={`.${popoverKey}`}
        className={`inline-flex h-5 w-5 cursor-pointer items-center justify-center ${className}`}
      >
        <HelpCircle className="h-5 w-5 text-muted-foreground" />
      </Link>
      <Popover className={cn('p-4', popoverKey)}>{children}</Popover>
    </>
  );
};
