import { Sheet, List, ListItem, f7, Preloader } from 'framework7-react'
import { authTeam } from '@/lib/http'
import { useAppStore } from '@/stores'
import { useShallow } from 'zustand/react/shallow'
import VipIcon from '@/assets/svg/vip.svg'
import { useQueryClient } from '@tanstack/react-query'
import { useTeamListQuery } from '@/hooks/team/team'
import { Check } from 'lucide-react'
import { setToken } from '@/lib/http'
import { Button } from '@/components/Button'
import { loadingManager } from '@/stores'

interface TeamSwitcherProps {
  opened: boolean
  onSheetClosed: () => void
}

export const TeamSwitcher = ({ opened, onSheetClosed }: TeamSwitcherProps) => {
  const { team } = useAppStore(
    useShallow((state) => ({
      team: state.team,
    })),
  )

  const queryClient = useQueryClient()

  // 使用React Query获取团队列表
  const { data: teamsData, isLoading } = useTeamListQuery({
    enabled: opened,
  })

  // 切换团队的处理函数
  const handleTeamSwitch = async (teamId: string) => {
    try {
      loadingManager.show('切换团队中')

      // 授权并切换到新团队
      const res = await authTeam(teamId)

      setToken(res.authorization)
      queryClient.clear()

      f7.views.current.router.navigate('/', {
        reloadAll: true,
      })

      // 关闭弹窗和加载指示器
      f7.sheet.close()
      loadingManager.hide()

      // 刷新相关数据
    } catch (error) {
      console.error('团队切换失败', error)
    }
  }

  return (
    <Sheet
      opened={opened}
      onSheetClosed={onSheetClosed}
      backdrop
      className="!h-auto !bg-transparent p-3 before:!hidden"
    >
      <div className="pb-safe">
        {isLoading && (
          <div className="flex h-full items-center justify-center">
            <Preloader className="h-10 w-10 text-primary" />
          </div>
        )}
        <List
          className="!my-4 overflow-hidden rounded-lg bg-background"
          dividersIos
          mediaList
          outlineIos
          strongIos
        >
          {teamsData?.data?.map((teamItem) => (
            <ListItem key={teamItem.id} onClick={() => handleTeamSwitch(teamItem.id)}>
              <img
                slot="media"
                src={teamItem.logoUrl}
                alt={teamItem.name}
                className="h-9 w-9 rounded-lg"
              />
              <div className="flex items-center justify-between gap-1 py-3" slot="title">
                <span className="flex-1 truncate">{teamItem.name}</span>
                {teamItem.isVip && <img src={VipIcon} alt="VIP" className="h-4 w-4 shrink-0" />}
              </div>
              {teamItem.id === team?.id && (
                <div slot="after">
                  <Check className="h-5 w-5 text-primary" />
                </div>
              )}
            </ListItem>
          ))}
          {teamsData?.data?.length === 0 && (
            <div className="flex h-20 items-center justify-center text-muted-foreground">
              暂无团队数据
            </div>
          )}
        </List>
        <Button
          variant="secondary"
          className="h-11 w-full rounded-lg bg-background"
          onClick={onSheetClosed}
        >
          取消
        </Button>
      </div>
    </Sheet>
  )
}
