/**
 * 基础UI组件
 *
 * 这些组件是通过shadcn CLI添加的基础组件，它们是构建其他复杂组件的基础。
 * 当使用shadcn CLI添加新组件后，请在这里导出它们。
 */

// 按钮相关组件
export * from './button';

// 表单相关组件
export * from './input';
export * from './form';
export * from './checkbox';
export * from './label';
export * from './select';
export * from './textarea';
export * from './switch';
export * from './radio-group';
export * from './slider';

// 对话框相关组件
export * from './dialog';
export * from './alert-dialog';
export * from './sheet';
export * from './drawer';

// 菜单相关组件
export * from './dropdown-menu';
export * from './context-menu';
export * from './menubar';

// 数据展示组件
export * from './avatar';
export * from './badge';
export * from './card';
export * from './progress';
export * from './separator';
export * from './table';
export * from './tabs';
export * from './tooltip';

// 其他组件
export * from './accordion';
export * from './alert';
export * from './aspect-ratio';
export * from './calendar';
export * from './carousel';
export * from './collapsible';
export * from './command';
export * from './hover-card';
export * from './popover';
export * from './scroll-area';
export * from './skeleton';
export * from './toggle';
export * from './toggle-group';

/**
 * 注意：上面的导出语句会在使用shadcn CLI添加组件后生效。
 * 如果尚未添加某个组件，相应的导出语句会导致TypeScript错误，
 * 可以先注释掉未添加组件的导出语句。
 */
