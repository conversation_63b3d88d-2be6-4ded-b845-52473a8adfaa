import { useState, useEffect } from 'react'
import { Popup, Page, Navbar, NavTitle, Link, Checkbox, NavLeft } from 'framework7-react'
import type { PlatformAccount } from '@/types'
import { useAccounts } from '@/hooks/useAccounts'
import { AccountFilters, AccountList } from '@/components/accounts'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'
import { Button } from '../ui/button'
import { isAccountEnabled } from '@/services/account'
import { toast } from 'sonner'

interface AccountSelectorPopupProps {
  isOpen: boolean
  onClose: () => void
  selectedAccounts: PlatformAccount[]
  onSelect: (accounts: PlatformAccount[]) => void
  mode?: 'single' | 'multiple'
  filter?: (item: PlatformAccount) => boolean
}

export const AccountSelectorPopup: React.FC<AccountSelectorPopupProps> = ({
  isOpen,
  onClose,
  selectedAccounts,
  onSelect,
  mode = 'multiple',
  filter,
}) => {
  // 本地选择状态
  const [localSelectedAccounts, setLocalSelectedAccounts] =
    useState<PlatformAccount[]>(selectedAccounts)

  // 使用useAccounts hook获取账号列表和筛选功能
  const { accounts, isLoading, isError, error, filters, setFilters, platforms } = useAccounts(
    {},
    filter,
  )

  // 同步外部选中状态
  useEffect(() => {
    if (isOpen) {
      setLocalSelectedAccounts([...selectedAccounts])
    }
  }, [selectedAccounts, isOpen])

  const handleConfirm = (accounts: PlatformAccount[]) => {
    onSelect(accounts)
    onClose()
  }

  const handleCancel = () => {
    // 还原为原始选择
    setLocalSelectedAccounts([...selectedAccounts])
    onClose()
  }

  // 检查当前筛选结果中的可用账号是否全部被选中
  const isAllSelected =
    accounts.length > 0 &&
    accounts.filter(isAccountEnabled).every((account) =>
      localSelectedAccounts.some((selected) => selected.id === account.id),
    )

  const handleAccountSelect = (account: PlatformAccount, isSelected: boolean) => {
    if (mode === 'single') {
      setLocalSelectedAccounts([account])
      handleConfirm([account])
    } else {
      if (isSelected) {
        setLocalSelectedAccounts((prev) => prev.filter((acc) => acc.id !== account.id))
      } else {
        if (localSelectedAccounts.length >= 20) {
          toast.warning('最多只能选择20个账号')
          return
        }
        setLocalSelectedAccounts((prev) => [...prev, account])
      }
    }
  }

  return (
    <Popup opened={isOpen} onPopupClosed={onClose} className="h-full">
      <Page className="accounts-page">
        <Navbar>
          <NavLeft>
            <Link onClick={handleCancel}>
              <Button variant="ghost" className="ml-1 p-0" size="icon">
                <X className="h-6 w-6 text-muted-foreground" />
              </Button>
            </Link>
          </NavLeft>
          <NavTitle>选择账号</NavTitle>
        </Navbar>
        {/* 筛选栏 */}
        <div className="flex h-full flex-col">
          <AccountFilters
            platforms={platforms}
            value={filters}
            onChange={setFilters}
            isLoadingPlatforms={isLoading}
            isErrorPlatforms={isError}
          />

          {/* 账号列表 */}
          <div
            className={cn(
              'accounts-list-container flex-grow overflow-y-auto',
              mode === 'multiple' && 'pb-12',
            )}
          >
            <AccountList
              accounts={accounts}
              isLoading={isLoading}
              isError={isError}
              error={error}
              hasFilters={!!(filters.platform || filters.group)}
              selectedAccounts={localSelectedAccounts}
              onAccountSelect={handleAccountSelect}
            />
          </div>
        </div>

        {/* 确认按钮 */}
        {mode === 'multiple' && (
          <div className="fixed bottom-0 left-0 right-0 z-10 bg-secondary pb-safe">
            <div className="flex items-center justify-between px-4 py-2">
              <label className="flex items-center gap-2">
                <Checkbox
                  checked={isAllSelected}
                  onChange={(e) => {
                    if (e.target.checked) {
                      // 全选当前筛选后的可用账号列表，同时保留已选中但不在当前筛选结果中的账号
                      const enabledAccounts = accounts.filter(isAccountEnabled)
                      const currentIds = enabledAccounts.map((account) => account.id)
                      const existingSelected = localSelectedAccounts.filter(
                        (selected) => !currentIds.includes(selected.id),
                      )
                      const newSelectedAccounts = [...existingSelected, ...enabledAccounts]
                      if (newSelectedAccounts.length > 20) {
                        toast.warning('选择账号总数不能超过20个')
                        return
                      }
                      setLocalSelectedAccounts(newSelectedAccounts)
                    } else {
                      // 取消选择当前筛选后的账号列表，保留不在当前筛选结果中的账号
                      const currentIds = accounts.map((account) => account.id)
                      setLocalSelectedAccounts(
                        localSelectedAccounts.filter(
                          (selected) => !currentIds.includes(selected.id),
                        ),
                      )
                    }
                  }}
                />
                <span className={cn(isAllSelected && 'text-primary')}>全选</span>
              </label>
              <Button
                disabled={localSelectedAccounts.length === 0}
                onClick={() => handleConfirm(localSelectedAccounts)}
              >
                确认
                {localSelectedAccounts.length > 0 && <span>({localSelectedAccounts.length})</span>}
              </Button>
            </div>
          </div>
        )}
      </Page>
    </Popup>
  )
}
