import React from 'react'
import { Block, Checkbox } from 'framework7-react'
import type { PlatformAccount } from '@/types'
import emptyImg from '@/assets/images/task-empty.png'
import { AccountAvatar } from './AccountAvatar'
import { LoadingContainer } from '../loading'
import { cn } from '@/lib/utils'
import { getAccountDisabledReason, isAccountEnabled } from '@/services/account'
import { Badge } from '../ui/badge'

interface AccountListProps {
  accounts: PlatformAccount[]
  isLoading?: boolean
  isError?: boolean
  error?: Error
  hasFilters?: boolean
  selectedAccounts?: PlatformAccount[]
  onAccountSelect?: (account: PlatformAccount, isSelected: boolean) => void
}

export const AccountList: React.FC<AccountListProps> = ({
  accounts,
  isLoading = false,
  isError = false,
  error,
  hasFilters = false,
  selectedAccounts = [],
  onAccountSelect,
}) => {
  // 判断账号是否被选中
  const isAccountSelected = (accountId: string) => {
    return selectedAccounts.some((acc) => acc.id === accountId)
  }

  const handleAccountSelect = (account: PlatformAccount, isSelected: boolean) => {
    onAccountSelect?.(account, isSelected)
  }

  if (isLoading) {
    return <LoadingContainer />
  }

  if (isError) {
    return <Block className="mt-10 text-center text-destructive">加载失败: {error?.message}</Block>
  }

  if (accounts.length === 0) {
    return (
      <Block className="text-center">
        <div className="mt-24 flex flex-col items-center">
          <img src={emptyImg} alt="暂无账号" className="mx-auto w-32" />
          <p className="mt-3 text-secondary-foreground">
            {hasFilters ? '没有符合条件的账号' : '暂无账号，请添加账号'}
          </p>
        </div>
      </Block>
    )
  }

  return (
    <div className="px-4">
      {accounts.map((account) => {
        const isSelected = isAccountSelected(account.id)
        const isEnable = isAccountEnabled(account)

        return (
          <label
            key={account.id}
            className={cn('flex h-16 items-center justify-between gap-3', {
              'opacity-45': !isEnable,
            })}
          >
            <div className="flex items-center flex-1 gap-3">
              {onAccountSelect && (
                <Checkbox
                  disabled={!isEnable}
                  checked={isSelected}
                  onChange={() => handleAccountSelect(account, isSelected)}
                />
              )}
              <AccountAvatar account={account} />
              <div className="text-sm font-medium">{account.platformAccountName || '未知名称'}</div>
            </div>
            {!isEnable && <Badge variant="secondary" className="text-xs">{getAccountDisabledReason(account)}</Badge>}
          </label>
        )
      })}
    </div>
  )
}
