
import { getPlatformByName } from '@/lib/platform'
import type { PlatformAccount } from '@/types'
import type { HTMLAttributes } from 'react'
import { cn } from '@/lib/utils'
import { AvatarBase } from '../Avater'

type AccountAvatarProps = {
  account: PlatformAccount
  className?: string
  size?: 'small' | 'medium' | 'large'
} & HTMLAttributes<HTMLDivElement>

export const AccountAvatar: React.FC<AccountAvatarProps> = ({
  account,
  className = '',
  size = 'medium',
}) => {
  const platformInfo = getPlatformByName(account.platformName, !!account.parentId )

  return (
    <div className={`relative ${className}`}>
      <AvatarBase
        src={account.platformAvatar}
        alt={account.platformAccountName}
        className={cn('h-6 w-6 rounded-full', {
          'h-10 w-10': size === 'medium',
          'h-16 w-16': size === 'large',
        })}
      />
      <img
        src={platformInfo.icon}
        alt={account.platformName}
        className={cn(
          'border-1 absolute -bottom-0.5 -right-0.5 rounded-full border-background bg-background object-contain',
          {
            'h-3 w-3': size === 'small',
            'h-5 w-5': size === 'medium',
            'h-7 w-7': size === 'large',
          },
        )}
      />
    </div>
  )
}
