import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import type { Platform } from '@/lib/platform'
import { getGroupList } from '@/lib/http'
import { Button } from '../ui/button'
import { Check, ChevronDown } from 'lucide-react'
import { Drawer, DrawerContent, DrawerTrigger } from '../ui/drawer'
import { AvatarBase } from '../Avater'
import allPlatform from '@/assets/images/platforms/allAccounts.svg'

interface FilterValue {
  platform?: string
  group?: string
}

interface AccountFiltersProps {
  platforms: Platform[]
  value: FilterValue
  onChange: (value: FilterValue) => void
  isLoadingPlatforms?: boolean
  isErrorPlatforms?: boolean
}

interface SelectItemProps {
  id: string
  name: string
  icon?: string
  isSelected: boolean
  onClick: () => void
}

const SelectItem: React.FC<SelectItemProps> = ({ name, icon, isSelected, onClick }) => (
  <div onClick={onClick} className="flex h-14 cursor-pointer items-center justify-between gap-2">
    <div className="flex flex-1 items-center gap-2">
      {icon && <AvatarBase src={icon} className="size-9" alt={name} />}
      <div className="flex flex-1 overflow-hidden">
        <span className="w-0 flex-1 truncate text-nowrap text-base font-medium">{name}</span>
      </div>
    </div>
    {isSelected && (
      <div className="flex items-center gap-2">
        <Check />
      </div>
    )}
  </div>
)

export const AccountFilters: React.FC<AccountFiltersProps> = ({ platforms, value, onChange }) => {
  const [platformOpen, setPlatformOpen] = useState(false)
  const [groupOpen, setGroupOpen] = useState(false)

  const { data: groupData } = useQuery({
    queryKey: ['groups'],
    queryFn: () => getGroupList(),
  })

  const groups = groupData?.data ?? []

  const handleChange = (key: keyof FilterValue, val: string) => {
    const newValue = { ...value }
    if (val === 'all') {
      delete newValue[key]
    } else {
      newValue[key] = val
    }
    onChange(newValue)
  }

  const handleSelect = (key: keyof FilterValue, val: string, setOpen: (open: boolean) => void) => {
    handleChange(key, val)
    setOpen(false)
  }

  const renderPlatformDrawer = () => (
    <Drawer open={platformOpen} onOpenChange={setPlatformOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" className="flex-1 justify-between rounded-full">
          <span className="truncate">{value.platform ? value.platform : '选择平台'}</span>
          <ChevronDown />
        </Button>
      </DrawerTrigger>
      <DrawerContent className="flex max-h-[90vh] flex-col overflow-hidden">
        <div className="flex-1 overflow-y-auto">
          <div className="px-4  pb-safe">
            <SelectItem
              id="all"
              name="全部平台"
              icon={allPlatform}
              isSelected={!value.platform}
              onClick={() => handleSelect('platform', 'all', setPlatformOpen)}
            />
            {platforms.map((item) => (
              <SelectItem
                key={item.key}
                id={item.key}
                name={item.name}
                icon={item.icon}
                isSelected={value.platform === item.key}
                onClick={() => handleSelect('platform', item.name, setPlatformOpen)}
              />
            ))}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )

  const renderGroupDrawer = () => (
    <Drawer open={groupOpen} onOpenChange={setGroupOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" className="sh flex-1 justify-between rounded-full">
          <span className="truncate">
            {value.group ? groups.find((item) => item.id === value.group)?.name : '选择分组'}
          </span>
          <ChevronDown />
        </Button>
      </DrawerTrigger>
      <DrawerContent className="flex max-h-[90vh] overflow-hidden">
        <div className="flex-1 overflow-y-auto">
          <div className="px-4 pb-safe">
            <SelectItem
              id="all"
              name="全部分组"
              isSelected={!value.group}
              onClick={() => handleSelect('group', 'all', setGroupOpen)}
            />
            {groups.map((item) => (
              <SelectItem
                key={item.id}
                id={item.id}
                name={item.name}
                isSelected={value.group === item.id}
                onClick={() => handleSelect('group', item.id, setGroupOpen)}
              />
            ))}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )

  return (
    <div className="grid grid-cols-2 gap-4 bg-background px-4 py-2">
      {renderPlatformDrawer()}
      {renderGroupDrawer()}
    </div>
  )
}
