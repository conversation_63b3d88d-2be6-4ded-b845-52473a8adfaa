import { useState } from 'react'
import type { PlatformAccount } from '@/types'
import { AccountSelectorPopup } from './AccountSelectorPopup'
import { AccountAvatar } from './AccountAvatar'
import { Badge } from '../Badge'
import { Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import PlatformManage from '@app/pages/publish/video/platformManage'

interface AccountSelectorProps {
  selectedAccounts?: PlatformAccount[]
  onAccountsChange: (accounts: PlatformAccount[]) => void
  maxDisplayCount?: number
  mode?: 'single' | 'multiple'
  filter?: (item: PlatformAccount) => boolean
}

export const AccountSelector: React.FC<AccountSelectorProps> = ({
  selectedAccounts = [],
  onAccountsChange,
  maxDisplayCount = 4,
  mode = 'multiple',
  filter,
}) => {
  const [isPopupOpen, setIsPopupOpen] = useState(false)

  const handleAccountDelete = (accountId: string) => {
    onAccountsChange(selectedAccounts.filter((acc) => acc.id !== accountId))
  }

  const handleAccountsSelect = (accounts: PlatformAccount[]) => {
    onAccountsChange(accounts)
    setIsPopupOpen(false)
  }

  const openSelector = () => {
    setIsPopupOpen(true)
  }

  const closeSelector = () => {
    setIsPopupOpen(false)
  }

  // 显示部分账号，其余用 +N 表示
  const displayAccounts = selectedAccounts.slice(0, maxDisplayCount)
  const remainingCount = selectedAccounts.length - displayAccounts.length

  return (
    <div>
      <div className="flex flex-wrap items-center gap-2">
        {displayAccounts.map((account) => (
          <Badge key={account.id} onDelete={() => handleAccountDelete(account.id)}>
            <>
              <AccountAvatar account={account} slot="media" size="small" />
              <span className="text-sm">{account.platformAccountName}</span>
            </>
          </Badge>
        ))}
        {remainingCount > 0 && (
          <PlatformManage
            key="platformManage"
            platforms={selectedAccounts}
            onChange={(platforms) => {
              onAccountsChange(platforms)
            }}
          >
            <Badge>{`+${remainingCount}`}</Badge>
          </PlatformManage>
        )}
        <Button key="openSelector" variant="secondary" className="h-9 px-2 text-muted-foreground" onClick={openSelector}>
          <Plus className="h-4 w-4" />
          选择账号
        </Button>
      </div>

     <AccountSelectorPopup
        mode={mode}
        isOpen={isPopupOpen}
        onClose={closeSelector}
        selectedAccounts={selectedAccounts}
        onSelect={handleAccountsSelect}
        filter={filter}
      />
    </div>
  )
}
