import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
} from '@/components/ui/alert-dialog'
import { forwardRef, useImperativeHandle, useRef, useState } from 'react'
import { Input } from '@/components/ui/input'

export type AlertBaseHandle = {
  open: (options: {
    title: string
    description?: string
    okText?: string
    cancelText?: string
    buttons?: {
      text: string
      onClick: () => void
    }[]
    type?: 'confirm' | 'prompt'
    onEscapeKeyDown?: (e: KeyboardEvent) => void
    //
    onSubmit?: (inputValue?: string) => void
    onCancel?: () => void
  }) => void
}

export const AlertBase = forwardRef<AlertBaseHandle, NonNullable<unknown>>((_, ref) => {
  const [open, setOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const options = useRef<Parameters<AlertBaseHandle['open']>[0]>(undefined)

  useImperativeHandle(ref, () => ({
    open(option) {
      options.current = option
      setOpen(true)
    },
  }))

  AlertBase.displayName = 'AlertBase'

  const type = options.current?.type ?? 'confirm'

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent
        onEscapeKeyDown={options.current?.onEscapeKeyDown}
        className="w-10/12 rounded-lg"
      >
        <AlertDialogHeader className="text-left">
          <AlertDialogTitle className="text-base font-medium">
            {options.current?.title}
          </AlertDialogTitle>
          {options.current?.description && (
            <AlertDialogDescription>{options.current?.description}</AlertDialogDescription>
          )}
        </AlertDialogHeader>
        {type === 'prompt' && (
          <Input
            autoFocus
            className="w-full"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
          />
        )}
        <AlertDialogFooter className="flex-row justify-end space-x-2">
          {options.current?.buttons ? (
            options.current.buttons.map((button, index) => (
              <AlertDialogCancel
                key={index}
                onClick={() => {
                  button.onClick()
                }}
              >
                {button.text}
              </AlertDialogCancel>
            ))
          ) : (
            <AlertDialogCancel onClick={options.current?.onCancel}>
              {options.current?.cancelText ?? '取消'}
            </AlertDialogCancel>
          )}
          <AlertDialogAction
            onClick={() => {
              if (type === 'prompt') {
                options.current?.onSubmit?.(inputValue)
                setInputValue('')
              } else {
                options.current?.onSubmit?.()
              }
            }}
          >
            {options.current?.okText ?? '确定'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})
