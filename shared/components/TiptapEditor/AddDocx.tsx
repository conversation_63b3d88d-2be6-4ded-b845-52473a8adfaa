import { useState } from 'react'
import { FileText, Loader2 } from 'lucide-react'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { FilePicker } from '@renderer/components/FilePicker'
import { toast } from 'sonner'
import mammoth from 'mammoth'
import type { EditorType } from './TiptapMenuBar'
import Tooltip from '@renderer/components/tooltip'
import { LinkImport } from '@renderer/components/LinkImport'

export function AddDocx({
  addDocxContent,
  type: _type,
  setTitle,
}: {
  addDocxContent: (htmlContent: string) => void
  type?: EditorType
  setTitle: (title: string) => void
}) {
  const [isLoading, setIsLoading] = useState(false)

  const handleDocxFile = async (files: File[]) => {
    if (files.length === 0) return

    const file = files[0]

    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.docx')) {
      toast.error('请选择有效的DOCX文件')
      return
    }

    setIsLoading(true)

    try {
      // 将文件转换为ArrayBuffer
      const arrayBuffer = await file.arrayBuffer()

      // 使用mammoth转换DOCX为HTML
      const result = await mammoth.convertToHtml({ arrayBuffer })

      if (result.value) {
        // 插入转换后的HTML内容到编辑器
        addDocxContent(result.value)
        toast.success('DOCX文件导入成功')

        // 如果有警告信息，显示给用户
        if (result.messages.length > 0) {
          const warnings = result.messages
            .filter((msg) => msg.type === 'warning')
            .map((msg) => msg.message)

          if (warnings.length > 0) {
            console.warn('DOCX导入警告:', warnings)
            toast.warning(`导入成功，但有${warnings.length}个格式警告`)
          }
        }
      } else {
        toast.error('DOCX文件内容为空或无法解析')
      }
    } catch (error) {
      console.error('DOCX导入失败:', error)
      toast.error('DOCX文件导入失败，请检查文件格式')
    } finally {
      setIsLoading(false)
    }
  }
  const setContent = (result: { title: string; content: string }) => {
    console.log('导入成功', result)
    setTitle(result.title)
    addDocxContent(result.content)
  }
  return (
    <DropdownMenu>
      <Tooltip tooltip="导入DOCX">
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-7 w-7 p-0" size="icon" disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <FileText className="h-5 w-5" />
            )}
          </Button>
        </DropdownMenuTrigger>
      </Tooltip>
      <DropdownMenuContent>
        <DropdownMenuLabel>导入DOCX</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <LinkImport importResult={setContent}></LinkImport>
        <FilePicker
          accept=".docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          onChange={handleDocxFile}
        >
          <Button
            className="h-auto w-full justify-start px-2 py-1.5 text-left text-sm font-normal"
            variant="ghost"
            disabled={isLoading}
          >
            选择DOCX文件
          </Button>
        </FilePicker>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
