import type { Editor } from '@tiptap/react'
import { useCallback } from 'react'
import {
  BoldIcon,
  CodeIcon,
  ItalicIcon,
  ListIcon,
  ListOrderedIcon,
  Heading1,
  Heading2,
  Heading3,
  Heading4,
  Heading5,
  Heading6,
  Braces,
  Quote,
  Undo2,
  Redo2,
  Strikethrough,
  Pilcrow,
  ChevronDown,
  Baseline,
  AlignCenter,
  AlignLeft,
  AlignRight,
  AlignJustify,
} from 'lucide-react'
import { EditorToggle } from './EditorToggle'
import { AddImage } from './AddImage'
import { Separator } from '@renderer/shadcn-components/ui/separator'
import { AddVideo } from './AddVideo'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Input } from '@renderer/shadcn-components/ui/input'
import Tooltip from '@renderer/components/tooltip'
import { cn } from '@renderer/lib/utils'

const textMenuItems = [
  { name: 'Text', icon: Pilcrow },
  { name: 'Heading 1', icon: Heading1 },
  { name: 'Heading 2', icon: Heading2 },
  { name: 'Heading 3', icon: Heading3 },
  { name: 'Heading 4', icon: Heading4 },
  { name: 'Heading 5', icon: Heading5 },
  { name: 'Heading 6', icon: Heading6 },
]

const alignmentMenuItems = [
  { name: 'left', icon: AlignLeft },
  { name: 'center', icon: AlignCenter },
  { name: 'right', icon: AlignRight },
  { name: 'justify', icon: AlignJustify },
]

export type EditorType = 'article' | 'wechatOA'

export function MenuBar({
  type,
  editor,
  setTitle,
}: {
  type?: EditorType
  editor: Editor | null
  setTitle?: (title: string) => void
}) {
  const addImage = useCallback(
    (url: string) => {
      if (url) {
        editor?.chain().focus().setImage({ src: url }).createParagraphNear().run()
      }
    },
    [editor],
  )

  const addVideo = useCallback(
    (url: string) => {
      if (url) {
        editor?.chain().focus().setVideo(url).createParagraphNear().run()
      }
    },
    [editor],
  )

  // const addDocxContent = useCallback(
  //   (htmlContent: string) => {
  //     if (htmlContent && editor) {
  //       editor.chain().focus().insertContent(htmlContent).run()
  //     }
  //   },
  //   [editor],
  // )

  if (!editor) {
    return null
  }

  const headingLevel = textMenuItems.findIndex((_item, index) =>
    editor.isActive('heading', { level: index }),
  )

  const AlignmentIcon =
    alignmentMenuItems.find((item) => editor.isActive({ textAlign: item.name }))?.icon ?? AlignLeft

  return (
    <div
      className={cn('sticky top-0 z-10 flex flex-wrap items-center gap-2 bg-background p-4', {
        'bg-white': type === 'wechatOA',
      })}
    >
      {/* 返回前进 */}
      <EditorToggle
        tooltip="返回"
        icon={Undo2}
        isActive={false}
        onClick={() => editor.chain().undo().run()}
        disabled={!editor.can().chain().undo().run()}
      />

      <EditorToggle
        tooltip="前进"
        icon={Redo2}
        isActive={false}
        onClick={() => editor.chain().redo().run()}
        disabled={!editor.can().chain().redo().run()}
      />
      <Separator orientation="vertical" className="h-5" />

      <EditorToggle
        tooltip="加粗"
        icon={BoldIcon}
        isActive={editor.isActive('bold')}
        onClick={() => editor.chain().focus().toggleBold().run()}
        disabled={!editor.can().chain().focus().toggleBold().run()}
      />

      <EditorToggle
        tooltip="斜体"
        icon={ItalicIcon}
        isActive={editor.isActive('italic')}
        onClick={() => editor.chain().focus().toggleItalic().run()}
        disabled={!editor.can().chain().focus().toggleItalic().run()}
      />

      <EditorToggle
        tooltip="删除线"
        icon={Strikethrough}
        isActive={editor.isActive('strike')}
        onClick={() => editor.chain().focus().toggleStrike().run()}
        disabled={!editor.can().chain().focus().toggleStrike().run()}
      />

      <EditorToggle
        tooltip="代码"
        icon={CodeIcon}
        isActive={editor.isActive('code')}
        onClick={() => editor.chain().focus().toggleCode().run()}
        disabled={!editor.can().chain().focus().toggleCode().run()}
      />
      <Button
        className="relative h-7 min-w-7 select-none gap-1 p-1 hover:bg-[#EDEFF1]"
        variant="ghost"
      >
        <Input
          type="color"
          onInput={(event) => editor.chain().focus().setColor(event.currentTarget.value).run()}
          value={editor.getAttributes('textStyle').color || '#000000'}
          data-testid="setColor"
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
        />
        <Baseline className="h-4 w-4" />
      </Button>
      <Separator orientation="vertical" className="h-5" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="h-7 min-w-7 select-none gap-1 p-1 hover:bg-[#EDEFF1]" variant="ghost">
            {textMenuItems.find((_item, index) => editor.isActive('heading', { level: index }))
              ?.name || 'Text'}
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuRadioGroup
            value={headingLevel >= 0 ? headingLevel.toString() : '0'}
            onValueChange={(value) => {
              if (value !== '0') {
                editor
                  .chain()
                  .focus()
                  .toggleHeading({ level: parseInt(value) as 1 | 2 | 3 | 4 | 5 | 6 })
                  .run()
              } else {
                editor.chain().focus().setParagraph().run()
              }
            }}
          >
            {textMenuItems.map((level, index) => {
              const Icon = level.icon
              return (
                <DropdownMenuRadioItem
                  className="flex gap-2"
                  key={level.name}
                  value={index.toString()}
                >
                  <Icon className="h-4 w-4" />
                  <span>{level.name}</span>
                </DropdownMenuRadioItem>
              )
            })}
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <Separator orientation="vertical" className="h-5" />
      <DropdownMenu>
        <Tooltip tooltip="Align">
          <DropdownMenuTrigger asChild>
            <Button
              className="h-7 min-w-7 select-none gap-1 p-1 hover:bg-[#EDEFF1]"
              variant="ghost"
            >
              <AlignmentIcon className="h-4 w-4" />
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
        </Tooltip>
        <DropdownMenuContent className="min-w-0" onCloseAutoFocus={(e) => e.preventDefault()}>
          {alignmentMenuItems.map((item) => {
            const Icon = item.icon
            return (
              <DropdownMenuItem
                onClick={() => {
                  editor.chain().focus().setTextAlign(item.name).run()
                }}
                className="flex gap-2"
                key={item.name}
              >
                <Icon className="h-4 w-4" />
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
      <EditorToggle
        tooltip="无序列表"
        icon={ListIcon}
        isActive={editor.isActive('bulletList')}
        onClick={() => editor.chain().focus().toggleBulletList().run()}
      />

      <EditorToggle
        tooltip="有序列表"
        icon={ListOrderedIcon}
        isActive={editor.isActive('orderedList')}
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
      />

      <EditorToggle
        tooltip="代码块"
        icon={Braces}
        isActive={editor.isActive('codeBlock')}
        onClick={() => editor.chain().focus().toggleCodeBlock().run()}
      />

      <EditorToggle
        tooltip="引用"
        icon={Quote}
        isActive={editor.isActive('blockquote')}
        onClick={() => editor.chain().focus().toggleBlockquote().run()}
      />
      <Separator orientation="vertical" className="h-5" />

      <AddImage addImage={addImage} type={type}></AddImage>
      <AddVideo addVideo={addVideo} type={type}></AddVideo>
      {/* <AddDocx
        setTitle={(title) => {
          setTitle && setTitle(title)
        }}
        addDocxContent={addDocxContent}
        type={type}
      ></AddDocx> */}
      {/* <AddMiniProgram /> */}
    </div>
  )
}
