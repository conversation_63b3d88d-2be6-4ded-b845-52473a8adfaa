import type { Editor } from '@tiptap/core'
import { BubbleMenu } from '@tiptap/react'
import { Menubar, MenubarMenu } from '@renderer/shadcn-components/ui/menubar'
import { Button } from '@renderer/shadcn-components/ui/button'
import { useRef, useState } from 'react'
import { AddImage } from './AddImage'
import { ToggleGroup, ToggleGroupItem } from '@renderer/shadcn-components/ui/toggle-group'
import {
  AlignHorizontalDistributeCenter,
  AlignHorizontalDistributeEnd,
  AlignHorizontalDistributeStart,
} from 'lucide-react'
import { FreeCropperDialog } from '@renderer/components/freeCropperDialog'
import { uiEvents } from '@common/events/ui-events'
import { localPath2Url } from '@common/protocol'
import { useAssetLibraryService } from '@renderer/infrastructure/services/application-service/assetLibrary-service'

export function ImageMenubarComponent({
  editor,
  type,
}: {
  editor: Editor
  type: React.ComponentProps<typeof AddImage>['type']
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const imgSrc = useRef('')
  const align = editor.getAttributes('comstomImage').align || 'left'

  const assetLibraryService = useAssetLibraryService()
  // console.log('align', align)
  return (
    <>
      <BubbleMenu
        editor={editor}
        shouldShow={({ editor }) => editor.isActive('comstomImage')}
        tippyOptions={{
          duration: 100,
          zIndex: 10,
        }}
      >
        <Menubar className="relative">
          <ToggleGroup
            type="single"
            value={align}
            onValueChange={(value) => {
              console.log('value', value)
              editor.chain().focus().setImageAlign({ align: value }).run()
            }}
            aria-label="Text alignment"
            className="mr-2"
          >
            <ToggleGroupItem value="left" className="h-7 w-7 rounded-sm">
              <AlignHorizontalDistributeStart className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="center" className="h-7 w-7 rounded-sm">
              <AlignHorizontalDistributeCenter className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="right" className="h-7 w-7 rounded-sm">
              <AlignHorizontalDistributeEnd className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
          <MenubarMenu>
            <Button
              variant="ghost"
              onClick={() => {
                imgSrc.current = editor.getAttributes('comstomImage').src
                setIsDialogOpen(true)
              }}
              className="h-auto rounded-sm px-3 py-1 text-sm outline-none"
            >
              裁剪
            </Button>
          </MenubarMenu>
          <MenubarMenu>
            <AddImage
              addImage={(url: string) => editor.chain().focus().setImageSrc({ src: url }).run()}
              type={type}
              isHover={true}
            >
              <Button variant="ghost" className="h-auto rounded-sm px-3 py-1 text-sm outline-none">
                替换
              </Button>
            </AddImage>
          </MenubarMenu>

          <MenubarMenu>
            <Button
              variant="ghost"
              onClick={() => editor.commands.deleteSelection()}
              className="h-auto rounded-sm px-3 py-1 text-sm outline-none"
            >
              删除
            </Button>
          </MenubarMenu>
        </Menubar>
      </BubbleMenu>
      <FreeCropperDialog
        title="裁剪图片"
        imageSrc={imgSrc.current}
        setIsDialogOpen={setIsDialogOpen}
        isDialogOpen={isDialogOpen}
        submit={async (blob) => {
          const arrayBuffer = new Uint8Array(await blob.arrayBuffer())
          let filePath = ''
          if (type === 'wechatOA') {
            const imageInfo = await assetLibraryService.uploadImageWechat('cover.jpg', arrayBuffer)
            filePath = imageInfo.url
          } else {
            filePath = await window.api.invoke(uiEvents.saveImageFile, arrayBuffer, 'png')
            filePath = localPath2Url(filePath)
          }

          if (filePath) {
            editor.chain().focus().setImageSrc({ src: filePath }).run()
          }
        }}
      />
    </>
  )
}
