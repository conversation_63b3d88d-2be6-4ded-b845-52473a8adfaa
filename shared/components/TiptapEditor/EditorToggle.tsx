import { Toggle } from '@renderer/shadcn-components/ui/toggle'
import { cn } from '@renderer/lib/utils'
import type { LucideIcon } from 'lucide-react'
import Tooltip from '@renderer/components/tooltip'

interface EditorToggleProps {
  icon: LucideIcon
  isActive: boolean
  onClick?: () => void
  disabled?: boolean
  className?: string
  tooltip: string
}

export function EditorToggle({
  icon: Icon,
  isActive,
  onClick,
  disabled,
  className,
  tooltip,
}: EditorToggleProps) {
  return (
    <Tooltip tooltip={tooltip}>
      <Toggle
        pressed={isActive}
        onPressedChange={onClick}
        disabled={disabled}
        className={cn('h-7 w-7 min-w-7 p-0 hover:bg-[#EDEFF1]', className)}
      >
        <Icon className="h-5 w-5" strokeWidth={2} />
      </Toggle>
    </Tooltip>
  )
}
