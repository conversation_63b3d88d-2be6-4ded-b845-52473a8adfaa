import { Color } from '@tiptap/extension-color'
import ListItem from '@tiptap/extension-list-item'
import TextStyle from '@tiptap/extension-text-style'
import type { Editor } from '@tiptap/react'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Document from '@tiptap/extension-document'
import Dropcursor from '@tiptap/extension-dropcursor'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import { LocalImage } from './extensions/image/localImage'
import FileHandler from '@tiptap-pro/extension-file-handler'
import Emoji, { gitHubEmojis } from '@tiptap-pro/extension-emoji'
import { MenuBar } from './TiptapMenuBar'
import './index.less'
import { Video } from './extensions/video'
import { Input } from '@renderer/shadcn-components/ui/input'
import { uiEvents } from '@common/events/ui-events'
import CharacterCount from '@tiptap/extension-character-count'
import { ImageMenubarComponent } from './ImageMenubarComponent'
import { localPath2Url } from '@common/protocol'
const extensions = [
  Color.configure({ types: [TextStyle.name, ListItem.name] }),
  TextStyle.configure(),
  StarterKit.configure({
    bulletList: {
      keepMarks: true,
      keepAttributes: false,
    },
    orderedList: {
      keepMarks: true,
      keepAttributes: false,
    },
  }),
  CharacterCount,
  Placeholder.configure({
    placeholder: '请输入正文',
  }),
  TextAlign.configure({
    types: ['heading', 'paragraph'],
    defaultAlignment: 'left',
  }),
  Document,
  Dropcursor,
  LocalImage.configure({
    inline: true,
    allowBase64: true,
    HTMLAttributes: {
      class: 'tiptap-image',
    },
  }),
  FileHandler.configure({
    allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
    onDrop: (currentEditor, files, pos) => {
      files.forEach((file) => {
        currentEditor
          .chain()
          .focus()
          .insertContentAt(pos, {
            type: 'image',
            attrs: {
              src: localPath2Url(file.path),
            },
          })
          .focus()
          .run()
      })
    },
    onPaste: (currentEditor, files, htmlContent) => {
      if (htmlContent) {
        console.log(htmlContent)
        return
      }
      files.forEach(async (file) => {
        let imageUrl = file.path
        if (!file.path) {
          // file对象转为arrayBuffer
          const arrayBuffer = await file.arrayBuffer()
          imageUrl = await window.api.invoke(uiEvents.saveImageFile, arrayBuffer)
        }
        currentEditor
          .chain()
          .focus()
          .insertContentAt(currentEditor.state.selection.anchor, {
            type: 'image',
            attrs: {
              src: localPath2Url(imageUrl),
            },
          })
          .focus()
          .run()
        return true
      })
    },
  }),
  Emoji.configure({
    emojis: gitHubEmojis,
  }),
  Video,
]

type TiptapEditorProps = {
  value: string
  onChange: (value: string) => void
  onTextLengthChange: (length: number) => void
  title: string
  setTitle: (title: string) => void
  editorRef?: React.MutableRefObject<Editor | null>
}

export default function ArticleEditor({
  value,
  onChange,
  onTextLengthChange,
  title,
  setTitle,
  editorRef,
}: TiptapEditorProps) {
  const editor = useEditor({
    extensions,
    content: value,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onChange(html)
      onTextLengthChange(editor.storage.characterCount.characters())
    },
    onCreate: ({ editor }) => {
      if (editorRef) {
        editorRef.current = editor
      }
      //TODO 初始状态直接刷新字数会得到0，需要延迟一下，这里考虑用插件或者更好的方式解决
      setTimeout(() => {
        // 初始状态需要刷新一下字数
        onTextLengthChange(editor.storage.characterCount.characters())
      }, 1000)
    },
    editorProps: {
      attributes: {
        class:
          'max-h-[700px] w-[700px] m-auto min-h-[200px] overflow-y-auto prose prose-stone max-w-none prose-sm sm:prose-base pt-0 pb-4 px-[50px] focus:outline-none',
      },
    },
  })
  return (
    <div className="w-[700px] rounded-md bg-secondary">
      {editor && <ImageMenubarComponent editor={editor} type="article" />}
      <div>
        <MenuBar editor={editor} setTitle={setTitle} />
        <div className="m-auto h-[72px] w-[600px] flex-shrink-0 pb-4">
          <Input
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="请输入标题"
            className="h-full rounded-none border-0 border-b border-input bg-transparent px-0 py-4 text-2xl font-[600] text-[#222222] shadow-none placeholder:text-[#969499] focus-visible:outline-none focus-visible:ring-0 md:text-2xl"
          />
        </div>

        <EditorContent editor={editor} />
      </div>
    </div>
  )
}
