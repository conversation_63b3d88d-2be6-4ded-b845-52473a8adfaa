:host {
  all: initial;
  -webkit-text-size-adjust: inherit;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.8);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
    --weui-FG-1: hsla(0, 0%, 100%, 0.5);
    --weui-FG-2: hsla(0, 0%, 100%, 0.3);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  }
}
@media (prefers-color-scheme: dark) {
  .wx-root[data-weui-mode='care']:not([data-weui-theme='light']),
  body[data-weui-mode='care']:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.85);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
    --weui-FG-1: hsla(0, 0%, 100%, 0.55);
    --weui-FG-2: hsla(0, 0%, 100%, 0.35);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-FG: #fff;
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-BG: #000;
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  }
}
.wx-root,
body {
  --weui-BG-COLOR-ACTIVE: #ececec;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BG-COLOR-ACTIVE: #373737;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-COLOR-ACTIVE: #373737;
  }
}
.wx-root,
body,
page {
  --weui-BTN-HEIGHT: 48;
  --weui-BTN-HEIGHT-MEDIUM: 40;
  --weui-BTN-HEIGHT-SMALL: 32;
}
.wx-root,
body {
  --weui-BTN-ACTIVE-MASK: rgba(0, 0, 0, 0.1);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BTN-ACTIVE-MASK: hsla(0, 0%, 100%, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BTN-ACTIVE-MASK: hsla(0, 0%, 100%, 0.1);
  }
}
.wx-root,
body {
  --weui-BTN-DEFAULT-ACTIVE-BG: #e6e6e6;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, 0.126);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, 0.126);
  }
}
.wx-root,
body {
  --weui-DIALOG-LINE-COLOR: rgba(0, 0, 0, 0.1);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, 0.1);
  }
}
.weui-btn {
  position: relative;
  display: block;
  width: 184px;
  margin-left: auto;
  margin-right: auto;
  padding: 12px 24px;
  box-sizing: border-box;
  font-weight: 500;
  font-size: 17px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  line-height: 1.41176471;
  border-radius: 8px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.weui-btn:active:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--weui-BTN-ACTIVE-MASK);
  border-radius: 8px;
}
.weui-btn:active.weui-btn_disabled:before,
.weui-btn:active.weui-btn_loading:before,
.weui-btn:active[disabled]:before {
  display: none;
}
.weui-btn_block {
  width: auto;
}
.weui-btn_inline {
  display: inline-block;
}
.weui-btn_default {
  background-color: var(--weui-FG-5);
}
.weui-btn_default,
.weui-btn_default:not(.weui-btn_disabled):visited {
  color: var(--weui-FG-0);
}
.weui-btn_primary {
  background-color: var(--weui-BRAND);
}
.weui-btn_primary:not(.weui-btn_disabled):visited {
  color: #fff;
}
.weui-btn_warn {
  background-color: var(--weui-FG-5);
}
.weui-btn_warn,
.weui-btn_warn:not(.weui-btn_disabled):visited {
  color: var(--weui-RED);
}
.weui-btn_overlay {
  background-color: #fff;
}
.weui-btn_overlay,
.weui-btn_overlay:not(.weui-btn_disabled):visited {
  color: var(--weui-BRAND);
}
.weui-btn[disabled],
.weui-btn_disabled {
  color: var(--weui-FG-4);
  background-color: var(--weui-BG-1);
}
.weui-btn_loading .weui-loading {
  margin: -0.2em 8px 0 0;
}
.weui-btn_loading .weui-mask-loading {
  margin: -0.2em 8px 0 0;
  color: currentColor;
}
.weui-btn_loading .weui-primary-loading {
  margin: -0.2em 8px 0 0;
  vertical-align: middle;
  color: currentColor;
}
.weui-btn_loading .weui-primary-loading:before {
  content: '';
}
.weui-btn_loading.weui-btn_primary {
  color: var(--weui-WHITE);
}
.weui-btn_cell {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  font-size: 17px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  line-height: 1.41176471;
  padding: 16px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
  background-color: var(--weui-BG-5);
}
.weui-btn_cell + .weui-btn_cell {
  margin-top: 16px;
}
.weui-btn_cell:active {
  background-color: var(--weui-BG-COLOR-ACTIVE);
}
.weui-btn_cell__icon {
  display: inline-block;
  vertical-align: middle;
  width: 24px;
  height: 24px;
  margin: -0.2em 0.34em 0 0;
}
.weui-btn_cell-default {
  color: var(--weui-FG-0);
}
.weui-btn_cell-primary {
  color: var(--weui-LINK);
}
.weui-btn_cell-warn {
  color: var(--weui-RED);
}
.weui-bottom-fixed-opr-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.weui-bottom-fixed-opr-page__content {
  min-height: 0;
  flex: 1;
  padding-bottom: 80px;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.weui-bottom-fixed-opr-page__tool {
  padding: 16px calc(32px + env(safe-area-inset-right)) calc(24px + env(safe-area-inset-bottom))
    calc(32px + env(safe-area-inset-left));
  background: #fff;
  position: relative;
  z-index: 50;
}
.weui-bottom-fixed-opr-page__tool:before {
  content: '';
  height: 80px;
  background: linear-gradient(0deg, #fff, hsla(0, 0%, 100%, 0));
  position: absolute;
  bottom: calc(100% - 1px);
  left: 0;
  right: 0;
  transform: translateZ(0);
  pointer-events: none;
}
.wx-root[data-weui-theme='dark'] .weui-bottom-fixed-opr-page__tool,
body[data-weui-theme='dark'] .weui-bottom-fixed-opr-page__tool {
  background: #191919;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']) .weui-bottom-fixed-opr-page__tool,
  body:not([data-weui-theme='light']) .weui-bottom-fixed-opr-page__tool {
    background: #191919;
  }
}
.wx-root[data-weui-theme='dark'] .weui-bottom-fixed-opr-page__tool:before,
body[data-weui-theme='dark'] .weui-bottom-fixed-opr-page__tool:before {
  background: linear-gradient(0deg, #191919, rgba(25, 25, 25, 0));
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']) .weui-bottom-fixed-opr-page__tool:before,
  body:not([data-weui-theme='light']) .weui-bottom-fixed-opr-page__tool:before {
    background: linear-gradient(0deg, #191919, rgba(25, 25, 25, 0));
  }
}
.weui-bottom-fixed-opr-page__tips {
  margin-bottom: 24px;
  padding: 0 32px;
  text-align: center;
}
.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr {
  display: flex;
  align-items: center;
  justify-content: center;
}
.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn {
  width: 184px;
  padding-left: 16px;
  padding-right: 16px;
}
.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n + 2),
.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n + 2) + .weui-btn {
  margin: 0 8px;
  width: 136px;
}
.weui-bottom-fixed-opr-page
  .weui-bottom-fixed-opr
  .weui-btn:nth-last-child(n + 2)
  + .weui-btn:first-child,
.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n + 2):first-child {
  margin-left: 0;
}
.weui-bottom-fixed-opr-page
  .weui-bottom-fixed-opr
  .weui-btn:nth-last-child(n + 2)
  + .weui-btn:last-child,
.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n + 2):last-child {
  margin-right: 0;
}
.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr {
  flex-direction: column;
}
.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr .weui-btn:nth-last-child(n + 2),
.weui-bottom-fixed-opr-page_btn-wrap
  .weui-bottom-fixed-opr
  .weui-btn:nth-last-child(n + 2)
  + .weui-btn {
  width: 184px;
  margin: 16px 0 0;
}
.weui-bottom-fixed-opr-page_btn-wrap
  .weui-bottom-fixed-opr
  .weui-btn:nth-last-child(n + 2)
  + .weui-btn:first-child,
.weui-bottom-fixed-opr-page_btn-wrap
  .weui-bottom-fixed-opr
  .weui-btn:nth-last-child(n + 2):first-child {
  margin-top: 0;
}
.weui-bottom-fixed-opr-page.weui-form {
  padding-top: 0;
}
.weui-bottom-fixed-opr-page.weui-form .weui-form__bd {
  padding-top: calc(56px + env(safe-area-inset-top));
}
.weui-bottom-fixed-opr-page.weui-form .weui-form__ft {
  padding-bottom: 0;
}
.weui-bottom-fixed-opr-page.weui-form .weui-form__control-area {
  margin-bottom: 0;
}
.weui-bottom-fixed-opr-page.weui-half-screen-dialog {
  padding: 0;
}
.weui-bottom-fixed-opr-page.weui-half-screen-dialog .weui-half-screen-dialog__bd,
.weui-bottom-fixed-opr-page.weui-half-screen-dialog .weui-half-screen-dialog__ft,
.weui-bottom-fixed-opr-page.weui-half-screen-dialog .weui-half-screen-dialog__hd {
  padding-left: calc(24px + env(safe-area-inset-left));
  padding-right: calc(24px + env(safe-area-inset-right));
}
.weui-bottom-fixed-opr-page.weui-half-screen-dialog .weui-half-screen-dialog__bd {
  padding-bottom: 80px;
}
.weui-bottom-fixed-opr-page.weui-half-screen-dialog .weui-half-screen-dialog__ft {
  padding-bottom: calc(64px + env(safe-area-inset-bottom));
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog {
  padding: 0;
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog .weui-half-screen-dialog__hd {
  padding: 0 calc(24px + env(safe-area-inset-right)) 0 calc(24px + env(safe-area-inset-left));
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog .weui-half-screen-dialog__bd {
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog .weui-half-screen-dialog__ft {
  padding: 0;
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog .weui-bottom-fixed-opr-page {
  flex: 1;
  min-height: 0;
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog .weui-bottom-fixed-opr-page__content {
  padding: 0 calc(24px + env(safe-area-inset-right)) 0 calc(24px + env(safe-area-inset-left));
}
.weui-half-screen-dialog_bottom-fixed.weui-half-screen-dialog .weui-bottom-fixed-opr {
  padding: 16px 0 calc(64px + env(safe-area-inset-bottom));
}
button.weui-btn,
input.weui-btn {
  border-width: 0;
  outline: 0;
  -webkit-appearance: none;
}
button.weui-btn:focus,
input.weui-btn:focus {
  outline: 0;
}
button.weui-btn_inline,
button.weui-btn_mini,
input.weui-btn_inline,
input.weui-btn_mini {
  width: auto;
}
.weui-btn_medium {
  font-size: 14px;
  padding: 10px 24px;
  line-height: calc(var(--weui-BTN-HEIGHT-MEDIUM) / 14 - 1.42857);
}
.weui-btn_mini {
  padding: 6px 12px;
  border-radius: 6px;
}
.weui-btn_mini,
.weui-btn_xmini {
  display: inline-block;
  width: auto;
  line-height: 1.42857;
  font-size: 14px;
}
.weui-btn_xmini {
  padding: 4px 12px;
  font-weight: 500;
  border-radius: 4px;
}
.weui-btn + .weui-btn {
  margin-top: 16px;
}
.weui-btn.weui-btn_mini + .weui-btn.weui-btn_mini,
.weui-btn.weui-btn_xmini + .weui-btn.weui-btn_xmini {
  margin-top: auto;
}
.weui-btn.weui-btn_inline + .weui-btn.weui-btn_inline {
  margin-left: 16px;
}
.weui-btn-area {
  margin: 48px 16px 8px;
}
.weui-btn-area_inline {
  display: flex;
}
.weui-btn-area_inline .weui-btn {
  margin-top: auto;
  margin-right: 16px;
  width: 100%;
  flex: 1;
}
.weui-btn-area_inline .weui-btn:last-child {
  margin-right: 0;
}
.weui-btn_reset {
  font-size: inherit;
}
.weui-btn_icon,
.weui-btn_reset {
  background: transparent;
  border: 0;
  padding: 0;
  outline: 0;
}
.weui-btn_icon {
  font-size: 0;
}
.weui-btn_icon:active [class*='weui-icon-'] {
  color: var(--weui-FG-1);
}
.weui-flex {
  display: flex;
}
.weui-flex__item {
  flex: 1;
}
.weui-hidden_abs {
  opacity: 0;
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.weui-a11y_ref {
  display: none;
}
.weui-hidden-space:empty:before {
  content: '\00A0';
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.weui-a11y-combo {
  position: relative;
}
.weui-a11y-combo__helper {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.weui-a11y-combo__content {
  position: relative;
  z-index: 1;
}
.weui-wa-hotarea-el {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 44px;
  min-height: 44px;
  width: 100%;
  height: 100%;
}
.weui-wa-hotarea,
.weui-wa-hotarea-el__wrp,
.weui-wa-hotarea_before {
  position: relative;
}
.weui-wa-hotarea-el__wrp a,
.weui-wa-hotarea-el__wrp button,
.weui-wa-hotarea-el__wrp navigator,
.weui-wa-hotarea_before a,
.weui-wa-hotarea_before button,
.weui-wa-hotarea_before navigator,
.weui-wa-hotarea a,
.weui-wa-hotarea button,
.weui-wa-hotarea navigator {
  position: relative;
  z-index: 1;
}
.weui-wa-hotarea:after,
.weui-wa-hotarea_before:before {
  content: '';
  pointer-events: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 44px;
  min-height: 44px;
  width: 100%;
  height: 100%;
}
.wx-root,
body {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #3fbeff;
  --weui-BLUE-170: #b7e6ff;
  --weui-BLUE-80: #0c8bcc;
  --weui-BLUE-90: #0e9ce6;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #5aafe4;
  --weui-BLUE-BG-130: #7fc0ea;
  --weui-BLUE-BG-90: #4095cb;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #38cd7f;
  --weui-BRAND-170: #b4ecce;
  --weui-BRAND-80: #059a4c;
  --weui-BRAND-90: #06ae56;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #3eb575;
  --weui-BRAND-BG-130: #69c694;
  --weui-BRAND-BG-90: #259c5c;
  --weui-FG-0: rgba(0, 0, 0, 0.9);
  --weui-FG-0_5: rgba(0, 0, 0, 0.9);
  --weui-FG-1: rgba(0, 0, 0, 0.55);
  --weui-FG-2: rgba(0, 0, 0, 0.3);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-FG-4: rgba(0, 0, 0, 0.15);
  --weui-GLYPH-0: rgba(0, 0, 0, 0.9);
  --weui-GLYPH-1: rgba(0, 0, 0, 0.55);
  --weui-GLYPH-2: rgba(0, 0, 0, 0.3);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #91d300;
  --weui-GREEN-120: #a7db33;
  --weui-GREEN-170: #def1b3;
  --weui-GREEN-80: #74a800;
  --weui-GREEN-90: #82bd00;
  --weui-GREEN-BG-100: #96be40;
  --weui-GREEN-BG-110: #a0c452;
  --weui-GREEN-BG-130: #b5d179;
  --weui-GREEN-BG-90: #86aa39;
  --weui-INDIGO-100: #1485ee;
  --weui-INDIGO-120: #439df1;
  --weui-INDIGO-170: #b8daf9;
  --weui-INDIGO-80: #106abe;
  --weui-INDIGO-90: #1277d6;
  --weui-INDIGO-BG-100: #2b77bf;
  --weui-INDIGO-BG-110: #3f84c5;
  --weui-INDIGO-BG-130: #6ba0d2;
  --weui-INDIGO-BG-90: #266aab;
  --weui-LIGHTGREEN-100: #95ec69;
  --weui-LIGHTGREEN-120: #aaef87;
  --weui-LIGHTGREEN-170: #def9d1;
  --weui-LIGHTGREEN-80: #77bc54;
  --weui-LIGHTGREEN-90: #85d35e;
  --weui-LIGHTGREEN-BG-100: #72cf60;
  --weui-LIGHTGREEN-BG-110: #80d370;
  --weui-LIGHTGREEN-BG-130: #9cdd90;
  --weui-LIGHTGREEN-BG-90: #66b956;
  --weui-LINK-100: #576b95;
  --weui-LINK-120: #7888aa;
  --weui-LINK-170: #ccd2de;
  --weui-LINK-80: #455577;
  --weui-LINK-90: #4e6085;
  --weui-LINKFINDER-100: #002666;
  --weui-MATERIAL-ATTACHMENTCOLUMN: hsla(0, 0%, 96.1%, 0.95);
  --weui-MATERIAL-NAVIGATIONBAR: hsla(0, 0%, 92.9%, 0.94);
  --weui-MATERIAL-REGULAR: hsla(0, 0%, 96.9%, 0.3);
  --weui-MATERIAL-THICK: hsla(0, 0%, 96.9%, 0.8);
  --weui-MATERIAL-THIN: hsla(0, 0%, 100%, 0.2);
  --weui-MATERIAL-TOOLBAR: hsla(0, 0%, 96.5%, 0.82);
  --weui-ORANGE-100: #fa9d3b;
  --weui-ORANGE-120: #fbb062;
  --weui-ORANGE-170: #fde1c3;
  --weui-ORANGE-80: #c87d2f;
  --weui-ORANGE-90: #e08c34;
  --weui-ORANGE-BG-100: #ea7800;
  --weui-ORANGE-BG-110: #ec8519;
  --weui-ORANGE-BG-130: #f0a04d;
  --weui-ORANGE-BG-90: #d26b00;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.5);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #6467f0;
  --weui-PURPLE-120: #8385f3;
  --weui-PURPLE-170: #d0d1fa;
  --weui-PURPLE-80: #5052c0;
  --weui-PURPLE-90: #595cd7;
  --weui-PURPLE-BG-100: #6769ba;
  --weui-PURPLE-BG-110: #7678c1;
  --weui-PURPLE-BG-130: #9496ce;
  --weui-PURPLE-BG-90: #5c5ea7;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #fb7373;
  --weui-RED-170: #fdcaca;
  --weui-RED-80: #c84040;
  --weui-RED-90: #e14949;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #d3625a;
  --weui-RED-BG-130: #dd847e;
  --weui-RED-BG-90: #b94840;
  --weui-SECONDARY-BG: rgba(0, 0, 0, 0.05);
  --weui-SEPARATOR-0: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-1: rgba(0, 0, 0, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: rgba(0, 0, 0, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: rgba(0, 0, 0, 0.2);
  --weui-YELLOW-100: #ffc300;
  --weui-YELLOW-120: #ffcf33;
  --weui-YELLOW-170: #ffecb2;
  --weui-YELLOW-80: #cc9c00;
  --weui-YELLOW-90: #e6af00;
  --weui-YELLOW-BG-100: #efb600;
  --weui-YELLOW-BG-110: #f0bd19;
  --weui-YELLOW-BG-130: #f3cc4d;
  --weui-YELLOW-BG-90: #d7a400;
  --weui-FG-HALF: rgba(0, 0, 0, 0.9);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #fa9d3b;
  --weui-YELLOW: #ffc300;
  --weui-GREEN: #91d300;
  --weui-LIGHTGREEN: #95ec69;
  --weui-TEXTGREEN: #06ae56;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1485ee;
  --weui-PURPLE: #6467f0;
  --weui-LINK: #576b95;
  --weui-TAG-TEXT-ORANGE: #fa9d3b;
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-TEXT-BLUE: #10aeff;
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
  --weui-WHITE: #fff;
  --weui-BG: #fff;
  --weui-FG: #000;
  --weui-FG-5: rgba(0, 0, 0, 0.05);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.8);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
    --weui-FG-1: hsla(0, 0%, 100%, 0.5);
    --weui-FG-2: hsla(0, 0%, 100%, 0.3);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  }
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BG-0: #111;
  --weui-BG-1: #1e1e1e;
  --weui-BG-2: #191919;
  --weui-BG-3: #202020;
  --weui-BG-4: #404040;
  --weui-BG-5: #2c2c2c;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #0c8bcc;
  --weui-BLUE-170: #04344d;
  --weui-BLUE-80: #3fbeff;
  --weui-BLUE-90: #28b6ff;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #4095cb;
  --weui-BLUE-BG-130: #32749e;
  --weui-BLUE-BG-90: #5aafe4;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #059a4c;
  --weui-BRAND-170: #023a1c;
  --weui-BRAND-80: #38cd7f;
  --weui-BRAND-90: #20c770;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #259c5c;
  --weui-BRAND-BG-130: #1d7a48;
  --weui-BRAND-BG-90: #3eb575;
  --weui-FG-0: hsla(0, 0%, 100%, 0.8);
  --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
  --weui-FG-1: hsla(0, 0%, 100%, 0.5);
  --weui-FG-2: hsla(0, 0%, 100%, 0.3);
  --weui-FG-3: hsla(0, 0%, 100%, 0.1);
  --weui-FG-4: hsla(0, 0%, 100%, 0.15);
  --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #74a800;
  --weui-GREEN-120: #5c8600;
  --weui-GREEN-170: #233200;
  --weui-GREEN-80: #8fb933;
  --weui-GREEN-90: #82b01a;
  --weui-GREEN-BG-100: #789833;
  --weui-GREEN-BG-110: #6b882d;
  --weui-GREEN-BG-130: #65802b;
  --weui-GREEN-BG-90: #85a247;
  --weui-INDIGO-100: #1196ff;
  --weui-INDIGO-120: #0d78cc;
  --weui-INDIGO-170: #052d4d;
  --weui-INDIGO-80: #40abff;
  --weui-INDIGO-90: #28a0ff;
  --weui-INDIGO-BG-100: #0d78cc;
  --weui-INDIGO-BG-110: #0b6bb7;
  --weui-INDIGO-BG-130: #09548f;
  --weui-INDIGO-BG-90: #2585d1;
  --weui-LIGHTGREEN-100: #3eb575;
  --weui-LIGHTGREEN-120: #31905d;
  --weui-LIGHTGREEN-170: #123522;
  --weui-LIGHTGREEN-80: #64c390;
  --weui-LIGHTGREEN-90: #51bc83;
  --weui-LIGHTGREEN-BG-100: #31905d;
  --weui-LIGHTGREEN-BG-110: #2c8153;
  --weui-LIGHTGREEN-BG-130: #226541;
  --weui-LIGHTGREEN-BG-90: #31905d;
  --weui-LINK-100: #7d90a9;
  --weui-LINK-120: #647387;
  --weui-LINK-170: #252a32;
  --weui-LINK-80: #97a6ba;
  --weui-LINK-90: #899ab1;
  --weui-LINKFINDER-100: #dee9ff;
  --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
  --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
  --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
  --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
  --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
  --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
  --weui-ORANGE-100: #c87d2f;
  --weui-ORANGE-120: #a06425;
  --weui-ORANGE-170: #3b250e;
  --weui-ORANGE-80: #d39758;
  --weui-ORANGE-90: #cd8943;
  --weui-ORANGE-BG-100: #bb6000;
  --weui-ORANGE-BG-110: #a85600;
  --weui-ORANGE-BG-130: #824300;
  --weui-ORANGE-BG-90: #c1701a;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.8);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #8183ff;
  --weui-PURPLE-120: #6768cc;
  --weui-PURPLE-170: #26274c;
  --weui-PURPLE-80: #9a9bff;
  --weui-PURPLE-90: #8d8fff;
  --weui-PURPLE-BG-100: #6768cc;
  --weui-PURPLE-BG-110: #5c5db7;
  --weui-PURPLE-BG-130: #48498f;
  --weui-PURPLE-BG-90: #7677d1;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #c84040;
  --weui-RED-170: #4b1818;
  --weui-RED-80: #fb7373;
  --weui-RED-90: #fa6262;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #ba4940;
  --weui-RED-BG-130: #913832;
  --weui-RED-BG-90: #d3625a;
  --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
  --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
  --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
  --weui-YELLOW-100: #cc9c00;
  --weui-YELLOW-120: #a37c00;
  --weui-YELLOW-170: #3d2f00;
  --weui-YELLOW-80: #d6af33;
  --weui-YELLOW-90: #d1a519;
  --weui-YELLOW-BG-100: #bf9100;
  --weui-YELLOW-BG-110: #ab8200;
  --weui-YELLOW-BG-130: #866500;
  --weui-YELLOW-BG-90: #c59c1a;
  --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #c87d2f;
  --weui-YELLOW: #cc9c00;
  --weui-GREEN: #74a800;
  --weui-LIGHTGREEN: #3eb575;
  --weui-TEXTGREEN: #259c5c;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1196ff;
  --weui-PURPLE: #8183ff;
  --weui-LINK: #7d90a9;
  --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
  --weui-WHITE: hsla(0, 0%, 100%, 0.8);
  --weui-FG: #fff;
  --weui-BG: #000;
  --weui-FG-5: hsla(0, 0%, 100%, 0.1);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
  --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
  --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
}
.wx-root[data-weui-mode='care'],
body[data-weui-mode='care'] {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-BLUE-100: #007dbb;
  --weui-BLUE-120: #3fbeff;
  --weui-BLUE-170: #b7e6ff;
  --weui-BLUE-80: #0c8bcc;
  --weui-BLUE-90: #0e9ce6;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #5aafe4;
  --weui-BLUE-BG-130: #7fc0ea;
  --weui-BLUE-BG-90: #4095cb;
  --weui-BRAND-100: #018942;
  --weui-BRAND-120: #38cd7f;
  --weui-BRAND-170: #b4ecce;
  --weui-BRAND-80: #059a4c;
  --weui-BRAND-90: #06ae56;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #3eb575;
  --weui-BRAND-BG-130: #69c694;
  --weui-BRAND-BG-90: #259c5c;
  --weui-FG-0: #000;
  --weui-FG-0_5: #000;
  --weui-FG-1: rgba(0, 0, 0, 0.6);
  --weui-FG-2: rgba(0, 0, 0, 0.42);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-FG-4: rgba(0, 0, 0, 0.15);
  --weui-GLYPH-0: #000;
  --weui-GLYPH-1: rgba(0, 0, 0, 0.6);
  --weui-GLYPH-2: rgba(0, 0, 0, 0.42);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #4f8400;
  --weui-GREEN-120: #a7db33;
  --weui-GREEN-170: #def1b3;
  --weui-GREEN-80: #74a800;
  --weui-GREEN-90: #82bd00;
  --weui-GREEN-BG-100: #96be40;
  --weui-GREEN-BG-110: #a0c452;
  --weui-GREEN-BG-130: #b5d179;
  --weui-GREEN-BG-90: #86aa39;
  --weui-INDIGO-100: #0075e2;
  --weui-INDIGO-120: #439df1;
  --weui-INDIGO-170: #b8daf9;
  --weui-INDIGO-80: #106abe;
  --weui-INDIGO-90: #1277d6;
  --weui-INDIGO-BG-100: #2b77bf;
  --weui-INDIGO-BG-110: #3f84c5;
  --weui-INDIGO-BG-130: #6ba0d2;
  --weui-INDIGO-BG-90: #266aab;
  --weui-LIGHTGREEN-100: #2e8800;
  --weui-LIGHTGREEN-120: #aaef87;
  --weui-LIGHTGREEN-170: #def9d1;
  --weui-LIGHTGREEN-80: #77bc54;
  --weui-LIGHTGREEN-90: #85d35e;
  --weui-LIGHTGREEN-BG-100: #72cf60;
  --weui-LIGHTGREEN-BG-110: #80d370;
  --weui-LIGHTGREEN-BG-130: #9cdd90;
  --weui-LIGHTGREEN-BG-90: #66b956;
  --weui-LINK-100: #576b95;
  --weui-LINK-120: #7888aa;
  --weui-LINK-170: #ccd2de;
  --weui-LINK-80: #455577;
  --weui-LINK-90: #4e6085;
  --weui-LINKFINDER-100: #002666;
  --weui-MATERIAL-ATTACHMENTCOLUMN: hsla(0, 0%, 96.1%, 0.95);
  --weui-MATERIAL-NAVIGATIONBAR: hsla(0, 0%, 92.9%, 0.94);
  --weui-MATERIAL-REGULAR: hsla(0, 0%, 96.9%, 0.3);
  --weui-MATERIAL-THICK: hsla(0, 0%, 96.9%, 0.8);
  --weui-MATERIAL-THIN: hsla(0, 0%, 100%, 0.2);
  --weui-MATERIAL-TOOLBAR: hsla(0, 0%, 96.5%, 0.82);
  --weui-ORANGE-100: #e17719;
  --weui-ORANGE-120: #fbb062;
  --weui-ORANGE-170: #fde1c3;
  --weui-ORANGE-80: #c87d2f;
  --weui-ORANGE-90: #e08c34;
  --weui-ORANGE-BG-100: #ea7800;
  --weui-ORANGE-BG-110: #ec8519;
  --weui-ORANGE-BG-130: #f0a04d;
  --weui-ORANGE-BG-90: #d26b00;
  --weui-ORANGERED-100: #d14730;
  --weui-OVERLAY: rgba(0, 0, 0, 0.5);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #6265f1;
  --weui-PURPLE-120: #8385f3;
  --weui-PURPLE-170: #d0d1fa;
  --weui-PURPLE-80: #5052c0;
  --weui-PURPLE-90: #595cd7;
  --weui-PURPLE-BG-100: #6769ba;
  --weui-PURPLE-BG-110: #7678c1;
  --weui-PURPLE-BG-130: #9496ce;
  --weui-PURPLE-BG-90: #5c5ea7;
  --weui-RED-100: #dc3636;
  --weui-RED-120: #fb7373;
  --weui-RED-170: #fdcaca;
  --weui-RED-80: #c84040;
  --weui-RED-90: #e14949;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #d3625a;
  --weui-RED-BG-130: #dd847e;
  --weui-RED-BG-90: #b94840;
  --weui-SECONDARY-BG: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-0: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-1: rgba(0, 0, 0, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: rgba(0, 0, 0, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: rgba(0, 0, 0, 0.2);
  --weui-YELLOW-100: #bb8e00;
  --weui-YELLOW-120: #ffcf33;
  --weui-YELLOW-170: #ffecb2;
  --weui-YELLOW-80: #cc9c00;
  --weui-YELLOW-90: #e6af00;
  --weui-YELLOW-BG-100: #efb600;
  --weui-YELLOW-BG-110: #f0bd19;
  --weui-YELLOW-BG-130: #f3cc4d;
  --weui-YELLOW-BG-90: #d7a400;
  --weui-FG-HALF: #000;
  --weui-RED: #dc3636;
  --weui-ORANGERED: #d14730;
  --weui-ORANGE: #e17719;
  --weui-YELLOW: #bb8e00;
  --weui-GREEN: #4f8400;
  --weui-LIGHTGREEN: #2e8800;
  --weui-TEXTGREEN: #06ae56;
  --weui-BRAND: #018942;
  --weui-BLUE: #007dbb;
  --weui-INDIGO: #0075e2;
  --weui-PURPLE: #6265f1;
  --weui-LINK: #576b95;
  --weui-TAG-TEXT-ORANGE: #e17719;
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-TEXT-BLUE: #007dbb;
  --weui-REDORANGE: #d14730;
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-WHITE: #fff;
  --weui-BG: #fff;
  --weui-FG: #000;
  --weui-FG-5: rgba(0, 0, 0, 0.05);
  --weui-TAG-BACKGROUND-ORANGE: rgba(225, 119, 25, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(0, 125, 187, 0.1);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
}
@media (prefers-color-scheme: dark) {
  .wx-root[data-weui-mode='care']:not([data-weui-theme='light']),
  body[data-weui-mode='care']:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.85);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
    --weui-FG-1: hsla(0, 0%, 100%, 0.55);
    --weui-FG-2: hsla(0, 0%, 100%, 0.35);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-FG: #fff;
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-BG: #000;
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  }
}
.wx-root[data-weui-mode='care'][data-weui-theme='dark'],
body[data-weui-mode='care'][data-weui-theme='dark'] {
  --weui-BG-0: #111;
  --weui-BG-1: #1e1e1e;
  --weui-BG-2: #191919;
  --weui-BG-3: #202020;
  --weui-BG-4: #404040;
  --weui-BG-5: #2c2c2c;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #0c8bcc;
  --weui-BLUE-170: #04344d;
  --weui-BLUE-80: #3fbeff;
  --weui-BLUE-90: #28b6ff;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #4095cb;
  --weui-BLUE-BG-130: #32749e;
  --weui-BLUE-BG-90: #5aafe4;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #059a4c;
  --weui-BRAND-170: #023a1c;
  --weui-BRAND-80: #38cd7f;
  --weui-BRAND-90: #20c770;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #259c5c;
  --weui-BRAND-BG-130: #1d7a48;
  --weui-BRAND-BG-90: #3eb575;
  --weui-FG-0: hsla(0, 0%, 100%, 0.85);
  --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
  --weui-FG-1: hsla(0, 0%, 100%, 0.55);
  --weui-FG-2: hsla(0, 0%, 100%, 0.35);
  --weui-FG-3: hsla(0, 0%, 100%, 0.1);
  --weui-FG-4: hsla(0, 0%, 100%, 0.15);
  --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #74a800;
  --weui-GREEN-120: #5c8600;
  --weui-GREEN-170: #233200;
  --weui-GREEN-80: #8fb933;
  --weui-GREEN-90: #82b01a;
  --weui-GREEN-BG-100: #789833;
  --weui-GREEN-BG-110: #6b882d;
  --weui-GREEN-BG-130: #65802b;
  --weui-GREEN-BG-90: #85a247;
  --weui-INDIGO-100: #1196ff;
  --weui-INDIGO-120: #0d78cc;
  --weui-INDIGO-170: #052d4d;
  --weui-INDIGO-80: #40abff;
  --weui-INDIGO-90: #28a0ff;
  --weui-INDIGO-BG-100: #0d78cc;
  --weui-INDIGO-BG-110: #0b6bb7;
  --weui-INDIGO-BG-130: #09548f;
  --weui-INDIGO-BG-90: #2585d1;
  --weui-LIGHTGREEN-100: #3eb575;
  --weui-LIGHTGREEN-120: #31905d;
  --weui-LIGHTGREEN-170: #123522;
  --weui-LIGHTGREEN-80: #64c390;
  --weui-LIGHTGREEN-90: #51bc83;
  --weui-LIGHTGREEN-BG-100: #31905d;
  --weui-LIGHTGREEN-BG-110: #2c8153;
  --weui-LIGHTGREEN-BG-130: #226541;
  --weui-LIGHTGREEN-BG-90: #31905d;
  --weui-LINK-100: #7d90a9;
  --weui-LINK-120: #647387;
  --weui-LINK-170: #252a32;
  --weui-LINK-80: #97a6ba;
  --weui-LINK-90: #899ab1;
  --weui-LINKFINDER-100: #dee9ff;
  --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
  --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
  --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
  --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
  --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
  --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
  --weui-ORANGE-100: #c87d2f;
  --weui-ORANGE-120: #a06425;
  --weui-ORANGE-170: #3b250e;
  --weui-ORANGE-80: #d39758;
  --weui-ORANGE-90: #cd8943;
  --weui-ORANGE-BG-100: #bb6000;
  --weui-ORANGE-BG-110: #a85600;
  --weui-ORANGE-BG-130: #824300;
  --weui-ORANGE-BG-90: #c1701a;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.8);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #8183ff;
  --weui-PURPLE-120: #6768cc;
  --weui-PURPLE-170: #26274c;
  --weui-PURPLE-80: #9a9bff;
  --weui-PURPLE-90: #8d8fff;
  --weui-PURPLE-BG-100: #6768cc;
  --weui-PURPLE-BG-110: #5c5db7;
  --weui-PURPLE-BG-130: #48498f;
  --weui-PURPLE-BG-90: #7677d1;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #c84040;
  --weui-RED-170: #4b1818;
  --weui-RED-80: #fb7373;
  --weui-RED-90: #fa6262;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #ba4940;
  --weui-RED-BG-130: #913832;
  --weui-RED-BG-90: #d3625a;
  --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
  --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
  --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
  --weui-YELLOW-100: #cc9c00;
  --weui-YELLOW-120: #a37c00;
  --weui-YELLOW-170: #3d2f00;
  --weui-YELLOW-80: #d6af33;
  --weui-YELLOW-90: #d1a519;
  --weui-YELLOW-BG-100: #bf9100;
  --weui-YELLOW-BG-110: #ab8200;
  --weui-YELLOW-BG-130: #866500;
  --weui-YELLOW-BG-90: #c59c1a;
  --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #c87d2f;
  --weui-YELLOW: #cc9c00;
  --weui-GREEN: #74a800;
  --weui-LIGHTGREEN: #3eb575;
  --weui-TEXTGREEN: #259c5c;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1196ff;
  --weui-PURPLE: #8183ff;
  --weui-LINK: #7d90a9;
  --weui-REDORANGE: #ff6146;
  --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
  --weui-FG: #fff;
  --weui-WHITE: hsla(0, 0%, 100%, 0.8);
  --weui-FG-5: hsla(0, 0%, 100%, 0.1);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
  --weui-BG: #000;
  --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
  --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
}
.wx-root {
  pointer-events: auto;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    Helvetica Neue,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei UI,
    Microsoft YaHei,
    Arial,
    sans-serif;
}
.wx-root,
.wx_card_root {
  position: relative;
}
.wxw_hide {
  display: none !important;
}
.wx_uninteractive {
  pointer-events: none;
}
.wx-root,
body {
  --APPMSGCARD-BG: #fafafa;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --APPMSGCARD-BG: #1e1e1e;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --APPMSGCARD-BG: #1e1e1e;
  }
}
.wx-root,
body {
  --APPMSGCARD-LINE-BG: rgba(0, 0, 0, 0.07);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --APPMSGCARD-LINE-BG: hsla(0, 0%, 100%, 0.07);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --APPMSGCARD-LINE-BG: hsla(0, 0%, 100%, 0.07);
  }
}
.appmsg_card_context {
  position: relative;
  background-color: var(--APPMSGCARD-BG);
  border-radius: 8px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui-primary-loading {
  font-size: 16px;
  display: inline-flex;
  position: relative;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  color: #606060;
  animation: circleLoading 1s steps(60) infinite;
}
.weui-primary-loading__dot {
  border-top-right-radius: 100%;
  border-bottom-right-radius: 100%;
}
.weui-primary-loading:after,
.weui-primary-loading:before {
  content: '';
  display: block;
  width: 0.5em;
  height: 1em;
  box-sizing: border-box;
  border: 0.0875em solid;
}
.weui-primary-loading:before {
  border-right-width: 0;
  border-top-left-radius: 1em;
  border-bottom-left-radius: 1em;
  -webkit-mask-image: linear-gradient(180deg, #000 8%, rgba(0, 0, 0, 0.3) 95%);
}
.weui-primary-loading:after {
  border-left-width: 0;
  border-top-right-radius: 1em;
  border-bottom-right-radius: 1em;
  -webkit-mask-image: linear-gradient(180deg, transparent 8%, rgba(0, 0, 0, 0.3) 95%);
}
.weui-primary-loading__dot {
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -0.04375em;
  width: 0.0875em;
  height: 0.0875em;
  border-top-right-radius: 0.0875em;
  border-bottom-right-radius: 0.0875em;
  background: currentColor;
}
@keyframes circleLoading {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.weui-primary-loading_brand {
  color: var(--weui-BRAND);
}
.weui-primary-loading_transparent {
  color: #ededed;
}
.weui-loading {
  font-size: 16px;
  width: 1em;
  height: 1em;
  display: inline-block;
  vertical-align: middle;
  background: transparent
    url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    no-repeat;
  background-size: 100%;
}
.weui-btn_loading.weui-btn_primary .weui-loading,
.weui-loading.weui-icon_toast,
.weui-loading.weui-loading_transparent {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23ededed' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23ededed' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23ededed' offset='0%25'/%3E%3Cstop stop-color='%23ededed' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23ededed' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E");
}
.weui-mask-loading {
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  width: 1em;
  height: 1em;
  -webkit-mask: url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    0 0 no-repeat;
  mask: url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    0 0 no-repeat;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  color: #606060;
}
@keyframes weuiLoading {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.wx_widget_placeholder {
  width: 100%;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--weui-BG-3);
  border-radius: 8px;
}
.wx_imgbc_placeholder {
  background-color: var(--weui-BG-3);
}
.wx_img_placeholder,
.wx_loading_placeholder {
  background: var(--weui-BG-3)
    url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  background-size: 16px;
  border-radius: 8px;
}
.weui-play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: hsla(0, 0%, 92.9%, 0.9);
  border-radius: 50%;
  font-size: 0;
}
.weui-play-btn:before {
  content: '';
  display: inline-block;
  width: 60%;
  height: 60%;
  vertical-align: middle;
  background-size: cover;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cpath fill='%23151515' fill-rule='evenodd' d='M9.524 4.938l10.092 6.21a1 1 0 0 1 0 1.704l-10.092 6.21A1 1 0 0 1 8 18.21V5.79a1 1 0 0 1 1.524-.852z'/%3E%3C/svg%3E");
}
.weui-play-btn_pause:before {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 5h2c.552 0 1 .418 1 .933v12.134c0 .515-.448.933-1 .933H7c-.552 0-1-.418-1-.933V5.933C6 5.418 6.448 5 7 5zm8 0h2c.552 0 1 .418 1 .933v12.134c0 .515-.448.933-1 .933h-2c-.552 0-1-.418-1-.933V5.933c0-.515.448-.933 1-.933z' fill-rule='evenodd' fill-opacity='.9'/%3E%3C/svg%3E");
}
.weui-play-btn_stop:before {
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cg fill='none' fill-rule='evenodd'%3E    %3Crect width='8' height='8' fill='%23000' fill-rule='nonzero' rx='.8' transform='translate(8 8)'/%3E    %3Cpath d='M0 0h24v24H0z'/%3E  %3C/g%3E%3C/svg%3E");
}
.weui-play-btn_primary {
  font-size: 10px;
  width: 4.8em;
  height: 4.8em;
  background-size: cover;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E  %3Cg fill='none' fill-rule='evenodd'%3E    %3Ccircle cx='24' cy='24' r='24' fill='%23000' fill-opacity='.15'/%3E    %3Cpath fill='%23FFF' fill-rule='nonzero' d='M24 0c13.255 0 24 10.745 24 24S37.255 48 24 48 0 37.255 0 24 10.745 0 24 0zm0 1.44C11.54 1.44 1.44 11.54 1.44 24c0 12.46 10.1 22.56 22.56 22.56 12.46 0 22.56-10.1 22.56-22.56 0-12.46-10.1-22.56-22.56-22.56zm-4.8 13.828a1.2 1.2 0 0 1 .595.158l13.182 7.532a1.2 1.2 0 0 1 0 2.084l-13.182 7.532A1.2 1.2 0 0 1 18 31.532V16.468a1.2 1.2 0 0 1 1.2-1.2z'/%3E  %3C/g%3E%3C/svg%3E");
}
.weui-play-btn_primary,
.weui-play-loading {
  display: inline-block;
  vertical-align: middle;
}
.weui-play-loading {
  width: 32px;
  height: 32px;
  background: transparent
    url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAEH0lEQVR42uXb2U9UZxzG8d+wzCKLQylLEbUIopVNBUGoIIuIVkWbWtDGlLZWoWrRqtRq3bvbpvtFL/qH9Lo3TXrfv6Hpbf8B+p3kOcmbN0Nimw4w532STw7xAuZ5wsyccwat0FleXq5ED45iDu/jMX7F3/gTty1OoVAzprGEb/AtvsP3+BG/wM+MFXMokMIIbuIJvgI0gEbQAH/Az89WjOGBZ3AI9/E5vniKAX6DnztWTOEBJ7AXH+JTfBYNIF/K197T4Af8hL8Q5XdUWrGEB5vFBXyET0QDIP8AH+MKZjGJMcxhFGVWLOHBbsESHgkjQL8FzhBPsIADaLQ4hCJ7cA8PAW8A6N8mkbW4hDIlOIy7uI8H8hCPHdNF9Vz+F+VfVXkNAA2AR5hFrcUxeou7jTtyVx7gHnZbXKNT2Fv4IM8AN7DZ4hqdzt6EBgA0wnlstLiGctW4jCXckmiIt5C0uEZneGdwQ5acIRZRbXEOBTtx3R9AX2+yOIeCaczjGt6T69JpcQ8l9+MqrkVHlT9ucQ8lk7iYZ4DFWJ3WrhRd2r6LRbkqYxZCKHoWl3BZrmAeGyzuoWSdyvsDDFgIoegQFuQduYQqCyG62pvHgnOcthCim5oXMO9YCOJ9XwO0aICLnhoLIbrNdd7xNs5ZKNHdWX+AYxZKKHsCb+BNx5CFEsqe1gBzuaN0WSjRtf+cZ6eFktwLHl73bLNQQtnXcM7TYqGEsjN5Bmi3UKJPc856dlkooewUznj6LJRQdhCzKj6bE8xNEA3QodIzjpMWSvQJ0GnHKzmxv//vDJBaYYA2CyX6BPhlzwELJZTtxinHSR0zFkIomnWL6zgd2jXBeFRcTuAlpCyEULRVpY/LMemwEELRMhzJM8AUKiyEULQdR+VIJNZ/B+QNUI4JTKn4lKPJQghFm70BJmU8lE+JExjAYZmUCQwjbXGPPi06iEMYlzHpQ6nFPTo5msC4jOKgjr3r6fwg91tbqG/chFGJBhjBMPrXwxWjnrJlKMkpxA9oU/HIsLyIIdSvYflSlBV6gAQ6MRIVl0HZj+2r+eKoskmkkJRSSeQUYoRt7gAqPuAce7EV5QUunkIF0khJwQbwH0CDCg86xfuxD73SjUYk/+fiGWxEpWxAxhmiXEpW4++I+5wB9jkD7MVu9GiIVtQi9R+vTTKoxjPIaoAqaABoACRRspq30TrQjz7ZI+4AXdgl27EZ9ahRsUqpkqzK1ksdav0BpELSKp9Yi7efOhXui8pLD7qcAV7ATuxAm7TgeWzFFmySRjR4A0ADMJw3QBkSa30Z3aSyPdKNTnHLtz/FAM95I9RKjTdCCglbL9ErcL2KFmSAdVl8pWsJlWiLyjsDtIo/QLP4A9Sgsqj+c2Wep0gVnlW5ZpWGBlBxNDiF06vxqv4PFQWElSE4GpoAAAAASUVORK5CYII=)
    0 0 no-repeat;
  background-size: 100%;
  animation: weuiLoading 1s steps(60) 0ms infinite;
  font-size: 0;
  color: transparent;
}
:host(.wx_tap_highlight_active) .wx_tap_link {
  opacity: 0.5;
}
:host(.wx_tap_highlight_active) .wx_tap_card {
  background-color: #f3f3f3;
}
:host(.wx_tap_highlight_active) .wx_tap_cell {
  background-color: rgba(0, 0, 0, 0.05);
}
@media (prefers-color-scheme: dark) {
  :host(.wx_tap_highlight_active) .wx_tap_card {
    background-color: #252525;
  }
  :host(.wx_tap_highlight_active) .wx_tap_cell {
    background-color: hsla(0, 0%, 100%, 0.1);
  }
}
.wx_css_active :active {
  opacity: 0.5;
}
.wx_hover_card:before {
  border-radius: 8px;
  border: 1px solid rgba(7, 193, 96, 0.3);
}
.wx_hover_card:before,
.wx_selected_card:before {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-sizing: border-box;
  pointer-events: none;
  z-index: 9;
}
.wx_selected_card:before {
  border-radius: 8px;
  border: 1.5px solid #07c160;
  background: rgba(7, 193, 96, 0.1);
}
div:focus {
  outline: none;
}
.wx-root,
body {
  --weui-COVER: rgba(0, 0, 0, 0.05);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-COVER: hsla(0, 0%, 100%, 0.05);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-COVER: hsla(0, 0%, 100%, 0.05);
  }
}
.wx-root,
.wx-root[data-weui-theme='dark'],
body,
body[data-weui-theme='dark'] {
  --weui-REDORANGE: #ff6146;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-REDORANGE: #ff6146;
  }
}
.wx-root {
  max-width: 100% !important;
}
img {
  pointer-events: none;
}
.wxw_wechannel_card {
  display: block;
}
.wxw_wechannel_card_disabled:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: hsla(0, 0%, 100%, 0.5);
  z-index: 3;
}
.wxw_wechannel_card_disabled .wxw_wechannel_card_inner {
  filter: blur(2px);
}
.wxw_wechannel_live_msg {
  background: #f7f7f7;
  opacity: 0.95;
  color: rgba(0, 0, 0, 0.55);
}
.wxw_wechannel_card {
  margin: 0 auto;
  font-size: 14px;
  text-align: left;
  line-height: 1.4;
  letter-spacing: normal;
  overflow: hidden;
  width: 65%;
  position: relative;
}
.wxw_wechannel_card.wx_tap_highlight_active {
  background-color: var(--APPMSGCARD-BG);
}
.wxw_wechannel_card.wx_tap_highlight_active:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--weui-COVER);
}
.wxw_wechannel_msg {
  background: var(--APPMSGCARD-BG);
  color: var(--weui-FG-2);
  flex-direction: column;
  padding: 0 32px;
  box-sizing: border-box;
  z-index: 1;
  text-align: center;
  font-size: 14px;
}
.wxw_wechannel_msg,
.wxw_wechannel_msg_web {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.wxw_wechannel_msg_web {
  z-index: 10;
}
.wxw_wechannel_card_ft {
  padding: 8px 12px;
  align-items: center;
  position: relative;
  color: var(--weui-FG-1);
}
.wxw_wechannel_card_ft:before {
  content: '';
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--APPMSGCARD-LINE-BG);
  color: var(--APPMSGCARD-LINE-BG);
  transform-origin: 0 0;
  transform: scaleY(0.5);
  left: 12px;
  right: 12px;
}
.wxw_wechannel_profile {
  padding: 12px;
  align-items: center;
}
.wxw_wechannel_avatar.wxw_wechannel_avatar {
  width: 20px;
  height: 20px !important;
  border-radius: 50%;
  margin-right: 8px;
}
.wxw_wechannel_nickname {
  font-weight: 500;
  line-height: 1.2;
}
.wxw_wechannel_desc,
.wxw_wechannel_nickname {
  color: var(--weui-FG-0);
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}
.wxw_wechannel_desc {
  padding: 0 12px 12px;
  margin-top: -4px;
}
.wxw_wechannel_logo {
  margin-right: 5px;
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px !important;
  background-size: cover;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 18 18'%3E  %3Cg fill='none' fill-rule='evenodd'%3E    %3Cpath d='M0 0h18v18H0z'/%3E    %3Cpath stroke='%23FA9D3B' stroke-width='.9' d='M9 9.878S6.512 15 5.161 15 1.556 6.027 2.457 3.753C3.827.293 9 9.878 9 9.878zm0 0S11.488 15 12.839 15c1.352 0 3.605-8.973 2.704-11.247C14.173.293 9 9.878 9 9.878z'/%3E  %3C/g%3E%3C/svg%3E");
}
.wxw_wechannel_info_circle_regular {
  -webkit-mask-size: 100%;
  mask-size: 100%;
  display: inline-block;
  vertical-align: middle;
  width: 1.42em;
  height: 1.42em;
  margin-bottom: 4px;
  background-color: var(--weui-FG-0);
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='20' height='20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M1.667 10a8.333 8.333 0 1016.666 0 8.333 8.333 0 00-16.666 0zm15.666 0a7.333 7.333 0 11-14.666 0 7.333 7.333 0 0114.666 0zM10.5 8.333v5.834h-1V8.333h1zM10 7.5a.833.833 0 100-1.667.833.833 0 000 1.667z' fill='%23000' fill-opacity='.3'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='20' height='20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M1.667 10a8.333 8.333 0 1016.666 0 8.333 8.333 0 00-16.666 0zm15.666 0a7.333 7.333 0 11-14.666 0 7.333 7.333 0 0114.666 0zM10.5 8.333v5.834h-1V8.333h1zM10 7.5a.833.833 0 100-1.667.833.833 0 000 1.667z' fill='%23000' fill-opacity='.3'/%3E%3C/svg%3E");
}
.wxw_wechannel_video_context {
  padding-bottom: 100%;
  background-size: cover;
}
.wxw_wechannel_video_context .weui-play-btn_primary {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.wxw_wechannel_img_context {
  position: relative;
  white-space: nowrap;
  font-size: 0;
  background-size: cover;
}
.wxw_wechannel_img_list {
  max-width: none !important;
}
.wxw_wechannel_img {
  pointer-events: none;
  min-height: 65vw;
  max-height: 88vw;
  background-position: 50% 0;
  background-size: cover;
}
.wxw_wechannel_img_navs {
  padding: 12px 12px 0;
  text-align: center;
  margin-bottom: -4px;
}
.wxw_wechannel_img_nav {
  display: inline-block;
  vertical-align: top;
  width: 6px;
  height: 6px;
  margin: 0 4px;
  border-radius: 50%;
  background: var(--weui-FG-3);
}
.wxw_wechannel_img_nav.wxw_wechannel_img_nav_current {
  background: var(--weui-ORANGE);
}
.weui-flex {
  align-items: center;
}
.weui-flex__item {
  min-width: 0;
}
.wxw_wechannel_video_context {
  position: relative;
}
.wxw_wechannel_video_context .weui-play-btn {
  width: 52px;
  height: 52px;
}
.js_wechannel_img_card {
  max-width: none;
}
.wxw_wechannel_card_live {
  width: auto;
  max-width: none;
}
.wxw_wechannel_card_live .wxw_wechannel_card_bd .wxw_wechannel_live_desc {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.wxw_wechannel_card_live .wxw_wechannel_card_ft {
  padding-left: 16px;
  padding-right: 16px;
}
.wxw_wechannel_card_live .wxw_wechannel_card_ft:before {
  left: 16px;
  right: 16px;
}
.wxw_wechannel_live_context {
  padding: 16px;
}
.wxw_wechannel_live_overview {
  align-items: center;
}
.icon_wxw_wechannel_live {
  display: inline-block;
  vertical-align: middle;
  width: 14px;
  height: 14px;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14'%3E  %3Cg fill='%23FF6146'%3E    %3Cpath d='M7 13.667A6.667 6.667 0 1 0 7 .333a6.667 6.667 0 0 0 0 13.334zm0-1A5.667 5.667 0 1 1 7 1.333a5.667 5.667 0 0 1 0 11.334z'/%3E    %3Ccircle cx='7' cy='7' r='3.667'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14'%3E  %3Cg fill='%23FF6146'%3E    %3Cpath d='M7 13.667A6.667 6.667 0 1 0 7 .333a6.667 6.667 0 0 0 0 13.334zm0-1A5.667 5.667 0 1 1 7 1.333a5.667 5.667 0 0 1 0 11.334z'/%3E    %3Ccircle cx='7' cy='7' r='3.667'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
}
.wxw_wechannel_live_hd {
  display: flex;
}
.wxw_wechannel_live_ft {
  padding-left: 16px;
}
.wxw_wechannel_live_nickname {
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  display: block;
  color: var(--weui-FG-0);
  font-weight: 500;
  margin-right: 2px;
  font-size: 15px;
}
.wxw_wechannel_live_desc {
  margin-top: 2px;
  color: var(--weui-FG-1);
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.wxw_wechannel_live_tips {
  margin-top: 16px;
  color: var(--weui-FG-1);
  line-height: 1.4;
}
.wxw_wechannel_live_hd .wxw_wechannel_live_avatar {
  width: 4.4em;
  height: 4.4em !important;
  border-radius: 100%;
  margin-right: 10px;
  font-size: 10px;
}
.wxw_wechannel_live_ft .wxw_wechannel_live_btn {
  display: inline-flex;
  align-items: center;
  letter-spacing: 1px;
}
.wxw_wechannel_live_btn {
  background: var(--weui-SECONDARY-BG);
  color: var(--weui-FG-0);
}
.wxw_wechannel_live_btn.weui-btn_disabled {
  background: rgba(0, 0, 0, 0.03);
}
.wxw_wechannel_live_btn.wxw_wechannel_live_btn_inlive {
  background-color: var(--weui-REDORANGE);
  color: #f7f7f7;
}
.wxw_wechannel_live_btn .icon_wxw_wechannel_live {
  margin-right: 4px;
}
.wxw_wechannel_live_btn.wx_tap_highlight_active {
  position: relative;
  overflow: hidden;
}
.wxw_wechannel_live_btn.wx_tap_highlight_active:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: var(--weui-FG-3);
}
.wxw_wechannel_card_topic {
  width: auto;
  max-width: none;
}
.wxw_wechannel_card_topic .wxw_wechannel_card_ft {
  padding-left: 16px;
  padding-right: 16px;
}
.wxw_wechannel_card_topic .wxw_wechannel_card_ft:before {
  left: 16px;
  right: 16px;
}
.wxw_wechannel_topic_context {
  font-size: 14px;
  padding: 20px 16px 16px;
}
.wxw_wechannel_topic_overview {
  align-items: center;
}
.wxw_wechannel_topic_hd {
  display: flex;
}
.wxw_wechannel_topic_ft {
  padding-left: 16px;
}
.wxw_wechannel_topic_profile {
  margin-bottom: 12px;
}
.wxw_wechannel_topic_name,
.wxw_wechannel_topic_nickname {
  display: block;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}
.wxw_wechannel_topic_nickname {
  color: var(--weui-FG-1);
  font-size: 14px;
  font-weight: 400;
}
.wxw_wechannel_topic_name {
  color: var(--weui-FG-0);
  font-size: 17px;
  font-weight: 500;
}
.wxw_wechannel_topic_tips {
  margin-top: 16px;
  color: var(--weui-FG-1);
  line-height: 1.4;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: pre-wrap;
}
.wxw_wechannel_topic_endtime,
.wxw_wechannel_topic_tips + .wxw_wechannel_topic_tips {
  margin-top: 4px;
}
.wxw_wechannel_topic_hd .wxw_wechannel_topic_avatar {
  width: 2em;
  height: 2em !important;
  border-radius: 100%;
  margin-right: 8px;
  font-size: 10px;
}
.wxw_wechannel_topic_btn {
  background-color: var(--weui-ORANGE);
  padding: 0 12px;
  min-width: 60px;
  font-size: 14px;
  line-height: 2.28571;
  position: relative;
  z-index: 1;
}
.wxw_wechannel_topic_btn[disabled] {
  background: rgba(0, 0, 0, 0.03);
}
.wxw_wechannel_topic_btn:active:before {
  display: none;
}
.wxw_wechannel_topic_btn.wxw_wechannel_topic_btn_active:before {
  display: block;
  content: '';
}
.wxw_wechannel_logo_tail:before {
  content: '\00b7';
  margin: 0 2px;
}
.wxw_wechannel_card.appmsg_card_channel {
  background-color: transparent;
  border-radius: 4px;
  position: relative;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_card_bd {
  border-radius: 0 0 4px 4px;
  overflow: hidden;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_card_ft {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  padding: 8px 12px;
  justify-content: space-between;
  box-sizing: border-box;
  background-image: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.5));
  line-height: 20px;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_card_ft:before {
  display: none;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_card_ft .wxw_wechannel_profile {
  padding: 0 60px 0 0;
  display: flex;
  align-items: center;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_card_ft .wxw_wechannel_nickname {
  line-height: 20px;
  color: #fff;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_card_ft .wxw_wechannel_authicon_wrp {
  flex-shrink: 0;
  width: 14px;
  height: 14px;
  margin-left: 3px;
  background-repeat: no-repeat;
  background-size: contain;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_like {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  overflow: hidden;
  color: hsla(0, 0%, 100%, 0.9);
  border-radius: 9px;
  line-height: 16px;
  font-weight: 400;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_like_content {
  display: flex;
  align-items: center;
  font-size: 12px;
  position: relative;
  z-index: 1;
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_like_icon {
  flex-shrink: 0;
  margin-right: 4px;
  width: 16px;
  height: 16px;
  background-size: cover;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABWElEQVR42u3Ur0sDARjG8clUziQyRReUNZsaNAsqCIIMYeVMwyY2qwuCJl3UYLT7qwkijNkEDQZ/bP+AWxIxiOzc+VXeIGP43rm97V74pD13z8Ngi0UXXXTa+b7fgQksYhYDSn4E81jAaCvFcazjGb/vE+cYa8jP4RqNV8Jy2HIHl/KCJ2whg1UcwsM7XMnnUMcb9pCFi11U5D0HYQbsyEN5OE0+n8Qj5Nv4uSJSTbIJHEkmG6R8Ch6OlVwCt/LiM3T/ke3CHV4wpA3Io45kgLG9yKEzQHZGxq5owQLKBr8mBzXsa8ESCkY/6SpOtdAJqgbl/fi+bS24IcHpNg9Yk/cuacE+VFBGT5vKh/GKG8SDPJCRtRdwWiwfxANqGA/z4KYyIky5B1eJ6yMMym1GSPm9Um4wQik3GqGXG47Qy81H6OX2I1JKufmID9ty/f/9CulYdNH9874AfGGk2NR09sAAAAAASUVORK5CYII=');
}
.wxw_wechannel_card.appmsg_card_channel .wxw_wechannel_like_icon.wxw_wechannel_like_hot {
  flex-shrink: 0;
  margin-right: 4px;
  width: 13px;
  height: 16px;
  background-size: cover;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAgCAYAAAAMq2gFAAAB50lEQVR42u3Vv2tTURjG8ahBk6FCbIVGHBSiEDGl4FQp0hDHUA0q6GBEwQ4tLaWV/APVRQpFBCsmQ8QuKbRkMsRNiJuTUio6WioFh6qNYn72KzyBS0hucnubRfLCh0vOec/73ntycuPoxv8X1Wr1GbIY7nSjBfyLCsY72egDviCHEi52osmYnmYax/AD2f1uMoA83sOpsSco4/B+NTmq7dqGzzA+oyf02m1wQNenKni1bv4xKjhip8kFRBBACcsNclawWTfWa6XJcXxFGEn8gbdB3lt8Nny+hIyVRq+0VdewhXSTvCXlRXEX29rK0+1uWUUFMrrebpLrwwZqsYUiHrbTaFFb9U6LC+hvcSInMIk+5PCpVZOD+I40PIjhssVDNK8d6TFLOlH75ds4rfdUY9AsaUhJozYaXVGNoFlSSEkRG43CqhEySwoo6YGNRjHVOG+W5MQvpExypjBrMp9SDWerO3qJQpM3QRS1uNlg/iT+ItnOo/tQRKJu/Cx28FF+4kxdTlxrfe3u8wvd9X3D2Gvk4cc5/MaqYX5ca+JWvlAX3qCMR+jVdj435CTUzIM5lLTGZfX0uJHVXX7T9ZZh/o7h/VZVE/ee//hwA2sqNmKYC2psHdf1J2kv9A70Nxg/hUOObnQydgE4y+fJnpuJjAAAAABJRU5ErkJggg==');
}
.wxw_wechannel_card .wxw_wechannel_logo {
  flex-shrink: 0;
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px !important;
  background-size: cover;
  background-image: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
}
.wxw_wechannel_video_context {
  min-height: 65vw;
  max-height: 88vw;
  padding-bottom: 0;
  background-position: 50%;
}
.wxw_wechannel_card_horizontal {
  width: 100%;
  max-width: none;
}
.wxw_wechannel_card_horizontal .wxw_wechannel_video_context {
  min-height: 56vw;
  max-height: 75vw;
  padding-bottom: 0;
}
@media screen and (min-width: 1024px) {
  .wxw_wechannel_video_context {
    max-height: 586.7px;
    min-height: 440px;
  }
  .wxw_wechannel_card_horizontal {
    width: 100%;
  }
  .wxw_wechannel_card_horizontal .wxw_wechannel_video_context {
    min-height: 381px;
    max-height: 508px;
  }
}
.common-web {
  max-width: none;
}
.common-web .wxw_wechannel_card_live {
  width: 350px;
  max-width: none;
}
.common-web .wxw_wechannel_card_topic {
  width: 376px;
  max-width: none;
}
.common-web .wxw_wechannel_video_context {
  min-height: 0;
}
.js_wechannel_msg {
  display: none;
}
.wxw_wechannel_tips_show {
  display: flex;
}
@media (prefers-color-scheme: dark) {
  .wxw_wechannel_topic_btn[disabled] {
    background: var(--weui-BTN-DEFAULT-BG);
  }
}
