import { useRef } from 'react'
import { useShadowDom } from '@renderer/hooks/useShadowDom'
import type { VideoSnapAttributes } from './videosnap'
import cssText from './videosnap.css?inline'

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'mp-common-videosnap': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          'data-url'?: string
          'data-headimgurl'?: string
          'data-username'?: string
          'data-nickname'?: string
          'data-desc'?: string
          'data-nonceid'?: string
          'data-type'?: string
          'data-mediatype'?: string
          'data-authiconurl'?: string
          'data-from'?: string
          'data-width'?: string
          'data-height'?: string
          'data-id'?: string
          class?: string
        },
        HTMLElement
      >
    }
  }
}

interface VideoSnapComponentProps {
  url: string
  pluginname: string
  headimgurl: string
  username: string
  nickname: string
  desc: string
  nonceid: string
  type: string
  mediatype: string
  authiconurl: string
  from: string
  width: string
  height: string
  id: string
}
export const VideoSnapComponent = (props: VideoSnapComponentProps) => {
  const shadowRootRef = useRef<HTMLElement>(null)

  const getTemplateHTML = (attrs: VideoSnapAttributes) => `
    <div class="wx-root common-web" data-weui-theme="light">
      <div role="option" tabindex="0" title="" class="wxw_wechannel_card appmsg_card_channel appmsg_card_context js_wechannel_video_card wx_tap_card wx_card_root wxw_wechannel_card_horizontal common-web" style="width: 575px;">
        <div class="wxw_wechannel_card_bd">
          <div class="wxw_wechannel_video_context" style="background-image: url('${attrs.url}'); height: 323px;">
            <i class="weui-play-btn_primary"></i>
          </div>
          <div class="wxw_wechannel_card_ft weui-flex">
            <div class="wxw_wechannel_profile weui-flex">
              <div class="wxw_wechannel_logo"></div>
              <div class="wxw_wechannel_nickname js_wx_tap_highlight">
                ${attrs.nickname}
              </div>
              <div class="wxw_wechannel_authicon_wrp" style="background-image: url('${attrs.authiconurl}');"></div>
            </div>
          </div>
        </div>
        <div class="wxw_wechannel_msg_web js_wechannel_msg">
          <div class="wxw_wechannel_msg_inner js_wechannel_msg_text"></div>
        </div>
      </div>
    </div>
  `

  const templateHTML = getTemplateHTML(props as VideoSnapAttributes)

  useShadowDom({
    ref: shadowRootRef,
    templateHTML,
    cssText: cssText,
  })

  return (
    <mp-common-videosnap
      class="js_uneditable custom_select_card channels_iframe videosnap_video_iframe mp_common_widget"
      data-pluginname={props.pluginname}
      data-url={props.url}
      data-headimgurl={props.headimgurl}
      data-username={props.username}
      data-nickname={props.nickname}
      data-desc={props.desc}
      data-nonceid={props.nonceid}
      data-type={props.type}
      data-mediatype={props.mediatype}
      data-authiconurl={props.authiconurl}
      data-from={props.from}
      data-width={props.width}
      data-height={props.height}
      data-id={props.id}
      ref={shadowRootRef}
    />
  )
}
