import { Node, mergeAttributes } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { VideoSnapComponent } from './videosnap-component'
import type { MapOptions } from '../map/common'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    videoSnap: {
      setVideoSnap: (attrs: VideoSnapAttributes) => ReturnType
    }
  }
}

export interface VideoSnapAttributes {
  url: string
  headimgurl: string
  pluginname: string
  username: string
  nickname: string
  desc: string
  nonceid: string
  type: string
  mediatype: string
  authiconurl: string
  from: string
  width: string
  height: string
  class: string
  id: string
}

export const VideoSnap = Node.create<MapOptions>({
  name: 'videoSnap',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      url: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-url'),
        renderHTML: (attributes) => ({
          'data-url': attributes.url,
        }),
      },
      pluginname: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-pluginname'),
        renderHTML: (attributes) => ({
          'data-pluginname': attributes.pluginname,
        }),
      },
      headimgurl: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-headimgurl'),
        renderHTML: (attributes) => ({
          'data-headimgurl': attributes.headimgurl,
        }),
      },
      username: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-username'),
        renderHTML: (attributes) => ({
          'data-username': attributes.username,
        }),
      },
      nickname: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-nickname'),
        renderHTML: (attributes) => ({
          'data-nickname': attributes.nickname,
        }),
      },
      desc: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-desc'),
        renderHTML: (attributes) => ({
          'data-desc': attributes.desc,
        }),
      },
      nonceid: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-nonceid'),
        renderHTML: (attributes) => ({
          'data-nonceid': attributes.nonceid,
        }),
      },
      type: {
        default: 'video',
        parseHTML: (element) => element.getAttribute('data-type'),
        renderHTML: (attributes) => ({
          'data-type': attributes.type,
        }),
      },
      mediatype: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-mediatype'),
        renderHTML: (attributes) => ({
          'data-mediatype': attributes.mediatype,
        }),
      },
      authiconurl: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-authiconurl'),
        renderHTML: (attributes) => ({
          'data-authiconurl': attributes.authiconurl,
        }),
      },
      from: {
        default: 'new',
        parseHTML: (element) => element.getAttribute('data-from'),
        renderHTML: (attributes) => ({
          'data-from': attributes.from,
        }),
      },
      width: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-width'),
        renderHTML: (attributes) => ({
          'data-width': attributes.width,
        }),
      },
      height: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-height'),
        renderHTML: (attributes) => ({
          'data-height': attributes.height,
        }),
      },
      id: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-id'),
        renderHTML: (attributes) => ({
          'data-id': attributes.id,
        }),
      },
      class: {
        default: '',
        parseHTML: (element) => element.getAttribute('class'),
        renderHTML: (attributes) => ({
          class: attributes.class,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'mp-common-videosnap',
        getAttrs: (element) => element.getAttribute('data-type') === 'video' && null,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'section',
      { class: 'channels_iframe_wrp' },
      ['mp-common-videosnap', mergeAttributes(HTMLAttributes)],
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(({ node }) => {
      return (
        <NodeViewWrapper
          as="section"
          class="channels_iframe_wrp custom_select_card_wrp my-4 flex items-center justify-center"
        >
          <VideoSnapComponent {...(node.attrs as VideoSnapAttributes)} />
        </NodeViewWrapper>
      )
    })
  },

  addCommands() {
    return {
      setVideoSnap:
        (attrs) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs,
          })
        },
    }
  },
})
