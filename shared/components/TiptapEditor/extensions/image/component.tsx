import { cn } from '@renderer/lib/utils'
import type { NodeViewProps } from '@tiptap/react'
import { NodeViewWrapper } from '@tiptap/react'
import { useRef } from 'react'

export const alignClassMap = {
  left: 'mr-auto',
  center: 'mx-auto',
  right: 'ml-auto',
}

export const ImageComponent = (props: NodeViewProps) => {
  const imgRef = useRef<HTMLImageElement>(null)
  const startXRef = useRef(0)
  const startWidthRef = useRef(0)

  const handleMouseDown = (e: React.MouseEvent, direction: 'left' | 'right') => {
    e.preventDefault()
    startXRef.current = e.clientX
    startWidthRef.current = imgRef.current?.offsetWidth || 0

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const dx = moveEvent.clientX - startXRef.current
      const newWidth =
        direction === 'right' ? startWidthRef.current + dx : startWidthRef.current - dx
      // 最大宽度限制
      const maxWidth = 600 // 设置最大宽度
      if (imgRef.current && newWidth > 50 && newWidth <= maxWidth) {
        imgRef.current.style.width = `${newWidth}px`
        props.updateAttributes({ width: `${newWidth}px` }) // 更新 Tiptap 中的属性
      }
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }
  return (
    <NodeViewWrapper className="" as="div">
      <div
        className={cn(
          'tiptap-image-wrapper group/media relative w-full max-w-full [font-size:0]',
          alignClassMap[props.node.attrs.align] || '',
        )}
        style={{
          width: props.node.attrs.width,
        }}
        data-resizing="false"
      >
        <span className="inline-block h-full w-full">
          <img ref={imgRef} className="w-full" {...props.node.attrs} />
        </span>
        <div
          onMouseDown={(e) => handleMouseDown(e, 'left')}
          className="absolute inset-y-0 left-0 z-40 flex h-full w-4 cursor-col-resize select-none flex-col justify-center pl-[5px] after:flex after:h-12 after:max-h-[50%] after:w-1.5 after:rounded-[20px] after:border after:border-white/90 after:bg-black/60 after:opacity-0 after:transition-opacity after:duration-200 after:ease-in-out after:content-['_'] group-hover/media:after:opacity-100"
          data-resizing="false"
        ></div>
        <div
          onMouseDown={(e) => handleMouseDown(e, 'right')}
          className="absolute inset-y-0 right-0 z-40 flex h-full w-4 cursor-col-resize select-none flex-col items-end justify-center pr-[5px] after:flex after:h-12 after:max-h-[50%] after:w-1.5 after:rounded-[20px] after:border after:border-white/90 after:bg-black/60 after:opacity-0 after:transition-opacity after:duration-200 after:ease-in-out after:content-['_'] group-hover/media:after:opacity-100"
          data-resizing="false"
        ></div>
      </div>
    </NodeViewWrapper>
  )
}
