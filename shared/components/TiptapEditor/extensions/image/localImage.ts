import { dataURLToArrayBuffer } from '@renderer/utils/file'
import { CustomImage } from './image'
import { localPath2Url } from '@common/protocol'
import { uiEvents } from '@common/events/ui-events'

export const LocalImage = CustomImage.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      src: {
        default: null,
        // 更换协议
        parseHTML: (el) => {
          let src = (el as HTMLDivElement).getAttribute('src')
          if (src && src.startsWith('file')) {
            // 更换协议
            const url = new URL(src)
            src = localPath2Url(url.pathname)
          } else {
            // 判断是否为base64编码的图片
            if (src && src.startsWith('data:image/') && src.includes(';base64,')) {
              // base64暂存本地
              const arrayBuffer = dataURLToArrayBuffer(src)
              const filePath = window.api.sendSync(uiEvents.saveImageFile, arrayBuffer)
              src = localPath2Url(filePath)
            }
          }
          return src
        },
      },
    }
  },
})
