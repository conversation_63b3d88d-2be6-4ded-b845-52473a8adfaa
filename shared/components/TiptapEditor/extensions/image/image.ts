import Image from '@tiptap/extension-image'
import { ReactNodeViewRenderer } from '@tiptap/react'
import { ImageComponent } from './component'

export const IMAGE_NODE_NAME = 'comstomImage'

const alignStyleMap = {
  left: 'margin-left: auto;',
  center: 'margin-left: auto; margin-right: auto;',
  right: 'margin-right: auto;',
}

// 扩展Image
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    [IMAGE_NODE_NAME]: {
      setImageAlign: (options: { align: string }) => ReturnType
      setImageSrc: (options: { src: string }) => ReturnType
    }
  }
}
export const CustomImage = Image.extend({
  name: IMAGE_NODE_NAME,
  addAttributes() {
    return {
      ...this.parent?.(),
      width: {
        default: null,
        parseHTML: (el) => el.getAttribute('width'),
      },
      align: {
        default: null,
        parseHTML: (el) => el.getAttribute('align'),
        renderHTML: (attributes) => {
          return {
            align: attributes.align,
          }
        },
      },
    }
  },

  addCommands() {
    return {
      ...this.parent?.(),
      setImageAlign:
        (options: { align: string }) =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, {
            align: options.align,
          })
        },
      setImageSrc:
        (options: { src: string }) =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, {
            src: options.src,
          })
        },
    }
  },

  renderHTML({ HTMLAttributes }) {
    const { width, align, ...rest } = HTMLAttributes

    return [
      'div',
      {
        width,
        style: alignStyleMap[align] || '',
      },
      ['img', rest],
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(ImageComponent, {
      attrs: this.options.HTMLAttributes,
    })
  },
})
