import { useRef } from 'react'
import { useShadowDom } from '@renderer/hooks/useShadowDom'
import cssText from './profile.css?inline'
import type { ProfileAttributes } from './profile'

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'mp-common-profile': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          'data-pluginname'?: string
          'data-id'?: string
          'data-headimg'?: string
          'data-nickname'?: string
          'data-alias'?: string
          'data-signature'?: string
          'data-from'?: string
          'data-is_biz_ban'?: string
          'data-service_type'?: string
          class?: string
        },
        HTMLElement
      >
    }
  }
}
export const ProfileComponent = (props: ProfileAttributes) => {
  const shadowRootRef = useRef<HTMLElement>(null)

  const getTemplateHTML = (attrs: ProfileAttributes) => `
  <div>
    <div
      role="option"
      tabindex="0"
      aria-labelledby="js_a11y_wx_profile_nickname js_a11y_comma js_a11y_wx_profile_desc js_a11y_comma js_a11y_wx_profile_tips js_a11y_comma js_a11y_wx_profile_logo"
      class="appmsg_card_context wx_profile_card wx-root wx_tap_card wx_card_root common-web"
      data-weui-theme="light"
    >
      <div class="wx_profile_card_inner">
        <div aria-hidden="true" class="wx_profile_card_bd">
          <div class="wx_profile weui-flex">
            <div class="wx_profile_hd">
              <img src="${attrs.headimg}" alt="" class="wx_profile_avatar" />
            </div>
            <div class="wx_profile_bd weui-flex weui-flex__item">
              <div class="weui-flex__item">
                <strong id="js_a11y_wx_profile_nickname" class="wx_profile_nickname">
                  ${attrs.nickname}
                </strong>
                <div id="js_a11y_wx_profile_desc" class="wx_profile_desc">
                  ${attrs.signature}
                </div>
              </div>
              <i class="weui-icon-arrow"></i>
            </div>
          </div>
        </div>
        <div id="js_a11y_wx_profile_logo" aria-hidden="true" class="wx_profile_card_ft">
          ${attrs.service_type === '0' ? '服务号' : '公众号'}
        </div>
      </div>
    </div>
    <span aria-hidden="true" id="js_a11y_comma" class="weui-a11y_ref" style="display: none;">
      ，
    </span>
  </div>
  `

  const templateHTML = getTemplateHTML(props as ProfileAttributes)

  useShadowDom({
    ref: shadowRootRef,
    templateHTML,
    cssText: cssText,
  })

  return (
    <mp-common-profile
      class="js_uneditable custom_select_card mp_profile_iframe mp_common_widget"
      data-pluginname={props.pluginname}
      data-id={props.id}
      data-headimg={props.headimg}
      data-nickname={props.nickname}
      data-alias={props.alias}
      data-signature={props.signature}
      data-from={props.from}
      data-is_biz_ban={props.is_biz_ban}
      data-service_type={props.service_type}
      ref={shadowRootRef}
    />
  )
}
