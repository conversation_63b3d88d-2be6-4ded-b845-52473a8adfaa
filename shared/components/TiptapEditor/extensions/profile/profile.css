:host {
  all: initial;
  -webkit-text-size-adjust: inherit;
}

@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.8);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
    --weui-FG-1: hsla(0, 0%, 100%, 0.5);
    --weui-FG-2: hsla(0, 0%, 100%, 0.3);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  }
}
@media (prefers-color-scheme: dark) {
  .wx-root[data-weui-mode='care']:not([data-weui-theme='light']),
  body[data-weui-mode='care']:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.85);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
    --weui-FG-1: hsla(0, 0%, 100%, 0.55);
    --weui-FG-2: hsla(0, 0%, 100%, 0.35);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-FG: #fff;
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-BG: #000;
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  }
}
.wx-root,
body,
page {
  --weui-BTN-HEIGHT: 48;
  --weui-BTN-HEIGHT-MEDIUM: 40;
  --weui-BTN-HEIGHT-SMALL: 32;
}
.wx-root,
body {
  --weui-BTN-ACTIVE-MASK: rgba(0, 0, 0, 0.1);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BTN-ACTIVE-MASK: hsla(0, 0%, 100%, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BTN-ACTIVE-MASK: hsla(0, 0%, 100%, 0.1);
  }
}
.wx-root,
body {
  --weui-BTN-DEFAULT-ACTIVE-BG: #e6e6e6;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, 0.126);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, 0.126);
  }
}
.wx-root,
body {
  --weui-DIALOG-LINE-COLOR: rgba(0, 0, 0, 0.1);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, 0.1);
  }
}
.weui-flex {
  display: flex;
}
.weui-flex__item {
  flex: 1;
}
.wx-root,
body {
  --weui-BG-COLOR-ACTIVE: #ececec;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BG-COLOR-ACTIVE: #373737;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-COLOR-ACTIVE: #373737;
  }
}
[class*=' weui-icon-'][class*=' weui-icon-'],
[class*=' weui-icon-'][class^='weui-icon-'],
[class^='weui-icon-'][class*=' weui-icon-'],
[class^='weui-icon-'][class^='weui-icon-'] {
  display: inline-block;
  vertical-align: middle;
  font-size: 10px;
  width: 2.4em;
  height: 2.4em;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor;
}
.weui-icon-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='1000' height='1000' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M500 916.667C269.881 916.667 83.333 730.119 83.333 500 83.333 269.881 269.881 83.333 500 83.333c230.119 0 416.667 186.548 416.667 416.667 0 230.119-186.548 416.667-416.667 416.667zm0-50c202.504 0 366.667-164.163 366.667-366.667 0-202.504-164.163-366.667-366.667-366.667-202.504 0-366.667 164.163-366.667 366.667 0 202.504 164.163 366.667 366.667 366.667z' fill-rule='evenodd' fill-opacity='.9'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='1000' height='1000' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M500 916.667C269.881 916.667 83.333 730.119 83.333 500 83.333 269.881 269.881 83.333 500 83.333c230.119 0 416.667 186.548 416.667 416.667 0 230.119-186.548 416.667-416.667 416.667zm0-50c202.504 0 366.667-164.163 366.667-366.667 0-202.504-164.163-366.667-366.667-366.667-202.504 0-366.667 164.163-366.667 366.667 0 202.504 164.163 366.667 366.667 366.667z' fill-rule='evenodd' fill-opacity='.9'/%3E%3C/svg%3E");
}
.weui-icon-download {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.25 12.04l-1.72-1.72-1.06 1.06 2.828 2.83a1 1 0 001.414-.001l2.828-2.828-1.06-1.061-1.73 1.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55 0 .999.446.999.996v13.008a.998.998 0 01-.996.996H4.996A.998.998 0 014 21.004V7.996A1 1 0 014.999 7h6.251z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.25 12.04l-1.72-1.72-1.06 1.06 2.828 2.83a1 1 0 001.414-.001l2.828-2.828-1.06-1.061-1.73 1.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55 0 .999.446.999.996v13.008a.998.998 0 01-.996.996H4.996A.998.998 0 014 21.004V7.996A1 1 0 014.999 7h6.251z'/%3E%3C/svg%3E");
}
.weui-icon-info {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.75-12v7h1.5v-7h-1.5zM12 9a1 1 0 100-2 1 1 0 000 2z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.75-12v7h1.5v-7h-1.5zM12 9a1 1 0 100-2 1 1 0 000 2z'/%3E%3C/svg%3E");
}
.weui-icon-safe-success {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.6C315.5 46.7 180.4 93.1 57.6 132c0 129.3.2 231.7.2 339.7 0 304.2 248.3 471.6 443.1 523.7C695.7 943.3 944 775.9 944 471.7c0-108 .2-210.4.2-339.7C821.4 93.1 686.3 46.7 500.9 4.6zm248.3 349.1l-299.7 295c-2.1 2-5.3 2-7.4-.1L304.4 506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3 5-2.8 7.2-1l112.2 86c2.3 1.8 6 1.7 8.1-.1l274.7-228.9c2.2-1.8 5.7-1.7 7.7.3l17 16.8c2.2 2.1 2.2 5.3.2 7.4z' fill-rule='evenodd' clip-rule='evenodd' fill='%23070202'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.6C315.5 46.7 180.4 93.1 57.6 132c0 129.3.2 231.7.2 339.7 0 304.2 248.3 471.6 443.1 523.7C695.7 943.3 944 775.9 944 471.7c0-108 .2-210.4.2-339.7C821.4 93.1 686.3 46.7 500.9 4.6zm248.3 349.1l-299.7 295c-2.1 2-5.3 2-7.4-.1L304.4 506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3 5-2.8 7.2-1l112.2 86c2.3 1.8 6 1.7 8.1-.1l274.7-228.9c2.2-1.8 5.7-1.7 7.7.3l17 16.8c2.2 2.1 2.2 5.3.2 7.4z' fill-rule='evenodd' clip-rule='evenodd' fill='%23070202'/%3E%3C/svg%3E");
}
.weui-icon-safe-warn {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.5c-185.4 42-320.4 88.4-443.2 127.3 0 129.3.2 231.7.2 339.6 0 304.1 248.2 471.4 443 523.6 194.7-52.2 443-219.5 443-523.6 0-107.9.2-210.3.2-339.6C821.3 92.9 686.2 46.5 500.9 4.5zm-26.1 271.1h52.1c5.8 0 10.3 4.7 10.1 10.4l-11.6 313.8c-.1 2.8-2.5 5.2-5.4 5.2h-38.2c-2.9 0-5.3-2.3-5.4-5.2L464.8 286c-.2-5.8 4.3-10.4 10-10.4zm26.1 448.3c-20.2 0-36.5-16.3-36.5-36.5s16.3-36.5 36.5-36.5 36.5 16.3 36.5 36.5-16.4 36.5-36.5 36.5z' fill-rule='evenodd' clip-rule='evenodd' fill='%23020202'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.5c-185.4 42-320.4 88.4-443.2 127.3 0 129.3.2 231.7.2 339.6 0 304.1 248.2 471.4 443 523.6 194.7-52.2 443-219.5 443-523.6 0-107.9.2-210.3.2-339.6C821.3 92.9 686.2 46.5 500.9 4.5zm-26.1 271.1h52.1c5.8 0 10.3 4.7 10.1 10.4l-11.6 313.8c-.1 2.8-2.5 5.2-5.4 5.2h-38.2c-2.9 0-5.3-2.3-5.4-5.2L464.8 286c-.2-5.8 4.3-10.4 10-10.4zm26.1 448.3c-20.2 0-36.5-16.3-36.5-36.5s16.3-36.5 36.5-36.5 36.5 16.3 36.5 36.5-16.4 36.5-36.5 36.5z' fill-rule='evenodd' clip-rule='evenodd' fill='%23020202'/%3E%3C/svg%3E");
}
.weui-icon-success {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1.177-7.86l-2.765-2.767L7 12.431l3.119 3.121a1 1 0 001.414 0l5.952-5.95-1.062-1.062-5.6 5.6z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1.177-7.86l-2.765-2.767L7 12.431l3.119 3.121a1 1 0 001.414 0l5.952-5.95-1.062-1.062-5.6 5.6z'/%3E%3C/svg%3E");
}
.weui-icon-success-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm-1.172-6.242l5.809-5.808.848.849-5.95 5.95a1 1 0 01-1.414 0L7 12.426l.849-.849 2.98 2.98z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm-1.172-6.242l5.809-5.808.848.849-5.95 5.95a1 1 0 01-1.414 0L7 12.426l.849-.849 2.98 2.98z'/%3E%3C/svg%3E");
}
.weui-icon-success-no-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.657 18.435L3 12.778l1.414-1.414 4.95 4.95L20.678 5l1.414 1.414-12.02 12.021a1 1 0 01-1.415 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.657 18.435L3 12.778l1.414-1.414 4.95 4.95L20.678 5l1.414 1.414-12.02 12.021a1 1 0 01-1.415 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-waiting {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.75 11.38V6h-1.5v6l4.243 4.243 1.06-1.06-3.803-3.804zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.75 11.38V6h-1.5v6l4.243 4.243 1.06-1.06-3.803-3.804zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-waiting-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.6 11.503l3.891 3.891-.848.849L11.4 12V6h1.2v5.503zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.6 11.503l3.891 3.891-.848.849L11.4 12V6h1.2v5.503zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z'/%3E%3C/svg%3E");
}
.weui-icon-warn {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.763-15.864l.11 7.596h1.305l.11-7.596h-1.525zm.759 10.967c.512 0 .902-.383.902-.882 0-.5-.39-.882-.902-.882a.878.878 0 00-.896.882c0 .499.396.882.896.882z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.763-15.864l.11 7.596h1.305l.11-7.596h-1.525zm.759 10.967c.512 0 .902-.383.902-.882 0-.5-.39-.882-.902-.882a.878.878 0 00-.896.882c0 .499.396.882.896.882z'/%3E%3C/svg%3E");
}
.weui-icon-outlined-warn {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12zm18.8 0a8.8 8.8 0 11-17.6 0 8.8 8.8 0 0117.6 0zm-8.14-5.569l-.089 7.06H11.43l-.088-7.06h1.318zm-1.495 9.807c0 .469.366.835.835.835a.82.82 0 00.835-.835.817.817 0 00-.835-.835.821.821 0 00-.835.835z' fill='%23000'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12zm18.8 0a8.8 8.8 0 11-17.6 0 8.8 8.8 0 0117.6 0zm-8.14-5.569l-.089 7.06H11.43l-.088-7.06h1.318zm-1.495 9.807c0 .469.366.835.835.835a.82.82 0 00.835-.835.817.817 0 00-.835-.835.821.821 0 00-.835.835z' fill='%23000'/%3E%3C/svg%3E");
}
.weui-icon-info-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zM11.4 10h1.2v7h-1.2v-7zm.6-1a1 1 0 110-2 1 1 0 010 2z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zM11.4 10h1.2v7h-1.2v-7zm.6-1a1 1 0 110-2 1 1 0 010 2z'/%3E%3C/svg%3E");
}
.weui-icon-cancel {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z' fill-rule='nonzero'/%3E%3Cpath d='M12.849 12l3.11 3.111-.848.849L12 12.849l-3.111 3.11-.849-.848L11.151 12l-3.11-3.111.848-.849L12 11.151l3.111-3.11.849.848L12.849 12z'/%3E%3C/g%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z' fill-rule='nonzero'/%3E%3Cpath d='M12.849 12l3.11 3.111-.848.849L12 12.849l-3.111 3.11-.849-.848L11.151 12l-3.11-3.111.848-.849L12 11.151l3.111-3.11.849.848L12.849 12z'/%3E%3C/g%3E%3C/svg%3E");
}
.weui-icon-search {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.31 15.561l4.114 4.115-.848.848-4.123-4.123a7 7 0 11.857-.84zM16.8 11a5.8 5.8 0 10-11.6 0 5.8 5.8 0 0011.6 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.31 15.561l4.114 4.115-.848.848-4.123-4.123a7 7 0 11.857-.84zM16.8 11a5.8 5.8 0 10-11.6 0 5.8 5.8 0 0011.6 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-clear {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.06 12l3.006-3.005-1.06-1.06L12 10.938 8.995 7.934l-1.06 1.06L10.938 12l-3.005 3.005 1.06 1.06L12 13.062l3.005 3.005 1.06-1.06L13.062 12zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.06 12l3.006-3.005-1.06-1.06L12 10.938 8.995 7.934l-1.06 1.06L10.938 12l-3.005 3.005 1.06 1.06L12 13.062l3.005 3.005 1.06-1.06L13.062 12zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z'/%3E%3C/svg%3E");
}
.weui-icon-back {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1.999-6.563L10.68 12 14 8.562 12.953 7.5 9.29 11.277a1.045 1.045 0 000 1.446l3.663 3.777L14 15.437z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1.999-6.563L10.68 12 14 8.562 12.953 7.5 9.29 11.277a1.045 1.045 0 000 1.446l3.663 3.777L14 15.437z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-delete {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.774 6.4l.812 13.648a.8.8 0 00.798.752h7.232a.8.8 0 00.798-.752L17.226 6.4H6.774zm11.655 0l-.817 13.719A2 2 0 0115.616 22H8.384a2 2 0 01-1.996-1.881L5.571 6.4H3.5v-.7a.5.5 0 01.5-.5h16a.5.5 0 01.5.5v.7h-2.071zM14 3a.5.5 0 01.5.5v.7h-5v-.7A.5.5 0 0110 3h4zM9.5 9h1.2l.5 9H10l-.5-9zm3.8 0h1.2l-.5 9h-1.2l.5-9z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.774 6.4l.812 13.648a.8.8 0 00.798.752h7.232a.8.8 0 00.798-.752L17.226 6.4H6.774zm11.655 0l-.817 13.719A2 2 0 0115.616 22H8.384a2 2 0 01-1.996-1.881L5.571 6.4H3.5v-.7a.5.5 0 01.5-.5h16a.5.5 0 01.5.5v.7h-2.071zM14 3a.5.5 0 01.5.5v.7h-5v-.7A.5.5 0 0110 3h4zM9.5 9h1.2l.5 9H10l-.5-9zm3.8 0h1.2l-.5 9h-1.2l.5-9z'/%3E%3C/svg%3E");
}
.weui-icon-success-no-circle-thin {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.864 16.617l-5.303-5.303-1.061 1.06 5.657 5.657a1 1 0 001.414 0L21.238 6.364l-1.06-1.06L8.864 16.616z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.864 16.617l-5.303-5.303-1.061 1.06 5.657 5.657a1 1 0 001.414 0L21.238 6.364l-1.06-1.06L8.864 16.616z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-arrow {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.454 6.58l1.06-1.06 5.78 5.779a.996.996 0 010 1.413l-5.78 5.779-1.06-1.061 5.425-5.425-5.425-5.424z' fill='%23B2B2B2' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.454 6.58l1.06-1.06 5.78 5.779a.996.996 0 010 1.413l-5.78 5.779-1.06-1.061 5.425-5.425-5.425-5.424z' fill='%23B2B2B2' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-arrow-bold {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg height='24' width='12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.157 12.711L4.5 18.368l-1.414-1.414 4.95-4.95-4.95-4.95L4.5 5.64l5.657 5.657a1 1 0 010 1.414z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg height='24' width='12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.157 12.711L4.5 18.368l-1.414-1.414 4.95-4.95-4.95-4.95L4.5 5.64l5.657 5.657a1 1 0 010 1.414z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-back-arrow {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.343 12l7.071 7.071L9 20.485l-7.778-7.778a1 1 0 010-1.414L9 3.515l1.414 1.414L3.344 12z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.343 12l7.071 7.071L9 20.485l-7.778-7.778a1 1 0 010-1.414L9 3.515l1.414 1.414L3.344 12z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-back-arrow-thin {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 010-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 010-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-close {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5l-6.193 6.193z' fill='%23000'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5l-6.193 6.193z' fill='%23000'/%3E%3C/svg%3E");
}
.weui-icon-close-thin {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-back-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm1.999-5.363L12.953 16.5 9.29 12.723a1.045 1.045 0 010-1.446L12.953 7.5 14 8.563 10.68 12 14 15.438z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm1.999-5.363L12.953 16.5 9.29 12.723a1.045 1.045 0 010-1.446L12.953 7.5 14 8.563 10.68 12 14 15.438z'/%3E%3C/svg%3E");
}
.weui-icon-success {
  color: var(--weui-BRAND);
}
.weui-icon-waiting {
  color: var(--weui-BLUE);
}
.weui-icon-warn {
  color: var(--weui-RED);
}
.weui-icon-info {
  color: var(--weui-BLUE);
}
.weui-icon-success-circle,
.weui-icon-success-no-circle,
.weui-icon-success-no-circle-thin {
  color: var(--weui-BRAND);
}
.weui-icon-waiting-circle {
  color: var(--weui-BLUE);
}
.weui-icon-circle {
  color: var(--weui-FG-2);
}
.weui-icon-download {
  color: var(--weui-BRAND);
}
.weui-icon-info-circle {
  color: var(--weui-FG-2);
}
.weui-icon-safe-success {
  color: var(--weui-BRAND);
}
.weui-icon-safe-warn {
  color: var(--weui-YELLOW);
}
.weui-icon-cancel {
  color: var(--weui-RED);
}
.weui-icon-search {
  color: var(--weui-FG-1);
}
.weui-icon-clear {
  color: var(--weui-FG-2);
}
.weui-icon-clear:active {
  color: var(--weui-FG-1);
}
.weui-icon-delete.weui-icon_gallery-delete {
  color: var(--weui-WHITE);
}
.weui-icon-arrow-bold.weui-icon-arrow,
.weui-icon-arrow-bold.weui-icon-arrow-bold,
.weui-icon-arrow-bold.weui-icon-back-arrow,
.weui-icon-arrow-bold.weui-icon-back-arrow-thin,
.weui-icon-arrow.weui-icon-arrow,
.weui-icon-arrow.weui-icon-arrow-bold,
.weui-icon-arrow.weui-icon-back-arrow,
.weui-icon-arrow.weui-icon-back-arrow-thin,
.weui-icon-back-arrow-thin.weui-icon-arrow,
.weui-icon-back-arrow-thin.weui-icon-arrow-bold,
.weui-icon-back-arrow-thin.weui-icon-back-arrow,
.weui-icon-back-arrow-thin.weui-icon-back-arrow-thin,
.weui-icon-back-arrow.weui-icon-arrow,
.weui-icon-back-arrow.weui-icon-arrow-bold,
.weui-icon-back-arrow.weui-icon-back-arrow,
.weui-icon-back-arrow.weui-icon-back-arrow-thin {
  width: 1.2em;
}
.weui-icon-arrow,
.weui-icon-arrow-bold {
  color: var(--weui-FG-2);
}
.weui-icon-back,
.weui-icon-back-arrow,
.weui-icon-back-arrow-thin,
.weui-icon-back-circle {
  color: var(--weui-FG-0);
}
.weui-icon_msg.weui-icon_msg {
  width: 6.4em;
  height: 6.4em;
}
.weui-icon_msg.weui-icon_msg.weui-icon-warn {
  color: var(--weui-RED);
}
.weui-icon_msg.weui-icon_msg.weui-icon-info-circle {
  color: var(--weui-BLUE);
}
.weui-icon_msg-primary.weui-icon_msg-primary {
  width: 6.4em;
  height: 6.4em;
}
.weui-icon_msg-primary.weui-icon_msg-primary.weui-icon-warn {
  color: var(--weui-YELLOW);
}
.wx-root,
body {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #3fbeff;
  --weui-BLUE-170: #b7e6ff;
  --weui-BLUE-80: #0c8bcc;
  --weui-BLUE-90: #0e9ce6;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #5aafe4;
  --weui-BLUE-BG-130: #7fc0ea;
  --weui-BLUE-BG-90: #4095cb;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #38cd7f;
  --weui-BRAND-170: #b4ecce;
  --weui-BRAND-80: #059a4c;
  --weui-BRAND-90: #06ae56;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #3eb575;
  --weui-BRAND-BG-130: #69c694;
  --weui-BRAND-BG-90: #259c5c;
  --weui-FG-0: rgba(0, 0, 0, 0.9);
  --weui-FG-0_5: rgba(0, 0, 0, 0.9);
  --weui-FG-1: rgba(0, 0, 0, 0.55);
  --weui-FG-2: rgba(0, 0, 0, 0.3);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-FG-4: rgba(0, 0, 0, 0.15);
  --weui-GLYPH-0: rgba(0, 0, 0, 0.9);
  --weui-GLYPH-1: rgba(0, 0, 0, 0.55);
  --weui-GLYPH-2: rgba(0, 0, 0, 0.3);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #91d300;
  --weui-GREEN-120: #a7db33;
  --weui-GREEN-170: #def1b3;
  --weui-GREEN-80: #74a800;
  --weui-GREEN-90: #82bd00;
  --weui-GREEN-BG-100: #96be40;
  --weui-GREEN-BG-110: #a0c452;
  --weui-GREEN-BG-130: #b5d179;
  --weui-GREEN-BG-90: #86aa39;
  --weui-INDIGO-100: #1485ee;
  --weui-INDIGO-120: #439df1;
  --weui-INDIGO-170: #b8daf9;
  --weui-INDIGO-80: #106abe;
  --weui-INDIGO-90: #1277d6;
  --weui-INDIGO-BG-100: #2b77bf;
  --weui-INDIGO-BG-110: #3f84c5;
  --weui-INDIGO-BG-130: #6ba0d2;
  --weui-INDIGO-BG-90: #266aab;
  --weui-LIGHTGREEN-100: #95ec69;
  --weui-LIGHTGREEN-120: #aaef87;
  --weui-LIGHTGREEN-170: #def9d1;
  --weui-LIGHTGREEN-80: #77bc54;
  --weui-LIGHTGREEN-90: #85d35e;
  --weui-LIGHTGREEN-BG-100: #72cf60;
  --weui-LIGHTGREEN-BG-110: #80d370;
  --weui-LIGHTGREEN-BG-130: #9cdd90;
  --weui-LIGHTGREEN-BG-90: #66b956;
  --weui-LINK-100: #576b95;
  --weui-LINK-120: #7888aa;
  --weui-LINK-170: #ccd2de;
  --weui-LINK-80: #455577;
  --weui-LINK-90: #4e6085;
  --weui-LINKFINDER-100: #002666;
  --weui-MATERIAL-ATTACHMENTCOLUMN: hsla(0, 0%, 96.1%, 0.95);
  --weui-MATERIAL-NAVIGATIONBAR: hsla(0, 0%, 92.9%, 0.94);
  --weui-MATERIAL-REGULAR: hsla(0, 0%, 96.9%, 0.3);
  --weui-MATERIAL-THICK: hsla(0, 0%, 96.9%, 0.8);
  --weui-MATERIAL-THIN: hsla(0, 0%, 100%, 0.2);
  --weui-MATERIAL-TOOLBAR: hsla(0, 0%, 96.5%, 0.82);
  --weui-ORANGE-100: #fa9d3b;
  --weui-ORANGE-120: #fbb062;
  --weui-ORANGE-170: #fde1c3;
  --weui-ORANGE-80: #c87d2f;
  --weui-ORANGE-90: #e08c34;
  --weui-ORANGE-BG-100: #ea7800;
  --weui-ORANGE-BG-110: #ec8519;
  --weui-ORANGE-BG-130: #f0a04d;
  --weui-ORANGE-BG-90: #d26b00;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.5);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #6467f0;
  --weui-PURPLE-120: #8385f3;
  --weui-PURPLE-170: #d0d1fa;
  --weui-PURPLE-80: #5052c0;
  --weui-PURPLE-90: #595cd7;
  --weui-PURPLE-BG-100: #6769ba;
  --weui-PURPLE-BG-110: #7678c1;
  --weui-PURPLE-BG-130: #9496ce;
  --weui-PURPLE-BG-90: #5c5ea7;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #fb7373;
  --weui-RED-170: #fdcaca;
  --weui-RED-80: #c84040;
  --weui-RED-90: #e14949;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #d3625a;
  --weui-RED-BG-130: #dd847e;
  --weui-RED-BG-90: #b94840;
  --weui-SECONDARY-BG: rgba(0, 0, 0, 0.05);
  --weui-SEPARATOR-0: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-1: rgba(0, 0, 0, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: rgba(0, 0, 0, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: rgba(0, 0, 0, 0.2);
  --weui-YELLOW-100: #ffc300;
  --weui-YELLOW-120: #ffcf33;
  --weui-YELLOW-170: #ffecb2;
  --weui-YELLOW-80: #cc9c00;
  --weui-YELLOW-90: #e6af00;
  --weui-YELLOW-BG-100: #efb600;
  --weui-YELLOW-BG-110: #f0bd19;
  --weui-YELLOW-BG-130: #f3cc4d;
  --weui-YELLOW-BG-90: #d7a400;
  --weui-FG-HALF: rgba(0, 0, 0, 0.9);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #fa9d3b;
  --weui-YELLOW: #ffc300;
  --weui-GREEN: #91d300;
  --weui-LIGHTGREEN: #95ec69;
  --weui-TEXTGREEN: #06ae56;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1485ee;
  --weui-PURPLE: #6467f0;
  --weui-LINK: #576b95;
  --weui-TAG-TEXT-ORANGE: #fa9d3b;
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-TEXT-BLUE: #10aeff;
  --weui-REDORANGE: #ff6146;
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
  --weui-WHITE: #fff;
  --weui-BG: #fff;
  --weui-FG: #000;
  --weui-FG-5: rgba(0, 0, 0, 0.05);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.8);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
    --weui-FG-1: hsla(0, 0%, 100%, 0.5);
    --weui-FG-2: hsla(0, 0%, 100%, 0.3);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  }
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BG-0: #111;
  --weui-BG-1: #1e1e1e;
  --weui-BG-2: #191919;
  --weui-BG-3: #202020;
  --weui-BG-4: #404040;
  --weui-BG-5: #2c2c2c;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #0c8bcc;
  --weui-BLUE-170: #04344d;
  --weui-BLUE-80: #3fbeff;
  --weui-BLUE-90: #28b6ff;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #4095cb;
  --weui-BLUE-BG-130: #32749e;
  --weui-BLUE-BG-90: #5aafe4;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #059a4c;
  --weui-BRAND-170: #023a1c;
  --weui-BRAND-80: #38cd7f;
  --weui-BRAND-90: #20c770;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #259c5c;
  --weui-BRAND-BG-130: #1d7a48;
  --weui-BRAND-BG-90: #3eb575;
  --weui-FG-0: hsla(0, 0%, 100%, 0.8);
  --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
  --weui-FG-1: hsla(0, 0%, 100%, 0.5);
  --weui-FG-2: hsla(0, 0%, 100%, 0.3);
  --weui-FG-3: hsla(0, 0%, 100%, 0.1);
  --weui-FG-4: hsla(0, 0%, 100%, 0.15);
  --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #74a800;
  --weui-GREEN-120: #5c8600;
  --weui-GREEN-170: #233200;
  --weui-GREEN-80: #8fb933;
  --weui-GREEN-90: #82b01a;
  --weui-GREEN-BG-100: #789833;
  --weui-GREEN-BG-110: #6b882d;
  --weui-GREEN-BG-130: #65802b;
  --weui-GREEN-BG-90: #85a247;
  --weui-INDIGO-100: #1196ff;
  --weui-INDIGO-120: #0d78cc;
  --weui-INDIGO-170: #052d4d;
  --weui-INDIGO-80: #40abff;
  --weui-INDIGO-90: #28a0ff;
  --weui-INDIGO-BG-100: #0d78cc;
  --weui-INDIGO-BG-110: #0b6bb7;
  --weui-INDIGO-BG-130: #09548f;
  --weui-INDIGO-BG-90: #2585d1;
  --weui-LIGHTGREEN-100: #3eb575;
  --weui-LIGHTGREEN-120: #31905d;
  --weui-LIGHTGREEN-170: #123522;
  --weui-LIGHTGREEN-80: #64c390;
  --weui-LIGHTGREEN-90: #51bc83;
  --weui-LIGHTGREEN-BG-100: #31905d;
  --weui-LIGHTGREEN-BG-110: #2c8153;
  --weui-LIGHTGREEN-BG-130: #226541;
  --weui-LIGHTGREEN-BG-90: #31905d;
  --weui-LINK-100: #7d90a9;
  --weui-LINK-120: #647387;
  --weui-LINK-170: #252a32;
  --weui-LINK-80: #97a6ba;
  --weui-LINK-90: #899ab1;
  --weui-LINKFINDER-100: #dee9ff;
  --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
  --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
  --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
  --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
  --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
  --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
  --weui-ORANGE-100: #c87d2f;
  --weui-ORANGE-120: #a06425;
  --weui-ORANGE-170: #3b250e;
  --weui-ORANGE-80: #d39758;
  --weui-ORANGE-90: #cd8943;
  --weui-ORANGE-BG-100: #bb6000;
  --weui-ORANGE-BG-110: #a85600;
  --weui-ORANGE-BG-130: #824300;
  --weui-ORANGE-BG-90: #c1701a;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.8);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #8183ff;
  --weui-PURPLE-120: #6768cc;
  --weui-PURPLE-170: #26274c;
  --weui-PURPLE-80: #9a9bff;
  --weui-PURPLE-90: #8d8fff;
  --weui-PURPLE-BG-100: #6768cc;
  --weui-PURPLE-BG-110: #5c5db7;
  --weui-PURPLE-BG-130: #48498f;
  --weui-PURPLE-BG-90: #7677d1;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #c84040;
  --weui-RED-170: #4b1818;
  --weui-RED-80: #fb7373;
  --weui-RED-90: #fa6262;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #ba4940;
  --weui-RED-BG-130: #913832;
  --weui-RED-BG-90: #d3625a;
  --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
  --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
  --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
  --weui-YELLOW-100: #cc9c00;
  --weui-YELLOW-120: #a37c00;
  --weui-YELLOW-170: #3d2f00;
  --weui-YELLOW-80: #d6af33;
  --weui-YELLOW-90: #d1a519;
  --weui-YELLOW-BG-100: #bf9100;
  --weui-YELLOW-BG-110: #ab8200;
  --weui-YELLOW-BG-130: #866500;
  --weui-YELLOW-BG-90: #c59c1a;
  --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #c87d2f;
  --weui-YELLOW: #cc9c00;
  --weui-GREEN: #74a800;
  --weui-LIGHTGREEN: #3eb575;
  --weui-TEXTGREEN: #259c5c;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1196ff;
  --weui-PURPLE: #8183ff;
  --weui-LINK: #7d90a9;
  --weui-REDORANGE: #ff6146;
  --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
  --weui-WHITE: hsla(0, 0%, 100%, 0.8);
  --weui-FG: #fff;
  --weui-BG: #000;
  --weui-FG-5: hsla(0, 0%, 100%, 0.1);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
  --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
  --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
}
.wx-root[data-weui-mode='care'],
body[data-weui-mode='care'] {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-BLUE-100: #007dbb;
  --weui-BLUE-120: #3fbeff;
  --weui-BLUE-170: #b7e6ff;
  --weui-BLUE-80: #0c8bcc;
  --weui-BLUE-90: #0e9ce6;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #5aafe4;
  --weui-BLUE-BG-130: #7fc0ea;
  --weui-BLUE-BG-90: #4095cb;
  --weui-BRAND-100: #018942;
  --weui-BRAND-120: #38cd7f;
  --weui-BRAND-170: #b4ecce;
  --weui-BRAND-80: #059a4c;
  --weui-BRAND-90: #06ae56;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #3eb575;
  --weui-BRAND-BG-130: #69c694;
  --weui-BRAND-BG-90: #259c5c;
  --weui-FG-0: #000;
  --weui-FG-0_5: #000;
  --weui-FG-1: rgba(0, 0, 0, 0.6);
  --weui-FG-2: rgba(0, 0, 0, 0.42);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-FG-4: rgba(0, 0, 0, 0.15);
  --weui-GLYPH-0: #000;
  --weui-GLYPH-1: rgba(0, 0, 0, 0.6);
  --weui-GLYPH-2: rgba(0, 0, 0, 0.42);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #4f8400;
  --weui-GREEN-120: #a7db33;
  --weui-GREEN-170: #def1b3;
  --weui-GREEN-80: #74a800;
  --weui-GREEN-90: #82bd00;
  --weui-GREEN-BG-100: #96be40;
  --weui-GREEN-BG-110: #a0c452;
  --weui-GREEN-BG-130: #b5d179;
  --weui-GREEN-BG-90: #86aa39;
  --weui-INDIGO-100: #0075e2;
  --weui-INDIGO-120: #439df1;
  --weui-INDIGO-170: #b8daf9;
  --weui-INDIGO-80: #106abe;
  --weui-INDIGO-90: #1277d6;
  --weui-INDIGO-BG-100: #2b77bf;
  --weui-INDIGO-BG-110: #3f84c5;
  --weui-INDIGO-BG-130: #6ba0d2;
  --weui-INDIGO-BG-90: #266aab;
  --weui-LIGHTGREEN-100: #2e8800;
  --weui-LIGHTGREEN-120: #aaef87;
  --weui-LIGHTGREEN-170: #def9d1;
  --weui-LIGHTGREEN-80: #77bc54;
  --weui-LIGHTGREEN-90: #85d35e;
  --weui-LIGHTGREEN-BG-100: #72cf60;
  --weui-LIGHTGREEN-BG-110: #80d370;
  --weui-LIGHTGREEN-BG-130: #9cdd90;
  --weui-LIGHTGREEN-BG-90: #66b956;
  --weui-LINK-100: #576b95;
  --weui-LINK-120: #7888aa;
  --weui-LINK-170: #ccd2de;
  --weui-LINK-80: #455577;
  --weui-LINK-90: #4e6085;
  --weui-LINKFINDER-100: #002666;
  --weui-MATERIAL-ATTACHMENTCOLUMN: hsla(0, 0%, 96.1%, 0.95);
  --weui-MATERIAL-NAVIGATIONBAR: hsla(0, 0%, 92.9%, 0.94);
  --weui-MATERIAL-REGULAR: hsla(0, 0%, 96.9%, 0.3);
  --weui-MATERIAL-THICK: hsla(0, 0%, 96.9%, 0.8);
  --weui-MATERIAL-THIN: hsla(0, 0%, 100%, 0.2);
  --weui-MATERIAL-TOOLBAR: hsla(0, 0%, 96.5%, 0.82);
  --weui-ORANGE-100: #e17719;
  --weui-ORANGE-120: #fbb062;
  --weui-ORANGE-170: #fde1c3;
  --weui-ORANGE-80: #c87d2f;
  --weui-ORANGE-90: #e08c34;
  --weui-ORANGE-BG-100: #ea7800;
  --weui-ORANGE-BG-110: #ec8519;
  --weui-ORANGE-BG-130: #f0a04d;
  --weui-ORANGE-BG-90: #d26b00;
  --weui-ORANGERED-100: #d14730;
  --weui-OVERLAY: rgba(0, 0, 0, 0.5);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #6265f1;
  --weui-PURPLE-120: #8385f3;
  --weui-PURPLE-170: #d0d1fa;
  --weui-PURPLE-80: #5052c0;
  --weui-PURPLE-90: #595cd7;
  --weui-PURPLE-BG-100: #6769ba;
  --weui-PURPLE-BG-110: #7678c1;
  --weui-PURPLE-BG-130: #9496ce;
  --weui-PURPLE-BG-90: #5c5ea7;
  --weui-RED-100: #dc3636;
  --weui-RED-120: #fb7373;
  --weui-RED-170: #fdcaca;
  --weui-RED-80: #c84040;
  --weui-RED-90: #e14949;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #d3625a;
  --weui-RED-BG-130: #dd847e;
  --weui-RED-BG-90: #b94840;
  --weui-SECONDARY-BG: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-0: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-1: rgba(0, 0, 0, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: rgba(0, 0, 0, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: rgba(0, 0, 0, 0.2);
  --weui-YELLOW-100: #bb8e00;
  --weui-YELLOW-120: #ffcf33;
  --weui-YELLOW-170: #ffecb2;
  --weui-YELLOW-80: #cc9c00;
  --weui-YELLOW-90: #e6af00;
  --weui-YELLOW-BG-100: #efb600;
  --weui-YELLOW-BG-110: #f0bd19;
  --weui-YELLOW-BG-130: #f3cc4d;
  --weui-YELLOW-BG-90: #d7a400;
  --weui-FG-HALF: #000;
  --weui-RED: #dc3636;
  --weui-ORANGERED: #d14730;
  --weui-ORANGE: #e17719;
  --weui-YELLOW: #bb8e00;
  --weui-GREEN: #4f8400;
  --weui-LIGHTGREEN: #2e8800;
  --weui-TEXTGREEN: #06ae56;
  --weui-BRAND: #018942;
  --weui-BLUE: #007dbb;
  --weui-INDIGO: #0075e2;
  --weui-PURPLE: #6265f1;
  --weui-LINK: #576b95;
  --weui-TAG-TEXT-ORANGE: #e17719;
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-TEXT-BLUE: #007dbb;
  --weui-REDORANGE: #d14730;
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-WHITE: #fff;
  --weui-BG: #fff;
  --weui-FG: #000;
  --weui-FG-5: rgba(0, 0, 0, 0.05);
  --weui-TAG-BACKGROUND-ORANGE: rgba(225, 119, 25, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(0, 125, 187, 0.1);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
}
@media (prefers-color-scheme: dark) {
  .wx-root[data-weui-mode='care']:not([data-weui-theme='light']),
  body[data-weui-mode='care']:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.85);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
    --weui-FG-1: hsla(0, 0%, 100%, 0.55);
    --weui-FG-2: hsla(0, 0%, 100%, 0.35);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-FG: #fff;
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-BG: #000;
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  }
}
.wx-root[data-weui-mode='care'][data-weui-theme='dark'],
body[data-weui-mode='care'][data-weui-theme='dark'] {
  --weui-BG-0: #111;
  --weui-BG-1: #1e1e1e;
  --weui-BG-2: #191919;
  --weui-BG-3: #202020;
  --weui-BG-4: #404040;
  --weui-BG-5: #2c2c2c;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #0c8bcc;
  --weui-BLUE-170: #04344d;
  --weui-BLUE-80: #3fbeff;
  --weui-BLUE-90: #28b6ff;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #4095cb;
  --weui-BLUE-BG-130: #32749e;
  --weui-BLUE-BG-90: #5aafe4;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #059a4c;
  --weui-BRAND-170: #023a1c;
  --weui-BRAND-80: #38cd7f;
  --weui-BRAND-90: #20c770;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #259c5c;
  --weui-BRAND-BG-130: #1d7a48;
  --weui-BRAND-BG-90: #3eb575;
  --weui-FG-0: hsla(0, 0%, 100%, 0.85);
  --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
  --weui-FG-1: hsla(0, 0%, 100%, 0.55);
  --weui-FG-2: hsla(0, 0%, 100%, 0.35);
  --weui-FG-3: hsla(0, 0%, 100%, 0.1);
  --weui-FG-4: hsla(0, 0%, 100%, 0.15);
  --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #74a800;
  --weui-GREEN-120: #5c8600;
  --weui-GREEN-170: #233200;
  --weui-GREEN-80: #8fb933;
  --weui-GREEN-90: #82b01a;
  --weui-GREEN-BG-100: #789833;
  --weui-GREEN-BG-110: #6b882d;
  --weui-GREEN-BG-130: #65802b;
  --weui-GREEN-BG-90: #85a247;
  --weui-INDIGO-100: #1196ff;
  --weui-INDIGO-120: #0d78cc;
  --weui-INDIGO-170: #052d4d;
  --weui-INDIGO-80: #40abff;
  --weui-INDIGO-90: #28a0ff;
  --weui-INDIGO-BG-100: #0d78cc;
  --weui-INDIGO-BG-110: #0b6bb7;
  --weui-INDIGO-BG-130: #09548f;
  --weui-INDIGO-BG-90: #2585d1;
  --weui-LIGHTGREEN-100: #3eb575;
  --weui-LIGHTGREEN-120: #31905d;
  --weui-LIGHTGREEN-170: #123522;
  --weui-LIGHTGREEN-80: #64c390;
  --weui-LIGHTGREEN-90: #51bc83;
  --weui-LIGHTGREEN-BG-100: #31905d;
  --weui-LIGHTGREEN-BG-110: #2c8153;
  --weui-LIGHTGREEN-BG-130: #226541;
  --weui-LIGHTGREEN-BG-90: #31905d;
  --weui-LINK-100: #7d90a9;
  --weui-LINK-120: #647387;
  --weui-LINK-170: #252a32;
  --weui-LINK-80: #97a6ba;
  --weui-LINK-90: #899ab1;
  --weui-LINKFINDER-100: #dee9ff;
  --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
  --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
  --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
  --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
  --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
  --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
  --weui-ORANGE-100: #c87d2f;
  --weui-ORANGE-120: #a06425;
  --weui-ORANGE-170: #3b250e;
  --weui-ORANGE-80: #d39758;
  --weui-ORANGE-90: #cd8943;
  --weui-ORANGE-BG-100: #bb6000;
  --weui-ORANGE-BG-110: #a85600;
  --weui-ORANGE-BG-130: #824300;
  --weui-ORANGE-BG-90: #c1701a;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.8);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #8183ff;
  --weui-PURPLE-120: #6768cc;
  --weui-PURPLE-170: #26274c;
  --weui-PURPLE-80: #9a9bff;
  --weui-PURPLE-90: #8d8fff;
  --weui-PURPLE-BG-100: #6768cc;
  --weui-PURPLE-BG-110: #5c5db7;
  --weui-PURPLE-BG-130: #48498f;
  --weui-PURPLE-BG-90: #7677d1;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #c84040;
  --weui-RED-170: #4b1818;
  --weui-RED-80: #fb7373;
  --weui-RED-90: #fa6262;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #ba4940;
  --weui-RED-BG-130: #913832;
  --weui-RED-BG-90: #d3625a;
  --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
  --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
  --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
  --weui-YELLOW-100: #cc9c00;
  --weui-YELLOW-120: #a37c00;
  --weui-YELLOW-170: #3d2f00;
  --weui-YELLOW-80: #d6af33;
  --weui-YELLOW-90: #d1a519;
  --weui-YELLOW-BG-100: #bf9100;
  --weui-YELLOW-BG-110: #ab8200;
  --weui-YELLOW-BG-130: #866500;
  --weui-YELLOW-BG-90: #c59c1a;
  --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #c87d2f;
  --weui-YELLOW: #cc9c00;
  --weui-GREEN: #74a800;
  --weui-LIGHTGREEN: #3eb575;
  --weui-TEXTGREEN: #259c5c;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1196ff;
  --weui-PURPLE: #8183ff;
  --weui-LINK: #7d90a9;
  --weui-REDORANGE: #ff6146;
  --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
  --weui-FG: #fff;
  --weui-WHITE: hsla(0, 0%, 100%, 0.8);
  --weui-FG-5: hsla(0, 0%, 100%, 0.1);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
  --weui-BG: #000;
  --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
  --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
}
.wx-root {
  pointer-events: auto;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    Helvetica Neue,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei UI,
    Microsoft YaHei,
    Arial,
    sans-serif;
}
.wx-root,
.wx_card_root {
  position: relative;
}
.wxw_hide {
  display: none !important;
}
.wx_uninteractive {
  pointer-events: none;
}
.wx-root,
body {
  --APPMSGCARD-BG: #fafafa;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --APPMSGCARD-BG: #1e1e1e;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --APPMSGCARD-BG: #1e1e1e;
  }
}
.wx-root,
body {
  --APPMSGCARD-LINE-BG: rgba(0, 0, 0, 0.07);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --APPMSGCARD-LINE-BG: hsla(0, 0%, 100%, 0.07);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --APPMSGCARD-LINE-BG: hsla(0, 0%, 100%, 0.07);
  }
}
.appmsg_card_context {
  position: relative;
  background-color: var(--APPMSGCARD-BG);
  border-radius: 8px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
:host(.wx_tap_highlight_active) .wx_tap_link {
  opacity: 0.5;
}
:host(.wx_tap_highlight_active) .wx_tap_card {
  background-color: #f3f3f3;
}
:host(.wx_tap_highlight_active) .wx_tap_cell {
  background-color: rgba(0, 0, 0, 0.05);
}
@media (prefers-color-scheme: dark) {
  :host(.wx_tap_highlight_active) .wx_tap_card {
    background-color: #252525;
  }
  :host(.wx_tap_highlight_active) .wx_tap_cell {
    background-color: hsla(0, 0%, 100%, 0.1);
  }
}
.wx_css_active :active {
  opacity: 0.5;
}
.weui-flex__item {
  min-width: 0;
}
.weui-flex_align-center {
  align-items: center;
}
[tabindex] {
  outline: 0;
}
.wx_hover_card:before {
  border-radius: 8px;
  border: 1px solid rgba(7, 193, 96, 0.3);
}
.wx_hover_card:before,
.wx_selected_card:before {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-sizing: border-box;
  pointer-events: none;
  z-index: 9;
}
.wx_selected_card:before {
  border-radius: 8px;
  border: 1.5px solid #07c160;
  background: rgba(7, 193, 96, 0.1);
}
img {
  pointer-events: none;
}
.wx_profile_card {
  line-height: 1.4;
  text-align: left;
  text-decoration: none;
  clear: both;
  position: relative;
}
.wx_profile_card_ft {
  padding: 8px 16px;
  align-items: center;
  position: relative;
  color: var(--weui-FG-2);
  font-size: 14px;
}
.wx_profile_card_ft:before {
  content: '';
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--APPMSGCARD-LINE-BG);
  color: var(--APPMSGCARD-LINE-BG);
  transform-origin: 0 0;
  transform: scaleY(0.5);
  left: 16px;
  right: 16px;
}
.wx_profile_msg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  background: hsla(0, 0%, 100%, 0.5);
  font-size: 14px;
  font-weight: 400;
}
.common-web {
  margin: 0 auto;
  max-width: 382px;
}
.wx_card_disabled .wx_profile_card_bd {
  filter: blur(2px);
}
.wx_profile {
  padding: 20px 16px;
}
.wx_profile .weui-icon-arrow {
  width: 1em;
  height: 2em;
}
.wx_profile_hd {
  display: flex;
  padding-right: 10px;
}
.wx_profile_hd .wx_profile_avatar {
  width: 44px;
  height: 44px !important;
  border-radius: 100%;
}
.wx_profile_ft {
  padding-left: 10px;
}
.wx_profile_bd {
  align-items: center;
}
.wx_profile_bd > .weui-flex__item {
  padding-right: 16px;
}
.wx_profile_nickname {
  display: block;
  color: var(--weui-FG-0);
  font-size: 17px;
  font-weight: 500;
  line-height: 1.2;
}
.wx_profile_desc,
.wx_profile_nickname {
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}
.wx_profile_desc,
.wx_profile_tips {
  color: var(--weui-FG-1);
  font-size: 14px;
  margin-top: 4px;
}
.wx_profile_tips {
  display: flex;
  min-height: 1.4em;
}
.wx_profile_tips:empty {
  display: none;
}
