import { Node, mergeAttributes } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { ProfileComponent } from './profile-component'
import type { MapOptions } from '../map/common'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    profile: {
      setProfile: (attrs: ProfileAttributes) => ReturnType
    }
  }
}

// <mp-common-profile
//   class="js_uneditable custom_select_card mp_profile_iframe mp_common_widget custom_select_card_selected"
//   data-pluginname="mpprofile"
//   data-id="MzIwOTA2MzYwNA=="
//   data-headimg="http://mmbiz.qpic.cn/sz_mmbiz_png/s37uxK5D5wkopAlrzEDxdhgtHwRWcv1Kgmx4vHROz7g9sg0q3D1r0AHHibdD8IpLqCGvVlGNhoiaicWEETB6Y20Pw/0?wx_fmt=png"
//   data-nickname="Apple"
//   data-alias="Apple"
//   data-signature="Apple 从 1984 年推出 Macintosh 以来，时至今日凭借 iPhone、iPad、Mac、Apple Watch 等引领全球创新。同时四个软件平台和突破性的服务带来所有 Apple 设备间的顺畅使用体验，赋予人们更大的能力。"
//   data-from="0"
//   data-is_biz_ban="0"
//   data-service_type="2"
//   contenteditable="false"
// ></mp-common-profile>

export interface ProfileAttributes {
  pluginname: string
  id: string
  headimg: string
  nickname: string
  alias: string
  signature: string
  from: string
  is_biz_ban: string
  service_type: string
}

export const Profile = Node.create<MapOptions>({
  name: 'profile',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      pluginname: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-pluginname'),
        renderHTML: (attributes) => ({
          'data-pluginname': attributes.pluginname,
        }),
      },
      id: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-id'),
        renderHTML: (attributes) => ({
          'data-id': attributes.id,
        }),
      },
      headimg: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-headimg'),
        renderHTML: (attributes) => ({
          'data-headimg': attributes.headimg,
        }),
      },
      nickname: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-nickname'),
        renderHTML: (attributes) => ({
          'data-nickname': attributes.nickname,
        }),
      },
      alias: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-alias'),
        renderHTML: (attributes) => ({
          'data-alias': attributes.alias,
        }),
      },
      signature: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-signature'),
        renderHTML: (attributes) => ({
          'data-signature': attributes.signature,
        }),
      },
      from: {
        default: '0',
        parseHTML: (element) => element.getAttribute('data-from'),
        renderHTML: (attributes) => ({
          'data-from': attributes.from,
        }),
      },
      is_biz_ban: {
        default: '0',
        parseHTML: (element) => element.getAttribute('data-is_biz_ban'),
        renderHTML: (attributes) => ({
          'data-is_biz_ban': attributes.is_biz_ban,
        }),
      },
      service_type: {
        default: '2',
        parseHTML: (element) => element.getAttribute('data-service_type'),
        renderHTML: (attributes) => ({
          'data-service_type': attributes.service_type,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'mp-common-profile',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'section',
      { class: 'mp_profile_iframe_wrp' },
      ['mp-common-profile', mergeAttributes(HTMLAttributes)],
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(({ node }) => {
      return (
        <NodeViewWrapper as="section" class="channels_iframe_wrp custom_select_card_wrp my-4">
          <ProfileComponent {...(node.attrs as ProfileAttributes)} />
        </NodeViewWrapper>
      )
    })
  },

  addCommands() {
    return {
      setProfile:
        (attrs) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs,
          })
        },
    }
  },
})
