import { mergeAttributes } from '@tiptap/core'
import { Mark } from '@tiptap/react'
import type { MapOptions } from './common'
import { addAttributes } from './common'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    mapText: {
      addMapText: (options: {
        id: string
        name: string
        address: string
        img: string
        longitude: string
        latitude: string
        poiid: string
        type: string
      }) => ReturnType
    }
  }
}

export const MapText = Mark.create<MapOptions>({
  name: 'mapText',

  priority: 1001,

  keepOnSplit: true,

  exitable: true,

  inclusive: false,

  addAttributes() {
    return addAttributes
  },

  parseHTML() {
    return [
      {
        tag: 'a',
        getAttrs: (element) => element.getAttribute('data-type') === '1' && null,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'a',
      mergeAttributes(
        { class: 'js_poi_entry wx_poi_link' },
        this.options.HTMLAttributes,
        HTMLAttributes,
      ),
      HTMLAttributes.name,
    ]
  },

  addCommands() {
    return {
      addMapText:
        (options) =>
        ({ editor, state }) => {
          const mapMark = state.schema.text(options.name, [
            state.schema.marks.mapText.create(options),
          ])
          const tr = editor.state.tr
          tr.insert(editor.state.selection.from, mapMark)
          editor.view.dispatch(tr)
          return true
        },
    }
  },
})
