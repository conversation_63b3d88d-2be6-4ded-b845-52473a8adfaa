import type React from 'react'
import { useRef } from 'react'
import cssText from './card-map.css?inline'
import { useShadowDom } from '@renderer/hooks/useShadowDom'

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'mp-common-poi': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          'data-id'?: string
          'data-name'?: string
          'data-address'?: string
          'data-img'?: string
          'data-longitude'?: string
          'data-latitude'?: string
          'data-poiid'?: string
          'data-type'?: string
          class?: string
        },
        HTMLElement
      >
    }
  }
}

interface MapCardProps {
  id: string
  name: string
  address: string
  img: string
  longitude: string
  latitude: string
  poiid: string
  type: string
}

const MapShadowCard: React.FC<MapCardProps> = ({
  id,
  name,
  address,
  img,
  longitude,
  latitude,
  poiid,
  type,
}) => {
  const shadowRootRef = useRef<HTMLElement>(null)

  const getTemplateHTML = (_id: string, name: string, address: string, img: string) => {
    // name、address、img需要转义
    const escapedName = decodeURIComponent(name)
    const escapedAddress = decodeURIComponent(address)
    const escapedImg = decodeURIComponent(img)
    return `
 <div role="option" class="wx-root wx_tap_card appmsg_card_context wx_poi_card common-poi-web wx_card_root" data-weui-theme="light">
    <div class="wx_poi_title">${escapedName}</div>
    <div class="wx_poi_desc">${escapedAddress}</div>
    <div class="js_nocatch js_noimgpopup js_noimgselected">
      <img src="${escapedImg}" style="width: 100%; height: 100%; object-fit: cover;" />
    </div>
    </div>
  `
  }

  const templateHTML = getTemplateHTML(id, name, address, img)

  useShadowDom({
    ref: shadowRootRef,
    templateHTML,
    cssText,
  })

  return (
    <mp-common-poi
      class="js_editor_mppoi appmsg_poi_iframe custom_select_card js_uneditable mp_common_widget custom_select_card_selected"
      data-id={id}
      data-name={name}
      data-address={address}
      data-img={img}
      data-longitude={longitude}
      data-latitude={latitude}
      data-poiid={poiid}
      data-type={type}
      ref={shadowRootRef}
    />
  )
}

export default MapShadowCard
