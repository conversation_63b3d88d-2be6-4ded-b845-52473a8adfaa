import { Node, mergeAttributes } from '@tiptap/core'
import type { MapOptions } from './common'
import { addAttributes } from './common'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import MapShadowCard from './card-component'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    mapCard: {
      setMapCard: (options: {
        id: string
        name: string
        address: string
        img: string
        longitude: string
        latitude: string
        poiid: string
        type: string
      }) => ReturnType
    }
  }
}

const Component = ({ node }) => {
  return (
    <NodeViewWrapper as="section" className="mp-poi-iframe-wrp custom_select_card_wrp">
      <MapShadowCard {...node.attrs} />
    </NodeViewWrapper>
  )
}

export const MapCard = Node.create<MapOptions>({
  name: 'mapCard',

  group: 'block',

  inline: false,

  atom: true,

  addAttributes() {
    return addAttributes
  },

  parseHTML() {
    return [
      {
        tag: 'mp-common-poi',
        getAttrs: (element) => element.getAttribute('data-type') === '2' && null,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['section', {}, ['mp-common-poi', mergeAttributes(HTMLAttributes)]]
  },

  addCommands() {
    return {
      setMapCard:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },
})
