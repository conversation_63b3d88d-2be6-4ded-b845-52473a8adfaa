import type { Attributes } from '@tiptap/react'

export interface MapOptions {
  HTMLAttributes: Record<string, unknown>
}

export const addAttributes: Attributes = {
  id: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-id'),
    renderHTML: (attributes) => ({
      'data-id': attributes.id,
    }),
  },
  name: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-name'),
    renderHTML: (attributes) => ({
      'data-name': attributes.name,
    }),
  },
  address: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-address'),
    renderHTML: (attributes) => ({
      'data-address': attributes.address,
    }),
  },
  img: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-img'),
    renderHTML: (attributes) => ({
      'data-img': attributes.img,
    }),
  },
  longitude: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-longitude'),
    renderHTML: (attributes) => ({
      'data-longitude': attributes.longitude,
    }),
  },
  latitude: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-latitude'),
    renderHTML: (attributes) => ({
      'data-latitude': attributes.latitude,
    }),
  },
  poiid: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-poiid'),
    renderHTML: (attributes) => ({
      'data-poiid': attributes.poiid,
    }),
  },
  type: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-type'),
    renderHTML: (attributes) => ({
      'data-type': attributes.type,
    }),
  },
}
