import { Node, mergeAttributes, nodeInputRule, nodePasteRule } from '@tiptap/react'

export interface TopicOptions {
  /**
   * HTML attributes to add to the highlight element.
   * @default {}
   * @example { class: 'foo' }
   */
  HTMLAttributes: Record<string, unknown>
}

declare module '@tiptap/react' {
  interface Commands<ReturnType> {
    topic: {
      /**
       * Add a highlight
       */
      addTopic: (attributes: { text: string }) => ReturnType
    }
  }
}

/**
 * Matches a highlight to a #topic  on input.
 */
export const inputRegex = /(#\S+(\s|\n))$/

/**
 * Matches a highlight to a #topic on paste.
 */
export const pasteRegex = /(#\S+\s)/g

export const Topic = Node.create<TopicOptions>({
  name: 'topic',
  group: 'inline',

  inline: true,

  selectable: false,

  atom: true,

  priority: 101,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      // 内容
      text: {
        default: '',
        parseHTML: (element) => element.getAttribute('text'),
        renderHTML: (attributes) => ({
          text: attributes.text,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'topic',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const text = HTMLAttributes.text
    return [
      'topic',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        text: text,
      }),
      '#' + text,
    ]
  },

  // renderText({ node }) {
  //   console.log(node)
  //   return node.attrs.text
  // },

  addCommands() {
    return {
      addTopic:
        (attrs) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs,
          })
        },
    }
  },

  addInputRules() {
    return [
      nodeInputRule({
        find: inputRegex,
        type: this.type,
        getAttributes: (match) => {
          // 去处空格和换行
          return { text: match[1].replace('#', '').trim() }
        },
      }),
    ]
  },

  addPasteRules() {
    return [
      nodePasteRule({
        find: pasteRegex,
        type: this.type,
        getAttributes: (match) => ({
          text: match[1].replace('#', '').trim(),
        }),
      }),
    ]
  },
})
