:host {
  all: initial;
  -webkit-text-size-adjust: inherit;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.8);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
    --weui-FG-1: hsla(0, 0%, 100%, 0.5);
    --weui-FG-2: hsla(0, 0%, 100%, 0.3);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  }
}
@media (prefers-color-scheme: dark) {
  .wx-root[data-weui-mode='care']:not([data-weui-theme='light']),
  body[data-weui-mode='care']:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.85);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
    --weui-FG-1: hsla(0, 0%, 100%, 0.55);
    --weui-FG-2: hsla(0, 0%, 100%, 0.35);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-FG: #fff;
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-BG: #000;
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  }
}
.wx-root,
body,
page {
  --weui-BTN-HEIGHT: 48;
  --weui-BTN-HEIGHT-MEDIUM: 40;
  --weui-BTN-HEIGHT-SMALL: 32;
}
.wx-root,
body {
  --weui-BTN-ACTIVE-MASK: rgba(0, 0, 0, 0.1);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BTN-ACTIVE-MASK: hsla(0, 0%, 100%, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BTN-ACTIVE-MASK: hsla(0, 0%, 100%, 0.1);
  }
}
.wx-root,
body {
  --weui-BTN-DEFAULT-ACTIVE-BG: #e6e6e6;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, 0.126);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, 0.126);
  }
}
.wx-root,
body {
  --weui-DIALOG-LINE-COLOR: rgba(0, 0, 0, 0.1);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, 0.1);
  }
}
.weui-flex {
  display: flex;
}
.weui-flex__item {
  flex: 1;
}
.weui-primary-loading {
  font-size: 16px;
  display: inline-flex;
  position: relative;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  color: #606060;
  animation: circleLoading 1s steps(60) infinite;
}
.weui-primary-loading__dot {
  border-top-right-radius: 100%;
  border-bottom-right-radius: 100%;
}
.weui-primary-loading:after,
.weui-primary-loading:before {
  content: '';
  display: block;
  width: 0.5em;
  height: 1em;
  box-sizing: border-box;
  border: 0.0875em solid;
}
.weui-primary-loading:before {
  border-right-width: 0;
  border-top-left-radius: 1em;
  border-bottom-left-radius: 1em;
  -webkit-mask-image: linear-gradient(180deg, #000 8%, rgba(0, 0, 0, 0.3) 95%);
}
.weui-primary-loading:after {
  border-left-width: 0;
  border-top-right-radius: 1em;
  border-bottom-right-radius: 1em;
  -webkit-mask-image: linear-gradient(180deg, transparent 8%, rgba(0, 0, 0, 0.3) 95%);
}
.weui-primary-loading__dot {
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -0.04375em;
  width: 0.0875em;
  height: 0.0875em;
  border-top-right-radius: 0.0875em;
  border-bottom-right-radius: 0.0875em;
  background: currentColor;
}
@keyframes circleLoading {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.weui-primary-loading_brand {
  color: var(--weui-BRAND);
}
.weui-primary-loading_transparent {
  color: #ededed;
}
.weui-loading {
  font-size: 16px;
  width: 1em;
  height: 1em;
  display: inline-block;
  vertical-align: middle;
  background: transparent
    url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    no-repeat;
  background-size: 100%;
}
.weui-btn_loading.weui-btn_primary .weui-loading,
.weui-loading.weui-icon_toast,
.weui-loading.weui-loading_transparent {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23ededed' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23ededed' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23ededed' offset='0%25'/%3E%3Cstop stop-color='%23ededed' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23ededed' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E");
}
.weui-mask-loading {
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  width: 1em;
  height: 1em;
  -webkit-mask: url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    0 0 no-repeat;
  mask: url("data:image/svg+xml;charset=utf-8,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='94.087%25' y1='0%25' x2='94.087%25' y2='90.559%25' id='a'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.674%25' x2='100%25' y2='90.629%25' id='b'%3E%3Cstop stop-color='%23606060' offset='0%25'/%3E%3Cstop stop-color='%23606060' stop-opacity='.3' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd' opacity='.9'%3E%3Cpath d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z' fill='url(%23a)'/%3E%3Cpath d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z' fill='url(%23b)'/%3E%3Ccircle fill='%23606060' cx='40.5' cy='3.5' r='3.5'/%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E")
    0 0 no-repeat;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  color: #606060;
}
@keyframes weuiLoading {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.wx-root,
body {
  --weui-BG-COLOR-ACTIVE: #ececec;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BG-COLOR-ACTIVE: #373737;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-COLOR-ACTIVE: #373737;
  }
}
[class*=' weui-icon-'][class*=' weui-icon-'],
[class*=' weui-icon-'][class^='weui-icon-'],
[class^='weui-icon-'][class*=' weui-icon-'],
[class^='weui-icon-'][class^='weui-icon-'] {
  display: inline-block;
  vertical-align: middle;
  font-size: 10px;
  width: 2.4em;
  height: 2.4em;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor;
}
.weui-icon-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='1000' height='1000' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M500 916.667C269.881 916.667 83.333 730.119 83.333 500 83.333 269.881 269.881 83.333 500 83.333c230.119 0 416.667 186.548 416.667 416.667 0 230.119-186.548 416.667-416.667 416.667zm0-50c202.504 0 366.667-164.163 366.667-366.667 0-202.504-164.163-366.667-366.667-366.667-202.504 0-366.667 164.163-366.667 366.667 0 202.504 164.163 366.667 366.667 366.667z' fill-rule='evenodd' fill-opacity='.9'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='1000' height='1000' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M500 916.667C269.881 916.667 83.333 730.119 83.333 500 83.333 269.881 269.881 83.333 500 83.333c230.119 0 416.667 186.548 416.667 416.667 0 230.119-186.548 416.667-416.667 416.667zm0-50c202.504 0 366.667-164.163 366.667-366.667 0-202.504-164.163-366.667-366.667-366.667-202.504 0-366.667 164.163-366.667 366.667 0 202.504 164.163 366.667 366.667 366.667z' fill-rule='evenodd' fill-opacity='.9'/%3E%3C/svg%3E");
}
.weui-icon-download {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.25 12.04l-1.72-1.72-1.06 1.06 2.828 2.83a1 1 0 001.414-.001l2.828-2.828-1.06-1.061-1.73 1.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55 0 .999.446.999.996v13.008a.998.998 0 01-.996.996H4.996A.998.998 0 014 21.004V7.996A1 1 0 014.999 7h6.251z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.25 12.04l-1.72-1.72-1.06 1.06 2.828 2.83a1 1 0 001.414-.001l2.828-2.828-1.06-1.061-1.73 1.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55 0 .999.446.999.996v13.008a.998.998 0 01-.996.996H4.996A.998.998 0 014 21.004V7.996A1 1 0 014.999 7h6.251z'/%3E%3C/svg%3E");
}
.weui-icon-info {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.75-12v7h1.5v-7h-1.5zM12 9a1 1 0 100-2 1 1 0 000 2z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.75-12v7h1.5v-7h-1.5zM12 9a1 1 0 100-2 1 1 0 000 2z'/%3E%3C/svg%3E");
}
.weui-icon-safe-success {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.6C315.5 46.7 180.4 93.1 57.6 132c0 129.3.2 231.7.2 339.7 0 304.2 248.3 471.6 443.1 523.7C695.7 943.3 944 775.9 944 471.7c0-108 .2-210.4.2-339.7C821.4 93.1 686.3 46.7 500.9 4.6zm248.3 349.1l-299.7 295c-2.1 2-5.3 2-7.4-.1L304.4 506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3 5-2.8 7.2-1l112.2 86c2.3 1.8 6 1.7 8.1-.1l274.7-228.9c2.2-1.8 5.7-1.7 7.7.3l17 16.8c2.2 2.1 2.2 5.3.2 7.4z' fill-rule='evenodd' clip-rule='evenodd' fill='%23070202'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.6C315.5 46.7 180.4 93.1 57.6 132c0 129.3.2 231.7.2 339.7 0 304.2 248.3 471.6 443.1 523.7C695.7 943.3 944 775.9 944 471.7c0-108 .2-210.4.2-339.7C821.4 93.1 686.3 46.7 500.9 4.6zm248.3 349.1l-299.7 295c-2.1 2-5.3 2-7.4-.1L304.4 506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3 5-2.8 7.2-1l112.2 86c2.3 1.8 6 1.7 8.1-.1l274.7-228.9c2.2-1.8 5.7-1.7 7.7.3l17 16.8c2.2 2.1 2.2 5.3.2 7.4z' fill-rule='evenodd' clip-rule='evenodd' fill='%23070202'/%3E%3C/svg%3E");
}
.weui-icon-safe-warn {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.5c-185.4 42-320.4 88.4-443.2 127.3 0 129.3.2 231.7.2 339.6 0 304.1 248.2 471.4 443 523.6 194.7-52.2 443-219.5 443-523.6 0-107.9.2-210.3.2-339.6C821.3 92.9 686.2 46.5 500.9 4.5zm-26.1 271.1h52.1c5.8 0 10.3 4.7 10.1 10.4l-11.6 313.8c-.1 2.8-2.5 5.2-5.4 5.2h-38.2c-2.9 0-5.3-2.3-5.4-5.2L464.8 286c-.2-5.8 4.3-10.4 10-10.4zm26.1 448.3c-20.2 0-36.5-16.3-36.5-36.5s16.3-36.5 36.5-36.5 36.5 16.3 36.5 36.5-16.4 36.5-36.5 36.5z' fill-rule='evenodd' clip-rule='evenodd' fill='%23020202'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'%3E%3Cpath d='M500.9 4.5c-185.4 42-320.4 88.4-443.2 127.3 0 129.3.2 231.7.2 339.6 0 304.1 248.2 471.4 443 523.6 194.7-52.2 443-219.5 443-523.6 0-107.9.2-210.3.2-339.6C821.3 92.9 686.2 46.5 500.9 4.5zm-26.1 271.1h52.1c5.8 0 10.3 4.7 10.1 10.4l-11.6 313.8c-.1 2.8-2.5 5.2-5.4 5.2h-38.2c-2.9 0-5.3-2.3-5.4-5.2L464.8 286c-.2-5.8 4.3-10.4 10-10.4zm26.1 448.3c-20.2 0-36.5-16.3-36.5-36.5s16.3-36.5 36.5-36.5 36.5 16.3 36.5 36.5-16.4 36.5-36.5 36.5z' fill-rule='evenodd' clip-rule='evenodd' fill='%23020202'/%3E%3C/svg%3E");
}
.weui-icon-success {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1.177-7.86l-2.765-2.767L7 12.431l3.119 3.121a1 1 0 001.414 0l5.952-5.95-1.062-1.062-5.6 5.6z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1.177-7.86l-2.765-2.767L7 12.431l3.119 3.121a1 1 0 001.414 0l5.952-5.95-1.062-1.062-5.6 5.6z'/%3E%3C/svg%3E");
}
.weui-icon-success-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm-1.172-6.242l5.809-5.808.848.849-5.95 5.95a1 1 0 01-1.414 0L7 12.426l.849-.849 2.98 2.98z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm-1.172-6.242l5.809-5.808.848.849-5.95 5.95a1 1 0 01-1.414 0L7 12.426l.849-.849 2.98 2.98z'/%3E%3C/svg%3E");
}
.weui-icon-success-no-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.657 18.435L3 12.778l1.414-1.414 4.95 4.95L20.678 5l1.414 1.414-12.02 12.021a1 1 0 01-1.415 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.657 18.435L3 12.778l1.414-1.414 4.95 4.95L20.678 5l1.414 1.414-12.02 12.021a1 1 0 01-1.415 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-waiting {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.75 11.38V6h-1.5v6l4.243 4.243 1.06-1.06-3.803-3.804zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.75 11.38V6h-1.5v6l4.243 4.243 1.06-1.06-3.803-3.804zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-waiting-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.6 11.503l3.891 3.891-.848.849L11.4 12V6h1.2v5.503zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.6 11.503l3.891 3.891-.848.849L11.4 12V6h1.2v5.503zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z'/%3E%3C/svg%3E");
}
.weui-icon-warn {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.763-15.864l.11 7.596h1.305l.11-7.596h-1.525zm.759 10.967c.512 0 .902-.383.902-.882 0-.5-.39-.882-.902-.882a.878.878 0 00-.896.882c0 .499.396.882.896.882z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.763-15.864l.11 7.596h1.305l.11-7.596h-1.525zm.759 10.967c.512 0 .902-.383.902-.882 0-.5-.39-.882-.902-.882a.878.878 0 00-.896.882c0 .499.396.882.896.882z'/%3E%3C/svg%3E");
}
.weui-icon-outlined-warn {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12zm18.8 0a8.8 8.8 0 11-17.6 0 8.8 8.8 0 0117.6 0zm-8.14-5.569l-.089 7.06H11.43l-.088-7.06h1.318zm-1.495 9.807c0 .469.366.835.835.835a.82.82 0 00.835-.835.817.817 0 00-.835-.835.821.821 0 00-.835.835z' fill='%23000'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12zm18.8 0a8.8 8.8 0 11-17.6 0 8.8 8.8 0 0117.6 0zm-8.14-5.569l-.089 7.06H11.43l-.088-7.06h1.318zm-1.495 9.807c0 .469.366.835.835.835a.82.82 0 00.835-.835.817.817 0 00-.835-.835.821.821 0 00-.835.835z' fill='%23000'/%3E%3C/svg%3E");
}
.weui-icon-info-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zM11.4 10h1.2v7h-1.2v-7zm.6-1a1 1 0 110-2 1 1 0 010 2z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zM11.4 10h1.2v7h-1.2v-7zm.6-1a1 1 0 110-2 1 1 0 010 2z'/%3E%3C/svg%3E");
}
.weui-icon-cancel {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z' fill-rule='nonzero'/%3E%3Cpath d='M12.849 12l3.11 3.111-.848.849L12 12.849l-3.111 3.11-.849-.848L11.151 12l-3.11-3.111.848-.849L12 11.151l3.111-3.11.849.848L12.849 12z'/%3E%3C/g%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6z' fill-rule='nonzero'/%3E%3Cpath d='M12.849 12l3.11 3.111-.848.849L12 12.849l-3.111 3.11-.849-.848L11.151 12l-3.11-3.111.848-.849L12 11.151l3.111-3.11.849.848L12.849 12z'/%3E%3C/g%3E%3C/svg%3E");
}
.weui-icon-search {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.31 15.561l4.114 4.115-.848.848-4.123-4.123a7 7 0 11.857-.84zM16.8 11a5.8 5.8 0 10-11.6 0 5.8 5.8 0 0011.6 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.31 15.561l4.114 4.115-.848.848-4.123-4.123a7 7 0 11.857-.84zM16.8 11a5.8 5.8 0 10-11.6 0 5.8 5.8 0 0011.6 0z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-clear {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.06 12l3.006-3.005-1.06-1.06L12 10.938 8.995 7.934l-1.06 1.06L10.938 12l-3.005 3.005 1.06 1.06L12 13.062l3.005 3.005 1.06-1.06L13.062 12zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.06 12l3.006-3.005-1.06-1.06L12 10.938 8.995 7.934l-1.06 1.06L10.938 12l-3.005 3.005 1.06 1.06L12 13.062l3.005 3.005 1.06-1.06L13.062 12zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z'/%3E%3C/svg%3E");
}
.weui-icon-back {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1.999-6.563L10.68 12 14 8.562 12.953 7.5 9.29 11.277a1.045 1.045 0 000 1.446l3.663 3.777L14 15.437z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1.999-6.563L10.68 12 14 8.562 12.953 7.5 9.29 11.277a1.045 1.045 0 000 1.446l3.663 3.777L14 15.437z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-delete {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.774 6.4l.812 13.648a.8.8 0 00.798.752h7.232a.8.8 0 00.798-.752L17.226 6.4H6.774zm11.655 0l-.817 13.719A2 2 0 0115.616 22H8.384a2 2 0 01-1.996-1.881L5.571 6.4H3.5v-.7a.5.5 0 01.5-.5h16a.5.5 0 01.5.5v.7h-2.071zM14 3a.5.5 0 01.5.5v.7h-5v-.7A.5.5 0 0110 3h4zM9.5 9h1.2l.5 9H10l-.5-9zm3.8 0h1.2l-.5 9h-1.2l.5-9z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.774 6.4l.812 13.648a.8.8 0 00.798.752h7.232a.8.8 0 00.798-.752L17.226 6.4H6.774zm11.655 0l-.817 13.719A2 2 0 0115.616 22H8.384a2 2 0 01-1.996-1.881L5.571 6.4H3.5v-.7a.5.5 0 01.5-.5h16a.5.5 0 01.5.5v.7h-2.071zM14 3a.5.5 0 01.5.5v.7h-5v-.7A.5.5 0 0110 3h4zM9.5 9h1.2l.5 9H10l-.5-9zm3.8 0h1.2l-.5 9h-1.2l.5-9z'/%3E%3C/svg%3E");
}
.weui-icon-success-no-circle-thin {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.864 16.617l-5.303-5.303-1.061 1.06 5.657 5.657a1 1 0 001.414 0L21.238 6.364l-1.06-1.06L8.864 16.616z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.864 16.617l-5.303-5.303-1.061 1.06 5.657 5.657a1 1 0 001.414 0L21.238 6.364l-1.06-1.06L8.864 16.616z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-arrow {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.454 6.58l1.06-1.06 5.78 5.779a.996.996 0 010 1.413l-5.78 5.779-1.06-1.061 5.425-5.425-5.425-5.424z' fill='%23B2B2B2' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.454 6.58l1.06-1.06 5.78 5.779a.996.996 0 010 1.413l-5.78 5.779-1.06-1.061 5.425-5.425-5.425-5.424z' fill='%23B2B2B2' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-arrow-bold {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg height='24' width='12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.157 12.711L4.5 18.368l-1.414-1.414 4.95-4.95-4.95-4.95L4.5 5.64l5.657 5.657a1 1 0 010 1.414z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg height='24' width='12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.157 12.711L4.5 18.368l-1.414-1.414 4.95-4.95-4.95-4.95L4.5 5.64l5.657 5.657a1 1 0 010 1.414z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-back-arrow {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.343 12l7.071 7.071L9 20.485l-7.778-7.778a1 1 0 010-1.414L9 3.515l1.414 1.414L3.344 12z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.343 12l7.071 7.071L9 20.485l-7.778-7.778a1 1 0 010-1.414L9 3.515l1.414 1.414L3.344 12z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-back-arrow-thin {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 010-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 010-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-close {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5l-6.193 6.193z' fill='%23000'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5l-6.193 6.193z' fill='%23000'/%3E%3C/svg%3E");
}
.weui-icon-close-thin {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5z' fill-rule='evenodd'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.25 10.693L6.057 4.5 5 5.557l6.193 6.193L5 17.943 6.057 19l6.193-6.193L18.443 19l1.057-1.057-6.193-6.193L19.5 5.557 18.443 4.5z' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.weui-icon-back-circle {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm1.999-5.363L12.953 16.5 9.29 12.723a1.045 1.045 0 010-1.446L12.953 7.5 14 8.563 10.68 12 14 15.438z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm1.999-5.363L12.953 16.5 9.29 12.723a1.045 1.045 0 010-1.446L12.953 7.5 14 8.563 10.68 12 14 15.438z'/%3E%3C/svg%3E");
}
.weui-icon-success {
  color: var(--weui-BRAND);
}
.weui-icon-waiting {
  color: var(--weui-BLUE);
}
.weui-icon-warn {
  color: var(--weui-RED);
}
.weui-icon-info {
  color: var(--weui-BLUE);
}
.weui-icon-success-circle,
.weui-icon-success-no-circle,
.weui-icon-success-no-circle-thin {
  color: var(--weui-BRAND);
}
.weui-icon-waiting-circle {
  color: var(--weui-BLUE);
}
.weui-icon-circle {
  color: var(--weui-FG-2);
}
.weui-icon-download {
  color: var(--weui-BRAND);
}
.weui-icon-info-circle {
  color: var(--weui-FG-2);
}
.weui-icon-safe-success {
  color: var(--weui-BRAND);
}
.weui-icon-safe-warn {
  color: var(--weui-YELLOW);
}
.weui-icon-cancel {
  color: var(--weui-RED);
}
.weui-icon-search {
  color: var(--weui-FG-1);
}
.weui-icon-clear {
  color: var(--weui-FG-2);
}
.weui-icon-clear:active {
  color: var(--weui-FG-1);
}
.weui-icon-delete.weui-icon_gallery-delete {
  color: var(--weui-WHITE);
}
.weui-icon-arrow-bold.weui-icon-arrow,
.weui-icon-arrow-bold.weui-icon-arrow-bold,
.weui-icon-arrow-bold.weui-icon-back-arrow,
.weui-icon-arrow-bold.weui-icon-back-arrow-thin,
.weui-icon-arrow.weui-icon-arrow,
.weui-icon-arrow.weui-icon-arrow-bold,
.weui-icon-arrow.weui-icon-back-arrow,
.weui-icon-arrow.weui-icon-back-arrow-thin,
.weui-icon-back-arrow-thin.weui-icon-arrow,
.weui-icon-back-arrow-thin.weui-icon-arrow-bold,
.weui-icon-back-arrow-thin.weui-icon-back-arrow,
.weui-icon-back-arrow-thin.weui-icon-back-arrow-thin,
.weui-icon-back-arrow.weui-icon-arrow,
.weui-icon-back-arrow.weui-icon-arrow-bold,
.weui-icon-back-arrow.weui-icon-back-arrow,
.weui-icon-back-arrow.weui-icon-back-arrow-thin {
  width: 1.2em;
}
.weui-icon-arrow,
.weui-icon-arrow-bold {
  color: var(--weui-FG-2);
}
.weui-icon-back,
.weui-icon-back-arrow,
.weui-icon-back-arrow-thin,
.weui-icon-back-circle {
  color: var(--weui-FG-0);
}
.weui-icon_msg.weui-icon_msg {
  width: 6.4em;
  height: 6.4em;
}
.weui-icon_msg.weui-icon_msg.weui-icon-warn {
  color: var(--weui-RED);
}
.weui-icon_msg.weui-icon_msg.weui-icon-info-circle {
  color: var(--weui-BLUE);
}
.weui-icon_msg-primary.weui-icon_msg-primary {
  width: 6.4em;
  height: 6.4em;
}
.weui-icon_msg-primary.weui-icon_msg-primary.weui-icon-warn {
  color: var(--weui-YELLOW);
}
.weui-hidden_abs {
  opacity: 0;
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.weui-a11y_ref {
  display: none;
}
.weui-hidden-space:empty:before {
  content: '\00A0';
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.weui-a11y-combo {
  position: relative;
}
.weui-a11y-combo__helper {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.weui-a11y-combo__content {
  position: relative;
  z-index: 1;
}
.weui-wa-hotarea-el {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 44px;
  min-height: 44px;
  width: 100%;
  height: 100%;
}
.weui-wa-hotarea,
.weui-wa-hotarea-el__wrp,
.weui-wa-hotarea_before {
  position: relative;
}
.weui-wa-hotarea-el__wrp a,
.weui-wa-hotarea-el__wrp button,
.weui-wa-hotarea-el__wrp navigator,
.weui-wa-hotarea_before a,
.weui-wa-hotarea_before button,
.weui-wa-hotarea_before navigator,
.weui-wa-hotarea a,
.weui-wa-hotarea button,
.weui-wa-hotarea navigator {
  position: relative;
  z-index: 1;
}
.weui-wa-hotarea:after,
.weui-wa-hotarea_before:before {
  content: '';
  pointer-events: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 44px;
  min-height: 44px;
  width: 100%;
  height: 100%;
}
.wx-root,
body {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #3fbeff;
  --weui-BLUE-170: #b7e6ff;
  --weui-BLUE-80: #0c8bcc;
  --weui-BLUE-90: #0e9ce6;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #5aafe4;
  --weui-BLUE-BG-130: #7fc0ea;
  --weui-BLUE-BG-90: #4095cb;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #38cd7f;
  --weui-BRAND-170: #b4ecce;
  --weui-BRAND-80: #059a4c;
  --weui-BRAND-90: #06ae56;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #3eb575;
  --weui-BRAND-BG-130: #69c694;
  --weui-BRAND-BG-90: #259c5c;
  --weui-FG-0: rgba(0, 0, 0, 0.9);
  --weui-FG-0_5: rgba(0, 0, 0, 0.9);
  --weui-FG-1: rgba(0, 0, 0, 0.55);
  --weui-FG-2: rgba(0, 0, 0, 0.3);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-FG-4: rgba(0, 0, 0, 0.15);
  --weui-GLYPH-0: rgba(0, 0, 0, 0.9);
  --weui-GLYPH-1: rgba(0, 0, 0, 0.55);
  --weui-GLYPH-2: rgba(0, 0, 0, 0.3);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #91d300;
  --weui-GREEN-120: #a7db33;
  --weui-GREEN-170: #def1b3;
  --weui-GREEN-80: #74a800;
  --weui-GREEN-90: #82bd00;
  --weui-GREEN-BG-100: #96be40;
  --weui-GREEN-BG-110: #a0c452;
  --weui-GREEN-BG-130: #b5d179;
  --weui-GREEN-BG-90: #86aa39;
  --weui-INDIGO-100: #1485ee;
  --weui-INDIGO-120: #439df1;
  --weui-INDIGO-170: #b8daf9;
  --weui-INDIGO-80: #106abe;
  --weui-INDIGO-90: #1277d6;
  --weui-INDIGO-BG-100: #2b77bf;
  --weui-INDIGO-BG-110: #3f84c5;
  --weui-INDIGO-BG-130: #6ba0d2;
  --weui-INDIGO-BG-90: #266aab;
  --weui-LIGHTGREEN-100: #95ec69;
  --weui-LIGHTGREEN-120: #aaef87;
  --weui-LIGHTGREEN-170: #def9d1;
  --weui-LIGHTGREEN-80: #77bc54;
  --weui-LIGHTGREEN-90: #85d35e;
  --weui-LIGHTGREEN-BG-100: #72cf60;
  --weui-LIGHTGREEN-BG-110: #80d370;
  --weui-LIGHTGREEN-BG-130: #9cdd90;
  --weui-LIGHTGREEN-BG-90: #66b956;
  --weui-LINK-100: #576b95;
  --weui-LINK-120: #7888aa;
  --weui-LINK-170: #ccd2de;
  --weui-LINK-80: #455577;
  --weui-LINK-90: #4e6085;
  --weui-LINKFINDER-100: #002666;
  --weui-MATERIAL-ATTACHMENTCOLUMN: hsla(0, 0%, 96.1%, 0.95);
  --weui-MATERIAL-NAVIGATIONBAR: hsla(0, 0%, 92.9%, 0.94);
  --weui-MATERIAL-REGULAR: hsla(0, 0%, 96.9%, 0.3);
  --weui-MATERIAL-THICK: hsla(0, 0%, 96.9%, 0.8);
  --weui-MATERIAL-THIN: hsla(0, 0%, 100%, 0.2);
  --weui-MATERIAL-TOOLBAR: hsla(0, 0%, 96.5%, 0.82);
  --weui-ORANGE-100: #fa9d3b;
  --weui-ORANGE-120: #fbb062;
  --weui-ORANGE-170: #fde1c3;
  --weui-ORANGE-80: #c87d2f;
  --weui-ORANGE-90: #e08c34;
  --weui-ORANGE-BG-100: #ea7800;
  --weui-ORANGE-BG-110: #ec8519;
  --weui-ORANGE-BG-130: #f0a04d;
  --weui-ORANGE-BG-90: #d26b00;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.5);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #6467f0;
  --weui-PURPLE-120: #8385f3;
  --weui-PURPLE-170: #d0d1fa;
  --weui-PURPLE-80: #5052c0;
  --weui-PURPLE-90: #595cd7;
  --weui-PURPLE-BG-100: #6769ba;
  --weui-PURPLE-BG-110: #7678c1;
  --weui-PURPLE-BG-130: #9496ce;
  --weui-PURPLE-BG-90: #5c5ea7;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #fb7373;
  --weui-RED-170: #fdcaca;
  --weui-RED-80: #c84040;
  --weui-RED-90: #e14949;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #d3625a;
  --weui-RED-BG-130: #dd847e;
  --weui-RED-BG-90: #b94840;
  --weui-SECONDARY-BG: rgba(0, 0, 0, 0.05);
  --weui-SEPARATOR-0: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-1: rgba(0, 0, 0, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: rgba(0, 0, 0, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: rgba(0, 0, 0, 0.2);
  --weui-YELLOW-100: #ffc300;
  --weui-YELLOW-120: #ffcf33;
  --weui-YELLOW-170: #ffecb2;
  --weui-YELLOW-80: #cc9c00;
  --weui-YELLOW-90: #e6af00;
  --weui-YELLOW-BG-100: #efb600;
  --weui-YELLOW-BG-110: #f0bd19;
  --weui-YELLOW-BG-130: #f3cc4d;
  --weui-YELLOW-BG-90: #d7a400;
  --weui-FG-HALF: rgba(0, 0, 0, 0.9);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #fa9d3b;
  --weui-YELLOW: #ffc300;
  --weui-GREEN: #91d300;
  --weui-LIGHTGREEN: #95ec69;
  --weui-TEXTGREEN: #06ae56;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1485ee;
  --weui-PURPLE: #6467f0;
  --weui-LINK: #576b95;
  --weui-TAG-TEXT-ORANGE: #fa9d3b;
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-TEXT-BLUE: #10aeff;
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
  --weui-WHITE: #fff;
  --weui-BG: #fff;
  --weui-FG: #000;
  --weui-FG-5: rgba(0, 0, 0, 0.05);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.8);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
    --weui-FG-1: hsla(0, 0%, 100%, 0.5);
    --weui-FG-2: hsla(0, 0%, 100%, 0.3);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  }
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --weui-BG-0: #111;
  --weui-BG-1: #1e1e1e;
  --weui-BG-2: #191919;
  --weui-BG-3: #202020;
  --weui-BG-4: #404040;
  --weui-BG-5: #2c2c2c;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #0c8bcc;
  --weui-BLUE-170: #04344d;
  --weui-BLUE-80: #3fbeff;
  --weui-BLUE-90: #28b6ff;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #4095cb;
  --weui-BLUE-BG-130: #32749e;
  --weui-BLUE-BG-90: #5aafe4;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #059a4c;
  --weui-BRAND-170: #023a1c;
  --weui-BRAND-80: #38cd7f;
  --weui-BRAND-90: #20c770;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #259c5c;
  --weui-BRAND-BG-130: #1d7a48;
  --weui-BRAND-BG-90: #3eb575;
  --weui-FG-0: hsla(0, 0%, 100%, 0.8);
  --weui-FG-0_5: hsla(0, 0%, 100%, 0.6);
  --weui-FG-1: hsla(0, 0%, 100%, 0.5);
  --weui-FG-2: hsla(0, 0%, 100%, 0.3);
  --weui-FG-3: hsla(0, 0%, 100%, 0.1);
  --weui-FG-4: hsla(0, 0%, 100%, 0.15);
  --weui-GLYPH-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.8);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.5);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.3);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #74a800;
  --weui-GREEN-120: #5c8600;
  --weui-GREEN-170: #233200;
  --weui-GREEN-80: #8fb933;
  --weui-GREEN-90: #82b01a;
  --weui-GREEN-BG-100: #789833;
  --weui-GREEN-BG-110: #6b882d;
  --weui-GREEN-BG-130: #65802b;
  --weui-GREEN-BG-90: #85a247;
  --weui-INDIGO-100: #1196ff;
  --weui-INDIGO-120: #0d78cc;
  --weui-INDIGO-170: #052d4d;
  --weui-INDIGO-80: #40abff;
  --weui-INDIGO-90: #28a0ff;
  --weui-INDIGO-BG-100: #0d78cc;
  --weui-INDIGO-BG-110: #0b6bb7;
  --weui-INDIGO-BG-130: #09548f;
  --weui-INDIGO-BG-90: #2585d1;
  --weui-LIGHTGREEN-100: #3eb575;
  --weui-LIGHTGREEN-120: #31905d;
  --weui-LIGHTGREEN-170: #123522;
  --weui-LIGHTGREEN-80: #64c390;
  --weui-LIGHTGREEN-90: #51bc83;
  --weui-LIGHTGREEN-BG-100: #31905d;
  --weui-LIGHTGREEN-BG-110: #2c8153;
  --weui-LIGHTGREEN-BG-130: #226541;
  --weui-LIGHTGREEN-BG-90: #31905d;
  --weui-LINK-100: #7d90a9;
  --weui-LINK-120: #647387;
  --weui-LINK-170: #252a32;
  --weui-LINK-80: #97a6ba;
  --weui-LINK-90: #899ab1;
  --weui-LINKFINDER-100: #dee9ff;
  --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
  --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
  --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
  --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
  --weui-MATERIAL-THIN: rgba(95, 95, 95, 0.4);
  --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
  --weui-ORANGE-100: #c87d2f;
  --weui-ORANGE-120: #a06425;
  --weui-ORANGE-170: #3b250e;
  --weui-ORANGE-80: #d39758;
  --weui-ORANGE-90: #cd8943;
  --weui-ORANGE-BG-100: #bb6000;
  --weui-ORANGE-BG-110: #a85600;
  --weui-ORANGE-BG-130: #824300;
  --weui-ORANGE-BG-90: #c1701a;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.8);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #8183ff;
  --weui-PURPLE-120: #6768cc;
  --weui-PURPLE-170: #26274c;
  --weui-PURPLE-80: #9a9bff;
  --weui-PURPLE-90: #8d8fff;
  --weui-PURPLE-BG-100: #6768cc;
  --weui-PURPLE-BG-110: #5c5db7;
  --weui-PURPLE-BG-130: #48498f;
  --weui-PURPLE-BG-90: #7677d1;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #c84040;
  --weui-RED-170: #4b1818;
  --weui-RED-80: #fb7373;
  --weui-RED-90: #fa6262;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #ba4940;
  --weui-RED-BG-130: #913832;
  --weui-RED-BG-90: #d3625a;
  --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.1);
  --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
  --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
  --weui-YELLOW-100: #cc9c00;
  --weui-YELLOW-120: #a37c00;
  --weui-YELLOW-170: #3d2f00;
  --weui-YELLOW-80: #d6af33;
  --weui-YELLOW-90: #d1a519;
  --weui-YELLOW-BG-100: #bf9100;
  --weui-YELLOW-BG-110: #ab8200;
  --weui-YELLOW-BG-130: #866500;
  --weui-YELLOW-BG-90: #c59c1a;
  --weui-FG-HALF: hsla(0, 0%, 100%, 0.6);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #c87d2f;
  --weui-YELLOW: #cc9c00;
  --weui-GREEN: #74a800;
  --weui-LIGHTGREEN: #3eb575;
  --weui-TEXTGREEN: #259c5c;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1196ff;
  --weui-PURPLE: #8183ff;
  --weui-LINK: #7d90a9;
  --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
  --weui-WHITE: hsla(0, 0%, 100%, 0.8);
  --weui-FG: #fff;
  --weui-BG: #000;
  --weui-FG-5: hsla(0, 0%, 100%, 0.1);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
  --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
  --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
}
.wx-root[data-weui-mode='care'],
body[data-weui-mode='care'] {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-BLUE-100: #007dbb;
  --weui-BLUE-120: #3fbeff;
  --weui-BLUE-170: #b7e6ff;
  --weui-BLUE-80: #0c8bcc;
  --weui-BLUE-90: #0e9ce6;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #5aafe4;
  --weui-BLUE-BG-130: #7fc0ea;
  --weui-BLUE-BG-90: #4095cb;
  --weui-BRAND-100: #018942;
  --weui-BRAND-120: #38cd7f;
  --weui-BRAND-170: #b4ecce;
  --weui-BRAND-80: #059a4c;
  --weui-BRAND-90: #06ae56;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #3eb575;
  --weui-BRAND-BG-130: #69c694;
  --weui-BRAND-BG-90: #259c5c;
  --weui-FG-0: #000;
  --weui-FG-0_5: #000;
  --weui-FG-1: rgba(0, 0, 0, 0.6);
  --weui-FG-2: rgba(0, 0, 0, 0.42);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-FG-4: rgba(0, 0, 0, 0.15);
  --weui-GLYPH-0: #000;
  --weui-GLYPH-1: rgba(0, 0, 0, 0.6);
  --weui-GLYPH-2: rgba(0, 0, 0, 0.42);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #4f8400;
  --weui-GREEN-120: #a7db33;
  --weui-GREEN-170: #def1b3;
  --weui-GREEN-80: #74a800;
  --weui-GREEN-90: #82bd00;
  --weui-GREEN-BG-100: #96be40;
  --weui-GREEN-BG-110: #a0c452;
  --weui-GREEN-BG-130: #b5d179;
  --weui-GREEN-BG-90: #86aa39;
  --weui-INDIGO-100: #0075e2;
  --weui-INDIGO-120: #439df1;
  --weui-INDIGO-170: #b8daf9;
  --weui-INDIGO-80: #106abe;
  --weui-INDIGO-90: #1277d6;
  --weui-INDIGO-BG-100: #2b77bf;
  --weui-INDIGO-BG-110: #3f84c5;
  --weui-INDIGO-BG-130: #6ba0d2;
  --weui-INDIGO-BG-90: #266aab;
  --weui-LIGHTGREEN-100: #2e8800;
  --weui-LIGHTGREEN-120: #aaef87;
  --weui-LIGHTGREEN-170: #def9d1;
  --weui-LIGHTGREEN-80: #77bc54;
  --weui-LIGHTGREEN-90: #85d35e;
  --weui-LIGHTGREEN-BG-100: #72cf60;
  --weui-LIGHTGREEN-BG-110: #80d370;
  --weui-LIGHTGREEN-BG-130: #9cdd90;
  --weui-LIGHTGREEN-BG-90: #66b956;
  --weui-LINK-100: #576b95;
  --weui-LINK-120: #7888aa;
  --weui-LINK-170: #ccd2de;
  --weui-LINK-80: #455577;
  --weui-LINK-90: #4e6085;
  --weui-LINKFINDER-100: #002666;
  --weui-MATERIAL-ATTACHMENTCOLUMN: hsla(0, 0%, 96.1%, 0.95);
  --weui-MATERIAL-NAVIGATIONBAR: hsla(0, 0%, 92.9%, 0.94);
  --weui-MATERIAL-REGULAR: hsla(0, 0%, 96.9%, 0.3);
  --weui-MATERIAL-THICK: hsla(0, 0%, 96.9%, 0.8);
  --weui-MATERIAL-THIN: hsla(0, 0%, 100%, 0.2);
  --weui-MATERIAL-TOOLBAR: hsla(0, 0%, 96.5%, 0.82);
  --weui-ORANGE-100: #e17719;
  --weui-ORANGE-120: #fbb062;
  --weui-ORANGE-170: #fde1c3;
  --weui-ORANGE-80: #c87d2f;
  --weui-ORANGE-90: #e08c34;
  --weui-ORANGE-BG-100: #ea7800;
  --weui-ORANGE-BG-110: #ec8519;
  --weui-ORANGE-BG-130: #f0a04d;
  --weui-ORANGE-BG-90: #d26b00;
  --weui-ORANGERED-100: #d14730;
  --weui-OVERLAY: rgba(0, 0, 0, 0.5);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #6265f1;
  --weui-PURPLE-120: #8385f3;
  --weui-PURPLE-170: #d0d1fa;
  --weui-PURPLE-80: #5052c0;
  --weui-PURPLE-90: #595cd7;
  --weui-PURPLE-BG-100: #6769ba;
  --weui-PURPLE-BG-110: #7678c1;
  --weui-PURPLE-BG-130: #9496ce;
  --weui-PURPLE-BG-90: #5c5ea7;
  --weui-RED-100: #dc3636;
  --weui-RED-120: #fb7373;
  --weui-RED-170: #fdcaca;
  --weui-RED-80: #c84040;
  --weui-RED-90: #e14949;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #d3625a;
  --weui-RED-BG-130: #dd847e;
  --weui-RED-BG-90: #b94840;
  --weui-SECONDARY-BG: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-0: rgba(0, 0, 0, 0.1);
  --weui-SEPARATOR-1: rgba(0, 0, 0, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: rgba(0, 0, 0, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: rgba(0, 0, 0, 0.2);
  --weui-YELLOW-100: #bb8e00;
  --weui-YELLOW-120: #ffcf33;
  --weui-YELLOW-170: #ffecb2;
  --weui-YELLOW-80: #cc9c00;
  --weui-YELLOW-90: #e6af00;
  --weui-YELLOW-BG-100: #efb600;
  --weui-YELLOW-BG-110: #f0bd19;
  --weui-YELLOW-BG-130: #f3cc4d;
  --weui-YELLOW-BG-90: #d7a400;
  --weui-FG-HALF: #000;
  --weui-RED: #dc3636;
  --weui-ORANGERED: #d14730;
  --weui-ORANGE: #e17719;
  --weui-YELLOW: #bb8e00;
  --weui-GREEN: #4f8400;
  --weui-LIGHTGREEN: #2e8800;
  --weui-TEXTGREEN: #06ae56;
  --weui-BRAND: #018942;
  --weui-BLUE: #007dbb;
  --weui-INDIGO: #0075e2;
  --weui-PURPLE: #6265f1;
  --weui-LINK: #576b95;
  --weui-TAG-TEXT-ORANGE: #e17719;
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-TEXT-BLUE: #007dbb;
  --weui-REDORANGE: #d14730;
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-WHITE: #fff;
  --weui-BG: #fff;
  --weui-FG: #000;
  --weui-FG-5: rgba(0, 0, 0, 0.05);
  --weui-TAG-BACKGROUND-ORANGE: rgba(225, 119, 25, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(0, 125, 187, 0.1);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
}
@media (prefers-color-scheme: dark) {
  .wx-root[data-weui-mode='care']:not([data-weui-theme='light']),
  body[data-weui-mode='care']:not([data-weui-theme='light']) {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-BLUE-100: #10aeff;
    --weui-BLUE-120: #0c8bcc;
    --weui-BLUE-170: #04344d;
    --weui-BLUE-80: #3fbeff;
    --weui-BLUE-90: #28b6ff;
    --weui-BLUE-BG-100: #48a6e2;
    --weui-BLUE-BG-110: #4095cb;
    --weui-BLUE-BG-130: #32749e;
    --weui-BLUE-BG-90: #5aafe4;
    --weui-BRAND-100: #07c160;
    --weui-BRAND-120: #059a4c;
    --weui-BRAND-170: #023a1c;
    --weui-BRAND-80: #38cd7f;
    --weui-BRAND-90: #20c770;
    --weui-BRAND-BG-100: #2aae67;
    --weui-BRAND-BG-110: #259c5c;
    --weui-BRAND-BG-130: #1d7a48;
    --weui-BRAND-BG-90: #3eb575;
    --weui-FG-0: hsla(0, 0%, 100%, 0.85);
    --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
    --weui-FG-1: hsla(0, 0%, 100%, 0.55);
    --weui-FG-2: hsla(0, 0%, 100%, 0.35);
    --weui-FG-3: hsla(0, 0%, 100%, 0.1);
    --weui-FG-4: hsla(0, 0%, 100%, 0.15);
    --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
    --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
    --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
    --weui-GLYPH-WHITE-3: #fff;
    --weui-GREEN-100: #74a800;
    --weui-GREEN-120: #5c8600;
    --weui-GREEN-170: #233200;
    --weui-GREEN-80: #8fb933;
    --weui-GREEN-90: #82b01a;
    --weui-GREEN-BG-100: #789833;
    --weui-GREEN-BG-110: #6b882d;
    --weui-GREEN-BG-130: #65802b;
    --weui-GREEN-BG-90: #85a247;
    --weui-INDIGO-100: #1196ff;
    --weui-INDIGO-120: #0d78cc;
    --weui-INDIGO-170: #052d4d;
    --weui-INDIGO-80: #40abff;
    --weui-INDIGO-90: #28a0ff;
    --weui-INDIGO-BG-100: #0d78cc;
    --weui-INDIGO-BG-110: #0b6bb7;
    --weui-INDIGO-BG-130: #09548f;
    --weui-INDIGO-BG-90: #2585d1;
    --weui-LIGHTGREEN-100: #3eb575;
    --weui-LIGHTGREEN-120: #31905d;
    --weui-LIGHTGREEN-170: #123522;
    --weui-LIGHTGREEN-80: #64c390;
    --weui-LIGHTGREEN-90: #51bc83;
    --weui-LIGHTGREEN-BG-100: #31905d;
    --weui-LIGHTGREEN-BG-110: #2c8153;
    --weui-LIGHTGREEN-BG-130: #226541;
    --weui-LIGHTGREEN-BG-90: #31905d;
    --weui-LINK-100: #7d90a9;
    --weui-LINK-120: #647387;
    --weui-LINK-170: #252a32;
    --weui-LINK-80: #97a6ba;
    --weui-LINK-90: #899ab1;
    --weui-LINKFINDER-100: #dee9ff;
    --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
    --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
    --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
    --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
    --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
    --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
    --weui-ORANGE-100: #c87d2f;
    --weui-ORANGE-120: #a06425;
    --weui-ORANGE-170: #3b250e;
    --weui-ORANGE-80: #d39758;
    --weui-ORANGE-90: #cd8943;
    --weui-ORANGE-BG-100: #bb6000;
    --weui-ORANGE-BG-110: #a85600;
    --weui-ORANGE-BG-130: #824300;
    --weui-ORANGE-BG-90: #c1701a;
    --weui-ORANGERED-100: #ff6146;
    --weui-OVERLAY: rgba(0, 0, 0, 0.8);
    --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
    --weui-PURPLE-100: #8183ff;
    --weui-PURPLE-120: #6768cc;
    --weui-PURPLE-170: #26274c;
    --weui-PURPLE-80: #9a9bff;
    --weui-PURPLE-90: #8d8fff;
    --weui-PURPLE-BG-100: #6768cc;
    --weui-PURPLE-BG-110: #5c5db7;
    --weui-PURPLE-BG-130: #48498f;
    --weui-PURPLE-BG-90: #7677d1;
    --weui-RED-100: #fa5151;
    --weui-RED-120: #c84040;
    --weui-RED-170: #4b1818;
    --weui-RED-80: #fb7373;
    --weui-RED-90: #fa6262;
    --weui-RED-BG-100: #cf5148;
    --weui-RED-BG-110: #ba4940;
    --weui-RED-BG-130: #913832;
    --weui-RED-BG-90: #d3625a;
    --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
    --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
    --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
    --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
    --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
    --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
    --weui-YELLOW-100: #cc9c00;
    --weui-YELLOW-120: #a37c00;
    --weui-YELLOW-170: #3d2f00;
    --weui-YELLOW-80: #d6af33;
    --weui-YELLOW-90: #d1a519;
    --weui-YELLOW-BG-100: #bf9100;
    --weui-YELLOW-BG-110: #ab8200;
    --weui-YELLOW-BG-130: #866500;
    --weui-YELLOW-BG-90: #c59c1a;
    --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
    --weui-RED: #fa5151;
    --weui-ORANGERED: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-TEXTGREEN: #259c5c;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-LINK: #7d90a9;
    --weui-REDORANGE: #ff6146;
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
    --weui-FG: #fff;
    --weui-WHITE: hsla(0, 0%, 100%, 0.8);
    --weui-FG-5: hsla(0, 0%, 100%, 0.1);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
    --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
    --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
    --weui-BG: #000;
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
  }
}
.wx-root[data-weui-mode='care'][data-weui-theme='dark'],
body[data-weui-mode='care'][data-weui-theme='dark'] {
  --weui-BG-0: #111;
  --weui-BG-1: #1e1e1e;
  --weui-BG-2: #191919;
  --weui-BG-3: #202020;
  --weui-BG-4: #404040;
  --weui-BG-5: #2c2c2c;
  --weui-BLUE-100: #10aeff;
  --weui-BLUE-120: #0c8bcc;
  --weui-BLUE-170: #04344d;
  --weui-BLUE-80: #3fbeff;
  --weui-BLUE-90: #28b6ff;
  --weui-BLUE-BG-100: #48a6e2;
  --weui-BLUE-BG-110: #4095cb;
  --weui-BLUE-BG-130: #32749e;
  --weui-BLUE-BG-90: #5aafe4;
  --weui-BRAND-100: #07c160;
  --weui-BRAND-120: #059a4c;
  --weui-BRAND-170: #023a1c;
  --weui-BRAND-80: #38cd7f;
  --weui-BRAND-90: #20c770;
  --weui-BRAND-BG-100: #2aae67;
  --weui-BRAND-BG-110: #259c5c;
  --weui-BRAND-BG-130: #1d7a48;
  --weui-BRAND-BG-90: #3eb575;
  --weui-FG-0: hsla(0, 0%, 100%, 0.85);
  --weui-FG-0_5: hsla(0, 0%, 100%, 0.65);
  --weui-FG-1: hsla(0, 0%, 100%, 0.55);
  --weui-FG-2: hsla(0, 0%, 100%, 0.35);
  --weui-FG-3: hsla(0, 0%, 100%, 0.1);
  --weui-FG-4: hsla(0, 0%, 100%, 0.15);
  --weui-GLYPH-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-0: hsla(0, 0%, 100%, 0.85);
  --weui-GLYPH-WHITE-1: hsla(0, 0%, 100%, 0.55);
  --weui-GLYPH-WHITE-2: hsla(0, 0%, 100%, 0.35);
  --weui-GLYPH-WHITE-3: #fff;
  --weui-GREEN-100: #74a800;
  --weui-GREEN-120: #5c8600;
  --weui-GREEN-170: #233200;
  --weui-GREEN-80: #8fb933;
  --weui-GREEN-90: #82b01a;
  --weui-GREEN-BG-100: #789833;
  --weui-GREEN-BG-110: #6b882d;
  --weui-GREEN-BG-130: #65802b;
  --weui-GREEN-BG-90: #85a247;
  --weui-INDIGO-100: #1196ff;
  --weui-INDIGO-120: #0d78cc;
  --weui-INDIGO-170: #052d4d;
  --weui-INDIGO-80: #40abff;
  --weui-INDIGO-90: #28a0ff;
  --weui-INDIGO-BG-100: #0d78cc;
  --weui-INDIGO-BG-110: #0b6bb7;
  --weui-INDIGO-BG-130: #09548f;
  --weui-INDIGO-BG-90: #2585d1;
  --weui-LIGHTGREEN-100: #3eb575;
  --weui-LIGHTGREEN-120: #31905d;
  --weui-LIGHTGREEN-170: #123522;
  --weui-LIGHTGREEN-80: #64c390;
  --weui-LIGHTGREEN-90: #51bc83;
  --weui-LIGHTGREEN-BG-100: #31905d;
  --weui-LIGHTGREEN-BG-110: #2c8153;
  --weui-LIGHTGREEN-BG-130: #226541;
  --weui-LIGHTGREEN-BG-90: #31905d;
  --weui-LINK-100: #7d90a9;
  --weui-LINK-120: #647387;
  --weui-LINK-170: #252a32;
  --weui-LINK-80: #97a6ba;
  --weui-LINK-90: #899ab1;
  --weui-LINKFINDER-100: #dee9ff;
  --weui-MATERIAL-ATTACHMENTCOLUMN: rgba(32, 32, 32, 0.93);
  --weui-MATERIAL-NAVIGATIONBAR: rgba(18, 18, 18, 0.9);
  --weui-MATERIAL-REGULAR: rgba(37, 37, 37, 0.6);
  --weui-MATERIAL-THICK: rgba(34, 34, 34, 0.9);
  --weui-MATERIAL-THIN: hsla(0, 0%, 96.1%, 0.4);
  --weui-MATERIAL-TOOLBAR: rgba(35, 35, 35, 0.93);
  --weui-ORANGE-100: #c87d2f;
  --weui-ORANGE-120: #a06425;
  --weui-ORANGE-170: #3b250e;
  --weui-ORANGE-80: #d39758;
  --weui-ORANGE-90: #cd8943;
  --weui-ORANGE-BG-100: #bb6000;
  --weui-ORANGE-BG-110: #a85600;
  --weui-ORANGE-BG-130: #824300;
  --weui-ORANGE-BG-90: #c1701a;
  --weui-ORANGERED-100: #ff6146;
  --weui-OVERLAY: rgba(0, 0, 0, 0.8);
  --weui-OVERLAY-WHITE: hsla(0, 0%, 94.9%, 0.8);
  --weui-PURPLE-100: #8183ff;
  --weui-PURPLE-120: #6768cc;
  --weui-PURPLE-170: #26274c;
  --weui-PURPLE-80: #9a9bff;
  --weui-PURPLE-90: #8d8fff;
  --weui-PURPLE-BG-100: #6768cc;
  --weui-PURPLE-BG-110: #5c5db7;
  --weui-PURPLE-BG-130: #48498f;
  --weui-PURPLE-BG-90: #7677d1;
  --weui-RED-100: #fa5151;
  --weui-RED-120: #c84040;
  --weui-RED-170: #4b1818;
  --weui-RED-80: #fb7373;
  --weui-RED-90: #fa6262;
  --weui-RED-BG-100: #cf5148;
  --weui-RED-BG-110: #ba4940;
  --weui-RED-BG-130: #913832;
  --weui-RED-BG-90: #d3625a;
  --weui-SECONDARY-BG: hsla(0, 0%, 100%, 0.15);
  --weui-SEPARATOR-0: hsla(0, 0%, 100%, 0.05);
  --weui-SEPARATOR-1: hsla(0, 0%, 100%, 0.15);
  --weui-STATELAYER-HOVERED: rgba(0, 0, 0, 0.02);
  --weui-STATELAYER-PRESSED: hsla(0, 0%, 100%, 0.1);
  --weui-STATELAYER-PRESSEDSTRENGTHENED: hsla(0, 0%, 100%, 0.2);
  --weui-YELLOW-100: #cc9c00;
  --weui-YELLOW-120: #a37c00;
  --weui-YELLOW-170: #3d2f00;
  --weui-YELLOW-80: #d6af33;
  --weui-YELLOW-90: #d1a519;
  --weui-YELLOW-BG-100: #bf9100;
  --weui-YELLOW-BG-110: #ab8200;
  --weui-YELLOW-BG-130: #866500;
  --weui-YELLOW-BG-90: #c59c1a;
  --weui-FG-HALF: hsla(0, 0%, 100%, 0.65);
  --weui-RED: #fa5151;
  --weui-ORANGERED: #ff6146;
  --weui-ORANGE: #c87d2f;
  --weui-YELLOW: #cc9c00;
  --weui-GREEN: #74a800;
  --weui-LIGHTGREEN: #3eb575;
  --weui-TEXTGREEN: #259c5c;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1196ff;
  --weui-PURPLE: #8183ff;
  --weui-LINK: #7d90a9;
  --weui-REDORANGE: #ff6146;
  --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, 0.05);
  --weui-FG: #fff;
  --weui-WHITE: hsla(0, 0%, 100%, 0.8);
  --weui-FG-5: hsla(0, 0%, 100%, 0.1);
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-RED: rgba(250, 81, 81, 0.6);
  --weui-TAG-BACKGROUND-RED: rgba(250, 81, 81, 0.1);
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, 0.6);
  --weui-BG: #000;
  --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, 0.6);
  --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, 0.6);
  --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, 0.5);
}
.wx-root {
  pointer-events: auto;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    Helvetica Neue,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei UI,
    Microsoft YaHei,
    Arial,
    sans-serif;
}
.wx-root,
.wx_card_root {
  position: relative;
}
.wxw_hide {
  display: none !important;
}
.wx_uninteractive {
  pointer-events: none;
}
.wx-root,
body {
  --APPMSGCARD-BG: #fafafa;
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --APPMSGCARD-BG: #1e1e1e;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --APPMSGCARD-BG: #1e1e1e;
  }
}
.wx-root,
body {
  --APPMSGCARD-LINE-BG: rgba(0, 0, 0, 0.07);
}
.wx-root[data-weui-theme='dark'],
body[data-weui-theme='dark'] {
  --APPMSGCARD-LINE-BG: hsla(0, 0%, 100%, 0.07);
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --APPMSGCARD-LINE-BG: hsla(0, 0%, 100%, 0.07);
  }
}
.appmsg_card_context {
  position: relative;
  background-color: var(--APPMSGCARD-BG);
  border-radius: 8px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.appmsg_card_context:hover {
  cursor: pointer;
}
.wx_img_link,
.wxw_img {
  vertical-align: bottom;
}
.wx_img_link {
  position: relative;
  display: inline-block;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.wx_img_link:after,
.wx_img_link:before {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.wx_img_link:before {
  background: rgba(95, 95, 95, 0.5);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 100%;
}
.wx_img_link:after {
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: 14px;
  mask-size: 14px;
  background: #fff;
}
.wx_img_link_center:after,
.wx_img_link_center:before {
  top: 50%;
  margin-top: -10px;
}
.wx_key_tag {
  padding: 2px 4px;
  font-size: 12px;
  line-height: 1.33333;
  border-radius: 2px;
  max-width: 70%;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  font-style: normal;
  letter-spacing: normal;
  background: var(--weui-TAG-BACKGROUND-BLACK);
  color: var(--weui-FG-2);
}
.wx_key_tag,
.wx_text_link:before {
  display: inline-block;
  vertical-align: middle;
}
.wx_text_link:before {
  content: '';
  width: 1em;
  height: 1em;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  margin: -0.3em 1px 0 2px;
}
.wx_text_link:empty {
  display: none;
}
:host(.wx_tap_highlight_active) .wx_tap_link {
  opacity: 0.5;
}
:host(.wx_tap_highlight_active) .wx_tap_card {
  background-color: #f3f3f3;
}
:host(.wx_tap_highlight_active) .wx_tap_cell {
  background-color: rgba(0, 0, 0, 0.05);
}
@media (prefers-color-scheme: dark) {
  :host(.wx_tap_highlight_active) .wx_tap_card {
    background-color: #252525;
  }
  :host(.wx_tap_highlight_active) .wx_tap_cell {
    background-color: hsla(0, 0%, 100%, 0.1);
  }
}
.wx_css_active :active {
  opacity: 0.5;
}
.weui-flex__item {
  min-width: 0;
}
.weui-flex_align-center {
  align-items: center;
}
[tabindex] {
  outline: 0;
}
.weui-circle-loading,
.weui-circle-loading_before:before {
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  width: 1em;
  height: 1em;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E  %3Cdefs%3E    %3ClinearGradient id='42ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-a' x1='94.087%25' x2='94.087%25' y1='0%25' y2='90.559%25'%3E      %3Cstop offset='0%25' stop-color='%23EDEDED' stop-opacity='0'/%3E      %3Cstop offset='100%25' stop-color='%23EDEDED' stop-opacity='.3'/%3E    %3C/linearGradient%3E    %3ClinearGradient id='42ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-b' x1='100%25' x2='100%25' y1='8.674%25' y2='90.629%25'%3E      %3Cstop offset='0%25' stop-color='%23EDEDED'/%3E      %3Cstop offset='100%25' stop-color='%23EDEDED' stop-opacity='.3'/%3E    %3C/linearGradient%3E  %3C/defs%3E  %3Cg fill='none' opacity='.9'%3E    %3Cpath fill='url(%2342ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-a)' d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z'/%3E    %3Cpath fill='url(%2342ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-b)' d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z'/%3E    %3Ccircle cx='40.5' cy='3.5' r='3.5' fill='%23EDEDED'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E  %3Cdefs%3E    %3ClinearGradient id='42ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-a' x1='94.087%25' x2='94.087%25' y1='0%25' y2='90.559%25'%3E      %3Cstop offset='0%25' stop-color='%23EDEDED' stop-opacity='0'/%3E      %3Cstop offset='100%25' stop-color='%23EDEDED' stop-opacity='.3'/%3E    %3C/linearGradient%3E    %3ClinearGradient id='42ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-b' x1='100%25' x2='100%25' y1='8.674%25' y2='90.629%25'%3E      %3Cstop offset='0%25' stop-color='%23EDEDED'/%3E      %3Cstop offset='100%25' stop-color='%23EDEDED' stop-opacity='.3'/%3E    %3C/linearGradient%3E  %3C/defs%3E  %3Cg fill='none' opacity='.9'%3E    %3Cpath fill='url(%2342ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-a)' d='M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0z'/%3E    %3Cpath fill='url(%2342ecdbc5-cb5f-41dc-88b6-1ee9f740fee0-b)' d='M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0z'/%3E    %3Ccircle cx='40.5' cy='3.5' r='3.5' fill='%23EDEDED'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  color: #606060;
  animation: circleLoading 1s steps(60) infinite;
}
.weui-circle-loading_before:before {
  content: '';
}
.weui-circle-loading.weui-circle-loading_white,
.weui-circle-loading_before.weui-circle-loading_white:before {
  color: #ededed;
}
.wx_hover_card:before {
  border-radius: 8px;
  border: 1px solid rgba(7, 193, 96, 0.3);
}
.wx_hover_card:before,
.wx_selected_card:before {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-sizing: border-box;
  pointer-events: none;
  z-index: 9;
}
.wx_selected_card:before {
  border-radius: 8px;
  border: 1.5px solid #07c160;
  background: rgba(7, 193, 96, 0.1);
}
.wx-root,
.wx-root[data-weui-theme='dark'],
body,
body[data-weui-theme='dark'] {
  --weui-REDORANGE: #ff6146;
}
@media (prefers-color-scheme: dark) {
  .wx-root:not([data-weui-theme='light']),
  body:not([data-weui-theme='light']) {
    --weui-REDORANGE: #ff6146;
  }
}
img {
  pointer-events: none;
}
.common-web .weapp_card {
  margin: 0 auto;
  max-width: 300px;
}
.weapp_card,
.weapp_img {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.weapp_text {
  color: var(--weui-LINK);
}
.weapp_text:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 1em;
  height: 1em;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  margin: -0.3em 1px 0 2px;
}
.weapp_text:empty {
  display: none;
}
.weapp_text:before {
  -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cpath fill='%23576B95' fill-rule='evenodd' d='M10.933 8.529l-.138.002c-.418 0-.662-.3-.505-.644a.902.902 0 0 1 .638-.496c.767-.18 1.288-.765 1.288-1.445 0-.824-.807-1.496-1.815-1.496-1.007 0-1.814.672-1.814 1.496v4.108c0 1.447-1.327 2.613-2.96 2.613-1.634 0-2.96-1.166-2.96-2.613 0-1.269 1.027-2.352 2.426-2.558h.112c.317 0 .545.185.545.45a.488.488 0 0 1-.006.092.348.348 0 0 1-.034.102c-.101.23-.36.429-.638.496-.761.18-1.288.76-1.288 1.418 0 .824.807 1.496 1.815 1.496 1.007 0 1.814-.672 1.814-1.496V5.946c0-1.447 1.327-2.613 2.96-2.613 1.634 0 2.96 1.166 2.96 2.613 0 1.275-1.003 2.346-2.4 2.583z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cpath fill='%23576B95' fill-rule='evenodd' d='M10.933 8.529l-.138.002c-.418 0-.662-.3-.505-.644a.902.902 0 0 1 .638-.496c.767-.18 1.288-.765 1.288-1.445 0-.824-.807-1.496-1.815-1.496-1.007 0-1.814.672-1.814 1.496v4.108c0 1.447-1.327 2.613-2.96 2.613-1.634 0-2.96-1.166-2.96-2.613 0-1.269 1.027-2.352 2.426-2.558h.112c.317 0 .545.185.545.45a.488.488 0 0 1-.006.092.348.348 0 0 1-.034.102c-.101.23-.36.429-.638.496-.761.18-1.288.76-1.288 1.418 0 .824.807 1.496 1.815 1.496 1.007 0 1.814-.672 1.814-1.496V5.946c0-1.447 1.327-2.613 2.96-2.613 1.634 0 2.96 1.166 2.96 2.613 0 1.275-1.003 2.346-2.4 2.583z'/%3E%3C/svg%3E");
}
.weapp_img {
  position: relative;
  display: inline-block;
  vertical-align: bottom;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.weapp_img:after,
.weapp_img:before {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.weapp_img:before {
  background: rgba(95, 95, 95, 0.5);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 100%;
}
.weapp_img:after {
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='%23576B95'%3E    %3Cpath d='M8.566 7.434l-1.98-1.98-.754.755 1.257 1.257H2.333v1.067H7.09L5.832 9.79l.754.755 1.98-1.98a.8.8 0 0 0 0-1.132z'/%3E    %3Cpath d='M4.333 11.8H3.267v1.067a.8.8 0 0 0 .797.8h7.805a.8.8 0 0 0 .798-.798V3.131a.799.799 0 0 0-.798-.798H4.064a.8.8 0 0 0-.797.8V4.2h1.066v-.8H11.6v9.2H4.333v-.8z'/%3E  %3C/g%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: 14px;
  mask-size: 14px;
  background: #fff;
  -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cpath fill='%23576B95' fill-rule='evenodd' d='M10.933 8.529l-.138.002c-.418 0-.662-.3-.505-.644a.902.902 0 0 1 .638-.496c.767-.18 1.288-.765 1.288-1.445 0-.824-.807-1.496-1.815-1.496-1.007 0-1.814.672-1.814 1.496v4.108c0 1.447-1.327 2.613-2.96 2.613-1.634 0-2.96-1.166-2.96-2.613 0-1.269 1.027-2.352 2.426-2.558h.112c.317 0 .545.185.545.45a.488.488 0 0 1-.006.092.348.348 0 0 1-.034.102c-.101.23-.36.429-.638.496-.761.18-1.288.76-1.288 1.418 0 .824.807 1.496 1.815 1.496 1.007 0 1.814-.672 1.814-1.496V5.946c0-1.447 1.327-2.613 2.96-2.613 1.634 0 2.96 1.166 2.96 2.613 0 1.275-1.003 2.346-2.4 2.583z'/%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cpath fill='%23576B95' fill-rule='evenodd' d='M10.933 8.529l-.138.002c-.418 0-.662-.3-.505-.644a.902.902 0 0 1 .638-.496c.767-.18 1.288-.765 1.288-1.445 0-.824-.807-1.496-1.815-1.496-1.007 0-1.814.672-1.814 1.496v4.108c0 1.447-1.327 2.613-2.96 2.613-1.634 0-2.96-1.166-2.96-2.613 0-1.269 1.027-2.352 2.426-2.558h.112c.317 0 .545.185.545.45a.488.488 0 0 1-.006.092.348.348 0 0 1-.034.102c-.101.23-.36.429-.638.496-.761.18-1.288.76-1.288 1.418 0 .824.807 1.496 1.815 1.496 1.007 0 1.814-.672 1.814-1.496V5.946c0-1.447 1.327-2.613 2.96-2.613 1.634 0 2.96 1.166 2.96 2.613 0 1.275-1.003 2.346-2.4 2.583z'/%3E%3C/svg%3E");
  -webkit-mask-size: 15px;
  mask-size: 15px;
}
.weapp_card {
  line-height: 1.4;
}
.weapp_card_bd {
  padding: 12px 12px 0;
}
.weapp_card_ft {
  padding: 8px 12px;
  text-align: left;
}
.weapp_card_profile {
  overflow: hidden;
}
.weapp_card_avatar {
  font-size: 10px;
  width: 2em;
  height: 2em;
  margin-right: 6px;
  flex-shrink: 0;
}
.avatar-img {
  border-radius: 50%;
  -o-object-fit: cover;
  object-fit: cover;
}
.weapp_card_nickname {
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  font-size: 14px;
  line-height: 1.4;
  color: var(--weui-FG-1);
}
.weapp_card_title {
  margin: 8px 0 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  color: var(--weui-FG-0);
  font-size: 17px;
  text-align: left;
}
.weapp_card_thumb_wrp {
  position: relative;
  padding-bottom: 80%;
  overflow: hidden;
  border-radius: 2px;
}
.weapp_card_thumb,
.weapp_card_thumb_wrp:before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.weapp_card_thumb {
  width: 100%;
  -webkit-touch-callout: none;
}
.weapp_card_logo {
  font-size: 14px;
  color: var(--weui-FG-1);
}
.weapp_card_logo:before {
  content: '';
  width: 18px;
  height: 18px;
  margin-top: -0.2em;
  margin-right: 4px;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 18 18'%3E  %3Cpath fill='%236467F0' fill-rule='evenodd' d='M12.753 9.743l-.177.003c-.535 0-.846-.393-.645-.845a1.16 1.16 0 0 1 .816-.651c.981-.237 1.648-1.004 1.648-1.897 0-1.081-1.032-1.963-2.322-1.963s-2.322.882-2.322 1.963v5.392c0 1.899-1.698 3.428-3.788 3.428s-3.788-1.53-3.788-3.428c0-1.665 1.314-3.087 3.105-3.357h.144c.405 0 .697.243.697.589a.64.64 0 0 1-.008.122.464.464 0 0 1-.044.134c-.13.301-.46.562-.816.651-.974.236-1.648.998-1.648 1.86 0 1.082 1.032 1.964 2.322 1.964s2.322-.882 2.322-1.963V6.353c0-1.899 1.698-3.428 3.788-3.428s3.788 1.53 3.788 3.428c0 1.674-1.283 3.079-3.072 3.39z'/%3E%3C/svg%3E");
}
.guarantee_icon,
.weapp_card_logo:before {
  display: inline-block;
  vertical-align: middle;
  background-size: cover;
}
.guarantee_icon {
  flex-shrink: 0;
  margin-left: 8px;
  font-size: 10px;
  width: 1.6em;
  height: 1.6em;
  text-indent: -999em;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E  %3Cg fill='none' fill-rule='evenodd'%3E    %3Cpath d='M0 0h16v16H0z'/%3E    %3Cpath fill='%2307C160' fill-opacity='.1' d='M9.155.976l4.357 2.513a2.307 2.307 0 0 1 1.155 1.998v5.026c0 .824-.44 1.586-1.155 1.998l-4.357 2.513a2.311 2.311 0 0 1-2.31 0l-4.357-2.513a2.307 2.307 0 0 1-1.155-1.998V5.487c0-.824.44-1.586 1.155-1.998L6.845.976a2.311 2.311 0 0 1 2.31 0z'/%3E    %3Cpath fill='%2307C160' fill-rule='nonzero' d='M9.155.976l4.357 2.513a2.307 2.307 0 0 1 1.155 1.998v5.026c0 .824-.44 1.586-1.155 1.998l-4.357 2.513a2.311 2.311 0 0 1-2.31 0l-4.357-2.513a2.307 2.307 0 0 1-1.155-1.998V5.487c0-.824.44-1.586 1.155-1.998L6.845.976a2.311 2.311 0 0 1 2.31 0zm-.4.693a1.511 1.511 0 0 0-1.51 0L2.888 4.182c-.467.27-.755.767-.755 1.305v5.026c0 .538.288 1.036.755 1.305l4.357 2.513c.467.27 1.043.27 1.51 0l4.357-2.513c.467-.27.755-.767.755-1.305V5.487c0-.538-.288-1.036-.755-1.305L8.755 1.669z'/%3E    %3Cpath fill='%2307C160' fill-rule='nonzero' d='M6.783 4.804h4.025v2.56H6.783v-2.56zm1.64 6.54V9.289c-.49.69-1.08 1.204-2.023 1.802l-.445-.69c1.05-.514 1.64-.997 2.131-1.672H6.37v-.697h2.055V7.48h.743v.552h2.07v.697h-1.84c.499.629 1.173 1.135 2.093 1.58l-.421.72a7.73 7.73 0 0 1-1.902-1.725v2.04h-.743zm-3.25.023V7.748c-.169.26-.36.514-.56.767l-.467-.69c.529-.729 1.288-2.162 1.594-3.305l.798.192c-.192.59-.391 1.142-.621 1.656v4.999h-.744zm2.4-5.827v1.089h2.445V5.54H7.573z'/%3E  %3C/g%3E%3C/svg%3E");
}
.safe_buy_icon {
  display: inline-flex;
  align-items: center;
  padding: 2px 4px;
  margin-left: 6px;
  background-color: rgba(255, 97, 70, 0.1);
  color: transparent;
  font-size: 0;
}
.safe_buy_icon:before {
  color: #ff6146;
  font-size: 10px;
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 3.3em;
  height: 1.2em;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='33' height='12' viewBox='0 0 33 12'%3E  %3Cpath fill='%23FF6146' fill-rule='evenodd' d='M28.196 8.24c.522.221 1.023.473 1.502.755.466.274.907.574 1.325.9a.127.127 0 0 1 .01.19l-.624.617a.127.127 0 0 1-.17.007c-.489-.4-.914-.733-1.275-.998a9.878 9.878 0 0 0-1.21-.762.127.127 0 0 1-.045-.184l.334-.48a.127.127 0 0 1 .153-.045zm-.505-4.714c.07 0 .126.057.126.127v.938c0 .49-.084 1.638-.179 2.044h3.677c.07 0 .127.057.127.127v.683c0 .07-.057.127-.127.127h-4.003a3.584 3.584 0 0 1-.257.461c-.16.24-.315.486-.513.7-1.242 1.345-3.219 2.005-5.93 1.982a.127.127 0 0 1-.125-.127v-.377c0-.067.053-.123.12-.126 2.334-.108 4.066-.776 5.197-2.004a4.16 4.16 0 0 0 .37-.509h-3.867a.127.127 0 0 1-.127-.127v-.683c0-.07.057-.127.127-.127h4.287c.128-.417.243-1.542.243-2.044v-.938c0-.07.057-.127.127-.127zM6.718 1.228l.728.12a.127.127 0 0 1 .103.152c-.098.461-.182.82-.254 1.075l-.034.119h2.813c.056 0 .103.036.12.087l.006.04v.683a.127.127 0 0 1-.086.12l-.04.007h-.58C9.41 5.28 9.02 6.696 8.303 7.857c.451.64 1.064 1.147 1.84 1.524.446.216 1.276.446 2.49.688.044.01.08.04.094.081l.008.044v.376a.126.126 0 0 1-.142.126c-1.426-.178-2.408-.39-2.947-.635a5.608 5.608 0 0 1-1.911-1.41c-.325.393-.61.66-1.09 1.077-.3.26-.695.53-1.187.81a.127.127 0 0 1-.135-.007l-.034-.035-.43-.669a.127.127 0 0 1 .047-.18c.521-.278.92-.533 1.196-.763.446-.372.763-.683 1.063-1.078a12.19 12.19 0 0 1-1.127-2.495 11.178 11.178 0 0 1-.404.502.127.127 0 0 1-.139.016l-.039-.031-.449-.532a.127.127 0 0 1-.005-.157c.246-.334.425-.598.536-.792.355-.62.637-1.333.847-2.138.05-.188.112-.471.188-.851a.127.127 0 0 1 .069-.09l.036-.01h.04zm-3.5.004c.16.344.276.599.347.765.102.24.192.468.271.687h1.391c.07 0 .127.057.127.127v.673c0 .07-.057.126-.127.126h-2.35v.826c0 .162-.01.316-.01.468h1.058c.527 0 .954.42.951.93-.01 2.001-.061 3.26-.144 3.775-.106.59-.559.886-1.36.886-.09 0-.31-.014-.658-.043a.126.126 0 0 1-.112-.094l-.153-.575a.127.127 0 0 1 .133-.159c.294.024.508.036.643.036.338 0 .537-.163.601-.488.06-.313.092-1.363.103-3.132a.241.241 0 0 0-.24-.24h-.864a14.394 14.394 0 0 1-.61 3.334 5.72 5.72 0 0 1-.66 1.362.127.127 0 0 1-.175.039l-.014-.01-.586-.499a.127.127 0 0 1-.025-.163 6.1 6.1 0 0 0 .652-1.38c.332-1.071.519-2.408.532-4.047V3.61h-.884a.127.127 0 0 1-.126-.126V2.81c0-.07.056-.127.126-.127h1.77c-.116-.273-.3-.658-.55-1.153a.127.127 0 0 1 .08-.18l.715-.188a.126.126 0 0 1 .147.069zm11.66 2.124c.055 0 .103.036.12.087l.006.04-.022 4.968c0 .48.43.87.926.87h1.34a.909.909 0 0 0 .638-.257.852.852 0 0 0 .262-.618l-.005-.958a.127.127 0 0 1 .168-.12l.729.254c.05.018.084.065.085.119l.005.7c.003.493-.194.958-.554 1.308-.36.35-.818.542-1.328.542h-1.34c-1.05 0-1.904-.825-1.904-1.84V3.483c0-.07.056-.127.126-.127h.747zm-2.34 1.033l.002.001.71.144a.127.127 0 0 1 .1.147c-.194 1.071-.354 1.852-.48 2.343-.132.511-.355 1.206-.668 2.083a.127.127 0 0 1-.185.066l-.642-.386a.127.127 0 0 1-.055-.15c.298-.85.505-1.501.623-1.954.125-.478.274-1.209.447-2.19a.127.127 0 0 1 .147-.104zm7-.192c.372.89.654 1.602.846 2.135.194.541.426 1.21.695 2.006a.127.127 0 0 1-.07.156l-.684.297a.127.127 0 0 1-.17-.076c-.291-.852-.54-1.558-.743-2.119a35.45 35.45 0 0 0-.832-2.063.127.127 0 0 1 .031-.145l.034-.022a.127.127 0 0 1 .012-.004l.726-.237c.062-.02.13.011.155.072zM8.557 3.631H6.934c-.105.254-.211.5-.327.724A12.43 12.43 0 0 0 7.703 6.95c.495-.947.78-2.057.854-3.32zm14.676.81c.41.16.765.32 1.065.48.29.153.592.328.91.525a.127.127 0 0 1 .039.178l-.372.55a.127.127 0 0 1-.176.035 24.441 24.441 0 0 0-.927-.597 6.58 6.58 0 0 0-.958-.466.127.127 0 0 1-.057-.19l.326-.47a.127.127 0 0 1 .15-.045zm.779-1.235c.404.15.753.295 1.046.44.303.148.63.324.98.527a.127.127 0 0 1 .04.18l-.37.55a.127.127 0 0 1-.174.036 19.512 19.512 0 0 0-.99-.6 7.84 7.84 0 0 0-.947-.436.127.127 0 0 1-.057-.191l.324-.46a.127.127 0 0 1 .148-.046zM30.333.225a.33.33 0 0 1 .333.38c-.005.051 0 .146.005.186l.044.292c.018.116.04.231.071.344.032.111.075.221.14.317a.761.761 0 0 0 .236.222 1.161 1.161 0 0 0 .53.154c.116.007.149.002.223.002a.34.34 0 0 1 .335.344.34.34 0 0 1-.335.344c-.074 0-.107-.005-.224.001a1.162 1.162 0 0 0-.529.155.75.75 0 0 0-.237.222 1.086 1.086 0 0 0-.139.317c-.072.256-.088.52-.115.784-.005.04-.01.135-.005.184a.331.331 0 0 1-.333.382.331.331 0 0 1-.331-.382.999.999 0 0 0-.006-.184c-.033-.264-.041-.526-.115-.784a1.086 1.086 0 0 0-.139-.317.75.75 0 0 0-.237-.222 1.162 1.162 0 0 0-.529-.155c-.116-.006-.149-.001-.223-.001a.34.34 0 0 1-.335-.344.34.34 0 0 1 .335-.344c.074 0 .107.005.223-.002a1.161 1.161 0 0 0 .529-.154.761.761 0 0 0 .237-.222c.065-.096.108-.206.14-.317.03-.113.052-.228.071-.344l.043-.292c.006-.04.01-.135.006-.185a.33.33 0 0 1 .332-.38zm-14.52 1.062c.324.369.586.671.787.909l.18.216.075.091c.257.318.578.75.962 1.296a.127.127 0 0 1-.035.18l-.628.409a.127.127 0 0 1-.175-.036 23.946 23.946 0 0 0-.9-1.268 35.338 35.338 0 0 0-1.06-1.265.127.127 0 0 1 .013-.178l.017-.013.603-.365a.127.127 0 0 1 .161.024zm12.314.701c.054 0 .085.056.056.1-.073.115-.176.247-.18.388-.005.114.076.222.117.32a.065.065 0 0 1-.063.089h-5.382a.127.127 0 0 1-.126-.127v-.643c0-.07.056-.127.126-.127z'/%3E%3C/svg%3E")
    no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='33' height='12' viewBox='0 0 33 12'%3E  %3Cpath fill='%23FF6146' fill-rule='evenodd' d='M28.196 8.24c.522.221 1.023.473 1.502.755.466.274.907.574 1.325.9a.127.127 0 0 1 .01.19l-.624.617a.127.127 0 0 1-.17.007c-.489-.4-.914-.733-1.275-.998a9.878 9.878 0 0 0-1.21-.762.127.127 0 0 1-.045-.184l.334-.48a.127.127 0 0 1 .153-.045zm-.505-4.714c.07 0 .126.057.126.127v.938c0 .49-.084 1.638-.179 2.044h3.677c.07 0 .127.057.127.127v.683c0 .07-.057.127-.127.127h-4.003a3.584 3.584 0 0 1-.257.461c-.16.24-.315.486-.513.7-1.242 1.345-3.219 2.005-5.93 1.982a.127.127 0 0 1-.125-.127v-.377c0-.067.053-.123.12-.126 2.334-.108 4.066-.776 5.197-2.004a4.16 4.16 0 0 0 .37-.509h-3.867a.127.127 0 0 1-.127-.127v-.683c0-.07.057-.127.127-.127h4.287c.128-.417.243-1.542.243-2.044v-.938c0-.07.057-.127.127-.127zM6.718 1.228l.728.12a.127.127 0 0 1 .103.152c-.098.461-.182.82-.254 1.075l-.034.119h2.813c.056 0 .103.036.12.087l.006.04v.683a.127.127 0 0 1-.086.12l-.04.007h-.58C9.41 5.28 9.02 6.696 8.303 7.857c.451.64 1.064 1.147 1.84 1.524.446.216 1.276.446 2.49.688.044.01.08.04.094.081l.008.044v.376a.126.126 0 0 1-.142.126c-1.426-.178-2.408-.39-2.947-.635a5.608 5.608 0 0 1-1.911-1.41c-.325.393-.61.66-1.09 1.077-.3.26-.695.53-1.187.81a.127.127 0 0 1-.135-.007l-.034-.035-.43-.669a.127.127 0 0 1 .047-.18c.521-.278.92-.533 1.196-.763.446-.372.763-.683 1.063-1.078a12.19 12.19 0 0 1-1.127-2.495 11.178 11.178 0 0 1-.404.502.127.127 0 0 1-.139.016l-.039-.031-.449-.532a.127.127 0 0 1-.005-.157c.246-.334.425-.598.536-.792.355-.62.637-1.333.847-2.138.05-.188.112-.471.188-.851a.127.127 0 0 1 .069-.09l.036-.01h.04zm-3.5.004c.16.344.276.599.347.765.102.24.192.468.271.687h1.391c.07 0 .127.057.127.127v.673c0 .07-.057.126-.127.126h-2.35v.826c0 .162-.01.316-.01.468h1.058c.527 0 .954.42.951.93-.01 2.001-.061 3.26-.144 3.775-.106.59-.559.886-1.36.886-.09 0-.31-.014-.658-.043a.126.126 0 0 1-.112-.094l-.153-.575a.127.127 0 0 1 .133-.159c.294.024.508.036.643.036.338 0 .537-.163.601-.488.06-.313.092-1.363.103-3.132a.241.241 0 0 0-.24-.24h-.864a14.394 14.394 0 0 1-.61 3.334 5.72 5.72 0 0 1-.66 1.362.127.127 0 0 1-.175.039l-.014-.01-.586-.499a.127.127 0 0 1-.025-.163 6.1 6.1 0 0 0 .652-1.38c.332-1.071.519-2.408.532-4.047V3.61h-.884a.127.127 0 0 1-.126-.126V2.81c0-.07.056-.127.126-.127h1.77c-.116-.273-.3-.658-.55-1.153a.127.127 0 0 1 .08-.18l.715-.188a.126.126 0 0 1 .147.069zm11.66 2.124c.055 0 .103.036.12.087l.006.04-.022 4.968c0 .48.43.87.926.87h1.34a.909.909 0 0 0 .638-.257.852.852 0 0 0 .262-.618l-.005-.958a.127.127 0 0 1 .168-.12l.729.254c.05.018.084.065.085.119l.005.7c.003.493-.194.958-.554 1.308-.36.35-.818.542-1.328.542h-1.34c-1.05 0-1.904-.825-1.904-1.84V3.483c0-.07.056-.127.126-.127h.747zm-2.34 1.033l.002.001.71.144a.127.127 0 0 1 .1.147c-.194 1.071-.354 1.852-.48 2.343-.132.511-.355 1.206-.668 2.083a.127.127 0 0 1-.185.066l-.642-.386a.127.127 0 0 1-.055-.15c.298-.85.505-1.501.623-1.954.125-.478.274-1.209.447-2.19a.127.127 0 0 1 .147-.104zm7-.192c.372.89.654 1.602.846 2.135.194.541.426 1.21.695 2.006a.127.127 0 0 1-.07.156l-.684.297a.127.127 0 0 1-.17-.076c-.291-.852-.54-1.558-.743-2.119a35.45 35.45 0 0 0-.832-2.063.127.127 0 0 1 .031-.145l.034-.022a.127.127 0 0 1 .012-.004l.726-.237c.062-.02.13.011.155.072zM8.557 3.631H6.934c-.105.254-.211.5-.327.724A12.43 12.43 0 0 0 7.703 6.95c.495-.947.78-2.057.854-3.32zm14.676.81c.41.16.765.32 1.065.48.29.153.592.328.91.525a.127.127 0 0 1 .039.178l-.372.55a.127.127 0 0 1-.176.035 24.441 24.441 0 0 0-.927-.597 6.58 6.58 0 0 0-.958-.466.127.127 0 0 1-.057-.19l.326-.47a.127.127 0 0 1 .15-.045zm.779-1.235c.404.15.753.295 1.046.44.303.148.63.324.98.527a.127.127 0 0 1 .04.18l-.37.55a.127.127 0 0 1-.174.036 19.512 19.512 0 0 0-.99-.6 7.84 7.84 0 0 0-.947-.436.127.127 0 0 1-.057-.191l.324-.46a.127.127 0 0 1 .148-.046zM30.333.225a.33.33 0 0 1 .333.38c-.005.051 0 .146.005.186l.044.292c.018.116.04.231.071.344.032.111.075.221.14.317a.761.761 0 0 0 .236.222 1.161 1.161 0 0 0 .53.154c.116.007.149.002.223.002a.34.34 0 0 1 .335.344.34.34 0 0 1-.335.344c-.074 0-.107-.005-.224.001a1.162 1.162 0 0 0-.529.155.75.75 0 0 0-.237.222 1.086 1.086 0 0 0-.139.317c-.072.256-.088.52-.115.784-.005.04-.01.135-.005.184a.331.331 0 0 1-.333.382.331.331 0 0 1-.331-.382.999.999 0 0 0-.006-.184c-.033-.264-.041-.526-.115-.784a1.086 1.086 0 0 0-.139-.317.75.75 0 0 0-.237-.222 1.162 1.162 0 0 0-.529-.155c-.116-.006-.149-.001-.223-.001a.34.34 0 0 1-.335-.344.34.34 0 0 1 .335-.344c.074 0 .107.005.223-.002a1.161 1.161 0 0 0 .529-.154.761.761 0 0 0 .237-.222c.065-.096.108-.206.14-.317.03-.113.052-.228.071-.344l.043-.292c.006-.04.01-.135.006-.185a.33.33 0 0 1 .332-.38zm-14.52 1.062c.324.369.586.671.787.909l.18.216.075.091c.257.318.578.75.962 1.296a.127.127 0 0 1-.035.18l-.628.409a.127.127 0 0 1-.175-.036 23.946 23.946 0 0 0-.9-1.268 35.338 35.338 0 0 0-1.06-1.265.127.127 0 0 1 .013-.178l.017-.013.603-.365a.127.127 0 0 1 .161.024zm12.314.701c.054 0 .085.056.056.1-.073.115-.176.247-.18.388-.005.114.076.222.117.32a.065.065 0 0 1-.063.089h-5.382a.127.127 0 0 1-.126-.127v-.643c0-.07.056-.127.126-.127z'/%3E%3C/svg%3E")
    no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
}
