import { mergeAttributes } from '@tiptap/core'
import { Mark } from '@tiptap/react'
import type { MiniProgramOptions } from './common'
import { addAttributes } from './common'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    textMiniProgram: {
      addTextMiniProgram: (options: {
        type: 'text'
        appId: string
        path: string
        nickname: string
        text: string
      }) => ReturnType
    }
  }
}

export const TextMiniProgram = Mark.create<MiniProgramOptions>({
  name: 'textminiProgram',

  priority: 1001,

  keepOnSplit: true,

  exitable: true,

  inclusive: false,

  addAttributes() {
    return addAttributes
  },

  parseHTML() {
    return [
      {
        tag: 'a',
        getAttrs: (element) => element.getAttribute('data-miniprogram-type') === 'text' && null,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'a',
      mergeAttributes(
        { class: 'weapp_text_link js_weapp_entry px-1' },
        this.options.HTMLAttributes,
        HTMLAttributes,
      ),
      HTMLAttributes.text,
    ]
  },

  addCommands() {
    return {
      addTextMiniProgram:
        (options) =>
        ({ editor, state }) => {
          const weappMark = state.schema.text(options.text, [
            state.schema.marks.textminiProgram.create(options),
          ])
          const tr = editor.state.tr
          tr.insert(editor.state.selection.from, weappMark)
          editor.view.dispatch(tr)
          return true
        },
    }
  },
})
