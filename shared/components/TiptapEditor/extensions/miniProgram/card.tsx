import { Node, mergeAttributes } from '@tiptap/core'
import type { MiniProgramOptions } from './common'
import { addAttributes } from './common'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import MiniProgramShadowCard from './miniprogram-component'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    cardMiniProgram: {
      setCardMiniProgram: (options: {
        type: 'card'
        appId: string
        path: string
        nickname: string
        imageUrl?: string
        avatar?: string
        title?: string
        servicetype?: string
        pluginname?: string
      }) => ReturnType
    }
  }
}

export const CardMiniProgram = Node.create<MiniProgramOptions>({
  name: 'cardMiniProgram',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  // 根据type动态设置group
  group: 'block',

  // 同样根据type设置inline
  inline: false,

  atom: true,

  addAttributes() {
    return {
      ...addAttributes,
      servicetype: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-miniprogram-servicetype'),
        renderHTML: (attributes) => ({
          'data-miniprogram-servicetype': attributes.servicetype,
        }),
      },
      pluginname: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-pluginname'),
        renderHTML: (attributes) => ({
          'data-pluginname': attributes.pluginname,
        }),
      },
      class: {
        default: '',
        parseHTML: (element) => element.getAttribute('class'),
        renderHTML: (attributes) => ({
          class: attributes.class,
        }),
      },
      imageurlback: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-miniprogram-imageurlback'),
        renderHTML: (attributes) => ({
          'data-miniprogram-imageurlback': attributes.imageurlback,
        }),
      },
      cropperinfo: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-miniprogram-cropperinfo'),
        renderHTML: (attributes) => ({
          'data-miniprogram-cropperinfo': attributes.cropperinfo,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'mp-common-miniprogram',
        getAttrs: (element) => element.getAttribute('data-miniprogram-type') === 'card' && null,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['section', {}, ['mp-common-miniprogram', mergeAttributes(HTMLAttributes)]]
  },

  addCommands() {
    return {
      setCardMiniProgram:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },
})

const Component = ({ node }) => {
  return (
    <NodeViewWrapper as="section" class="custom_select_card_wrp wx-edui-media-wrp my-4">
      <MiniProgramShadowCard {...node.attrs} />
    </NodeViewWrapper>
  )
}
