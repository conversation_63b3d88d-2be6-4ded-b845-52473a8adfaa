import type { Attributes } from '@tiptap/react'

export interface MiniProgramOptions {
  HTMLAttributes: Record<string, unknown>
}

export const addAttributes: Attributes = {
  type: {
    default: 'text',
    parseHTML: (element) => element.getAttribute('data-miniprogram-type'),
    renderHTML: (attributes) => ({
      'data-miniprogram-type': attributes.type,
    }),
  },
  appId: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-miniprogram-appid'),
    renderHTML: (attributes) => ({
      'data-miniprogram-appid': attributes.appId,
    }),
  },
  path: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-miniprogram-path'),
    renderHTML: (attributes) => ({
      'data-miniprogram-path': attributes.path,
    }),
  },
  nickname: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-miniprogram-nickname'),
    renderHTML: (attributes) => ({
      'data-miniprogram-nickname': attributes.nickname,
    }),
  },
  text: {
    default: '',
  },
  imageUrl: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-miniprogram-imageurl'),
    renderHTML: (attributes) => ({
      'data-miniprogram-imageurl': attributes.imageUrl,
    }),
  },
  avatar: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-miniprogram-avatar'),
    renderHTML: (attributes) => ({
      'data-miniprogram-avatar': attributes.avatar,
    }),
  },
  title: {
    default: '',
    parseHTML: (element) => element.getAttribute('data-miniprogram-title'),
    renderHTML: (attributes) => ({
      'data-miniprogram-title': attributes.title,
    }),
  },
}
