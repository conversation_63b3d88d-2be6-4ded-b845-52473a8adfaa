import type React from 'react'
import { useRef } from 'react'
import cssText from './minProgram.css?inline'
import { useShadowDom } from '@renderer/hooks/useShadowDom'

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'mp-common-miniprogram': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          'data-miniprogram-nickname'?: string
          'data-miniprogram-avatar'?: string
          'data-miniprogram-title'?: string
          'data-miniprogram-imageurl'?: string
          'data-miniprogram-type'?: string
          'data-miniprogram-servicetype'?: string
          'data-pluginname'?: string
          'data-miniprogram-appid'?: string
          'data-miniprogram-path'?: string
          class?: string
        },
        HTMLElement
      >
    }
  }
}

interface MiniProgramShadowCardProps {
  nickname: string
  avatar: string
  title: string
  imageUrl: string
  appId: string
  path: string
  children?: React.ReactNode
}

const MiniProgramShadowCard: React.FC<MiniProgramShadowCardProps> = ({
  nickname,
  avatar,
  title,
  imageUrl,
  appId,
  path,
}) => {
  const shadowRootRef = useRef<HTMLElement>(null)

  const getTemplateHTML = (nickname: string, avatar: string, title: string, imageUrl: string) => `
    <div class="wx-root weapp_root common-web" data-weui-theme="light">
      <div role="option" title="" class="weapp_card appmsg_card_context wx_tap_card wx_card_root">
        <div class="weapp_card_bd">
          <div class="weapp_card_profile weui-flex weui-flex_align-center">
            <img alt="" data-weappavatar="${avatar}"
                 src="${avatar}"
                 class="weapp_card_avatar js_weapp_card_avatar_img" />
            <div class="weui-flex__item">
              <div class="weapp_card_nickname_wrp weui-flex weui-flex_align-center">
                <span class="weapp_card_nickname">${nickname}</span>
              </div>
            </div>
          </div>
          <div class="weapp_card_info">
            <div class="weapp_card_title">${title}</div>
            <div class="weapp_card_thumb_wrp weui-circle-loading_before">
              <img alt="" data-weappcover="${imageUrl}"
                   src="${imageUrl}"
                   class="weapp_card_thumb js_weapp_card_thumb_img" />
            </div>
          </div>
        </div>
        <div class="weapp_card_ft">
          <span class="weapp_card_logo">小程序</span>
        </div>
      </div>
    </div>
  `

  const templateHTML = getTemplateHTML(nickname, avatar, title, imageUrl)

  useShadowDom({
    ref: shadowRootRef,
    templateHTML,
    cssText,
  })

  return (
    <mp-common-miniprogram
      data-miniprogram-nickname={nickname}
      data-miniprogram-avatar={avatar}
      data-miniprogram-title={title}
      data-miniprogram-imageurl={imageUrl}
      data-miniprogram-type="card"
      data-miniprogram-servicetype="0"
      data-pluginname="insertminiprogram"
      class="js_uneditable custom_select_card mp_miniprogram_iframe mp_common_widget"
      data-miniprogram-appid={appId}
      data-miniprogram-path={path}
      ref={shadowRootRef}
    />
  )
}

export default MiniProgramShadowCard
