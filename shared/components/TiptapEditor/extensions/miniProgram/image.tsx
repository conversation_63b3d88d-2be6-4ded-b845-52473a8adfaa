import { Node, mergeAttributes } from '@tiptap/core'
import type { MiniProgramOptions } from './common'
import { addAttributes } from './common'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    imageMiniProgram: {
      setImageMiniProgram: (options: {
        type: 'image'
        appId: string
        path: string
        nickname: string
        imageUrl?: string
        avatar?: string
        title?: string
      }) => ReturnType
    }
  }
}

export const ImageMiniProgram = Node.create<MiniProgramOptions>({
  name: 'imageMiniProgram',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  // 根据type动态设置group
  group: 'inline',

  // 同样根据type设置inline
  inline: true,

  atom: true,

  priority: 2001,

  addAttributes() {
    return addAttributes
  },

  parseHTML() {
    return [
      {
        tag: 'a.weapp_image_link',
        // 添加优先级更高的选择器
        priority: 1000,
        getAttrs: (node) => {
          // 如果是 HTMLElement，检查是否包含特定的类
          if (node instanceof HTMLElement) {
            if (node.classList.contains('weapp_image_link')) {
              return {} // 返回空对象表示匹配成功
            }
          }
          return false // 返回 false 表示不匹配
        },
      },
    ]
  },

  renderHTML({ node, HTMLAttributes }) {
    console.log(node.attrs)
    return [
      'a',
      mergeAttributes(
        { class: 'weapp_image_link js_weapp_entry' },
        this.options.HTMLAttributes,
        HTMLAttributes,
      ),
      ['img', { src: node.attrs.imageUrl }],
    ]
  },

  addCommands() {
    return {
      setImageMiniProgram:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
