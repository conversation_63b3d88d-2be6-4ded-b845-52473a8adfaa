import { Color } from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import TextStyle from '@tiptap/extension-text-style'
import type { Editor } from '@tiptap/react'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Document from '@tiptap/extension-document'
import Dropcursor from '@tiptap/extension-dropcursor'
import Placeholder from '@tiptap/extension-placeholder'
import CharacterCount from '@tiptap/extension-character-count'
import { CustomImage } from './extensions/image/image'
import Link from '@tiptap/extension-link'
import Emoji, { gitHubEmojis } from '@tiptap-pro/extension-emoji'
import { MenuBar } from './TiptapMenuBar'
import './index.less'
import { Video } from './extensions/video'
import { TextMiniProgram, ImageMiniProgram, CardMiniProgram } from './extensions/miniProgram'
import TextAlign from '@tiptap/extension-text-align'
import { MapCard, MapText } from './extensions/map'
import { VideoSnap } from './extensions/videosnap'
import { Profile } from './extensions/profile'
import LengthInput from '../lengthInput'
import { useAssetLibraryService } from '@renderer/infrastructure/services/application-service/assetLibrary-service'
import { useMemo } from 'react'
import FileHandler from '@tiptap-pro/extension-file-handler'
import { ImageMenubarComponent } from './ImageMenubarComponent'
const extensions = [
  Color,
  TextStyle,
  FontFamily.configure({
    types: ['textStyle'],
  }),
  StarterKit.configure({
    bulletList: {
      keepMarks: true,
      keepAttributes: false,
    },
    orderedList: {
      keepMarks: true,
      keepAttributes: false,
    },
  }),
  Placeholder.configure({
    placeholder: '请输入正文',
  }),
  Link.configure({
    openOnClick: false,
  }),
  TextMiniProgram,
  ImageMiniProgram,
  CardMiniProgram,
  MapCard,
  MapText,
  VideoSnap,
  Profile,
  Document,
  Dropcursor,
  CustomImage.configure({
    allowBase64: true,
    HTMLAttributes: {
      class: 'tiptap-image',
    },
  }),
  Emoji.configure({
    emojis: gitHubEmojis,
  }),
  TextAlign.configure({
    types: ['heading', 'paragraph'],
    defaultAlignment: 'left',
  }),
  Video,
]

type TiptapEditorProps = {
  value: string
  onChange: (value: string) => void
  title: string
  setTitle: (title: string) => void
  author: string
  setAuthor: (author: string) => void
  editorRef?: React.MutableRefObject<Editor | null>
  limit?: number
}

export default function WechatOAEditor({
  value,
  onChange,
  title,
  setTitle,
  author,
  setAuthor,
  limit,
  editorRef,
}: TiptapEditorProps) {
  const assetLibraryService = useAssetLibraryService()
  const extensionsMemo = useMemo(
    () => [
      ...extensions,
      CharacterCount.configure({
        limit,
      }),
      FileHandler.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
        onDrop: async (currentEditor, files, pos) => {
          void assetLibraryService.uploadImageWechatBatch(files).then((list) => {
            list.forEach((item) => {
              // 插入图片到编辑器
              currentEditor
                .chain()
                .focus()
                .insertContentAt(currentEditor.state.selection.anchor, {
                  type: 'image',
                  attrs: {
                    key: item.key,
                    src: item.url,
                  },
                })
                .focus()
                .run()
            })
          })
        },
        onPaste: (currentEditor, files, htmlContent) => {
          if (htmlContent) {
            console.log(htmlContent)
          }
          void assetLibraryService.uploadImageWechatBatch(files).then((list) => {
            list.forEach((item) => {
              // 插入图片到编辑器
              currentEditor
                .chain()
                .focus()
                .insertContentAt(currentEditor.state.selection.anchor, {
                  type: 'image',
                  attrs: {
                    key: item.key,
                    src: item.url,
                  },
                })
                .focus()
                .run()
            })
          })
        },
      }),
    ],
    [assetLibraryService, limit],
  )
  const editor = useEditor({
    extensions: extensionsMemo,
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    },
    onCreate: ({ editor }) => {
      if (editorRef) {
        editorRef.current = editor
      }
    },
    editorProps: {
      attributes: {
        class:
          'min-h-[420px] wechat-oa-editor view prose prose-stone max-w-none prose-sm sm:prose-base pt-0 pb-4 focus:outline-none',
      },
    },
  })
  return (
    <div className="rounded-md">
      {editor && <ImageMenubarComponent editor={editor} type="wechatOA" />}
      <MenuBar type="wechatOA" editor={editorRef?.current ?? null} />
      <div className="m-auto w-[570px] flex-shrink-0 pb-4">
        <LengthInput
          value={title}
          maxLength={64}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="请输入标题"
          className="h-full whitespace-pre-wrap break-words rounded-none border-0 border-b border-input bg-transparent px-0 py-4 pr-12 text-2xl font-[600] text-[#222222] shadow-none placeholder:text-[#969499] focus-visible:outline-none focus-visible:ring-0 md:text-2xl"
        />
        <LengthInput
          value={author}
          maxLength={8}
          onChange={(e) => setAuthor(e.target.value)}
          placeholder="请输入作者"
          className="h-full rounded-none border-0 border-input bg-transparent px-0 py-4 shadow-none focus-visible:outline-none focus-visible:ring-0"
        />
        <EditorContent editor={editor} />
        <div className="flex justify-end px-6 pb-4 text-sm text-[#969499]">
          {editorRef?.current?.storage.characterCount.characters()}/{limit}
        </div>
      </div>
    </div>
  )
}
