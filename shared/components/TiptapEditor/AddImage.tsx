import { useMemo } from 'react'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@renderer/shadcn-components/ui/alert-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/ui/dropdown-menu'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Image } from 'lucide-react'
import { electronService } from '@renderer/infrastructure/services'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import { useDialogInput } from '@renderer/hooks/useDialogInput'
import { localPath2Url } from '@common/protocol'
import type { EditorType } from './TiptapMenuBar'
import { useAssetLibraryService } from '@renderer/infrastructure/services/application-service/assetLibrary-service'
import { FilePicker } from '@renderer/components/FilePicker'
import { EAssetLibraryFileType } from '@renderer/components/AssetLibrary/AssetLibraryMenuTypes'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { toast } from 'sonner'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@renderer/shadcn-components/ui/hover-card'

function DropdownMenuButton({
  children,
  onClick,
}: {
  children: React.ReactNode
  onClick: () => void
}) {
  return (
    <Button
      className="h-auto w-full justify-start px-2 py-1.5 text-left text-sm font-normal"
      variant="ghost"
      onClick={onClick}
    >
      {children}
    </Button>
  )
}

export function AddImage({
  addImage,
  type,
  children,
  onOpenChange,
  isHover = false,
}: {
  addImage: (url: string) => void
  type?: EditorType
  children?: React.ReactNode
  onOpenChange?: (open: boolean) => void
  isHover?: boolean
}) {
  const { open, handleOpenChange, value, setValue } = useDialogInput()
  const { onSelectNotDownloadAsset, selectMultipleImageAsset } = useAssetLibrary()
  const assetLibraryService = useAssetLibraryService()

  // 是否线上图片
  const isOnlineImage = type === 'wechatOA'

  const isValidUrl = useMemo(() => {
    return /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(value)
  }, [value])

  // 处理本地图选择
  const handleLocalImage = async () => {
    try {
      const imageInfo = await electronService.openImageFiles('jpg', 'png', 'jpeg', 'webp')
      if (imageInfo) {
        imageInfo.forEach((image) => {
          addImage(localPath2Url(image.path))
        })
      }
    } catch (e) {
      console.error(e)
    }
  }

  const handleAssetLibrary = async () => {
    if (isOnlineImage) {
      const value = await onSelectNotDownloadAsset({
        type: EAssetLibraryFileType.Image,
        isMultiple: true,
      })
      if (value) {
        const res = value.filter((item) => item.size.bytes < ByteSize.fromMB(10).bytes)
        if (res.length < value.length) {
          toast.warning('已过滤超过10M的图片')
        }
        res.forEach((image) => {
          addImage(image.filePath)
        })
      }
    } else {
      const value = await selectMultipleImageAsset()
      if (value) {
        value.forEach((image) => {
          addImage(localPath2Url(image.path))
        })
      }
    }
  }

  return (
    <>
      {isHover ? (
        <HoverCard openDelay={100}>
          <HoverCardTrigger asChild>
            {children || (
              <Button variant="ghost" className="h-7 w-7 p-0" size="icon">
                <Image className="h-5 w-5" />
              </Button>
            )}
          </HoverCardTrigger>
          <HoverCardContent className="z-50 w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md">
            {!children && <div className="mb-2 text-sm font-semibold">插入图片</div>}

            <DropdownMenuButton onClick={() => handleOpenChange(true)}>输入URL</DropdownMenuButton>
            <DropdownMenuButton onClick={handleAssetLibrary}>素材库</DropdownMenuButton>
            {!isOnlineImage ? (
              <DropdownMenuButton onClick={handleLocalImage}>本地文件</DropdownMenuButton>
            ) : (
              <FilePicker
                accept="image/*"
                multiple
                onChange={(files) => {
                  void assetLibraryService.uploadImageWechatBatch(files).then((results) => {
                    results.forEach((result) => {
                      addImage(result.url)
                    })
                  })
                }}
              >
                <Button
                  className="h-auto w-full justify-start px-2 py-1.5 text-left text-sm font-normal"
                  variant="ghost"
                >
                  本地文件
                </Button>
              </FilePicker>
            )}
          </HoverCardContent>
        </HoverCard>
      ) : (
        <DropdownMenu onOpenChange={onOpenChange}>
          <DropdownMenuTrigger asChild>
            {children || (
              <Button variant="ghost" className="h-7 w-7 p-0" size="icon">
                <Image className="h-5 w-5" />
              </Button>
            )}
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {!children && (
              <>
                <DropdownMenuLabel>插入图片</DropdownMenuLabel>
                <DropdownMenuSeparator />
              </>
            )}
            <DropdownMenuItem onClick={() => handleOpenChange(true)}>输入URL</DropdownMenuItem>
            <DropdownMenuItem onClick={handleAssetLibrary}>素材库</DropdownMenuItem>
            {!isOnlineImage ? (
              <DropdownMenuItem onClick={handleLocalImage}>本地文件</DropdownMenuItem>
            ) : (
              <FilePicker
                accept="image/*"
                multiple
                onChange={(files) => {
                  void assetLibraryService.uploadImageWechatBatch(files).then((results) => {
                    results.forEach((result) => {
                      addImage(result.url)
                    })
                  })
                }}
              >
                <Button
                  className="h-auto w-full justify-start px-2 py-1.5 text-left text-sm font-normal"
                  variant="ghost"
                >
                  本地文件
                </Button>
              </FilePicker>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      <AlertDialog open={open} onOpenChange={handleOpenChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>插入图片</AlertDialogTitle>
            <AlertDialogDescription>请输入图片的URL</AlertDialogDescription>
            <div className="flex flex-col gap-4">
              <Input
                type="text"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                placeholder="输入图片URL"
              />
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex w-full">
            <div className="flex gap-2">
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction disabled={!isValidUrl} onClick={() => addImage(value)}>
                确定
              </AlertDialogAction>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
