import { useCallback } from 'react'
import { useCurrentEditor } from '@tiptap/react'
import { Button } from '@renderer/shadcn-components/ui/button'

export function AddMiniProgram() {
  const { editor } = useCurrentEditor()

  const addTextMiniProgram = useCallback(() => {
    editor?.chain().focus().addTextMiniProgram({
      type: 'text',
      appId: 'wxba5400d4db4c593d',
      path: 'pages/business/index/index',
      nickname: '惜食魔法袋',
      text: '惜食魔法袋',
    })
  }, [editor])

  const addImageMiniProgram = useCallback(() => {
    editor
      ?.chain()
      .focus()
      .setImageMiniProgram({
        type: 'image',
        appId: 'wxba5400d4db4c593d',
        path: 'pages/business/index/index',
        nickname: '惜食魔法袋',
        imageUrl: 'https://cdn.beekka.com/blogimg/asset/202411/bg2024110601.webp',
      })
      .run()
  }, [editor])

  const addCardMiniProgram = useCallback(() => {
    editor
      ?.chain()
      .focus()
      .setCardMiniProgram({
        type: 'card',
        appId: 'wxba5400d4db4c593d',
        path: 'pages/business/index/index',
        nickname: '惜食魔法袋',
        title: '惜时魔法袋',
        avatar: 'https://cdn.beekka.com/blogimg/asset/202411/bg2024110601.webp',
        imageUrl: 'https://cdn.beekka.com/blogimg/asset/202411/bg2024110601.webp',
      })
      .run()
  }, [editor])

  return (
    <div className="flex gap-2">
      <Button onClick={addTextMiniProgram}>添加文字小程序</Button>
      <Button onClick={addImageMiniProgram}>添加图片小程序</Button>
      <Button onClick={addCardMiniProgram}>添加卡片小程序</Button>
    </div>
  )
}
