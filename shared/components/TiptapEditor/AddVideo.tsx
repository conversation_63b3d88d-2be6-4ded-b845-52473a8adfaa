import { useMemo } from 'react'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@renderer/shadcn-components/ui/alert-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/ui/dropdown-menu'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Video } from 'lucide-react'
import { electronService } from '@renderer/infrastructure/services'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import { useDialogInput } from '@renderer/hooks/useDialogInput'
import type { EditorType } from './TiptapMenuBar'
import { FilePicker } from '@renderer/components/FilePicker'
import { useAssetLibraryService } from '@renderer/infrastructure/services/application-service/assetLibrary-service'
import { EAssetLibraryFileType } from '@renderer/components/AssetLibrary/AssetLibraryMenuTypes'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { toast } from 'sonner'
import { localPath2Url } from '@common/protocol'
export function AddVideo({
  addVideo,
  type,
}: {
  addVideo: (url: string) => void
  type?: EditorType
}) {
  const { open, handleOpenChange, value, setValue } = useDialogInput()
  // 校验url是否有效
  const isValidUrl = useMemo(() => {
    return /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(value)
  }, [value])

  // 是否线上图片
  const isOnlineImage = type === 'wechatOA'

  const assetLibraryService = useAssetLibraryService()

  // 处理本地视频选择
  const handleLocalVideo = async () => {
    try {
      const videoInfo = await electronService.openVideoFiles()
      if (videoInfo) {
        videoInfo.forEach((video) => {
          addVideo(localPath2Url(video.filePath))
        })
      }
    } catch (e) {
      console.error(e)
    }
  }
  const { selectMultipleVideoAsset, onSelectNotDownloadAsset } = useAssetLibrary()

  const handleAssetLibrary = async () => {
    if (isOnlineImage) {
      const value = await onSelectNotDownloadAsset({
        type: EAssetLibraryFileType.Video,
        isMultiple: true,
      })
      if (value) {
        const res = value.filter((item) => item.size.bytes < ByteSize.fromMB(10).bytes)
        if (res.length < value.length) {
          toast.warning('已过滤超过10M的视频')
        }
        res.forEach((video) => {
          addVideo(video.filePath)
        })
      }
    } else {
      const value = await selectMultipleVideoAsset()
      if (value) {
        value.forEach((video) => {
          addVideo(localPath2Url(video.filePath))
        })
      }
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-7 w-7 p-0" size="icon">
            <Video className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>插入视频</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {/* <DropdownMenuItem onClick={() => handleOpenChange(true)}>输入URL</DropdownMenuItem> */}
          <DropdownMenuItem onClick={handleAssetLibrary}>素材库</DropdownMenuItem>
          {!isOnlineImage ? (
            <DropdownMenuItem onClick={handleLocalVideo}>本地文件</DropdownMenuItem>
          ) : (
            <FilePicker
              accept="video/*"
              multiple
              onChange={(files) => {
                void assetLibraryService.uploadImageWechatBatch(files).then((results) => {
                  results.forEach((result) => {
                    addVideo(result.url)
                  })
                })
              }}
            >
              <Button
                className="h-auto w-full justify-start px-2 py-1.5 text-left text-sm font-normal"
                variant="ghost"
              >
                本地文件
              </Button>
            </FilePicker>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={open} onOpenChange={handleOpenChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>插入视频</AlertDialogTitle>
            <AlertDialogDescription>请输入视频的URL</AlertDialogDescription>
            <div className="flex flex-col gap-4">
              <Input
                type="text"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                placeholder="输入视频URL"
              />
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex w-full">
            <div className="flex gap-2">
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction disabled={!isValidUrl} onClick={() => addVideo(value)}>
                确定
              </AlertDialogAction>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
