import { useRef, useEffect, useState } from 'react'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/Tabs'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface Tab {
  value: string
  label: string
}

interface ScrollableTabsProps {
  tabs: Tab[]
  defaultValue?: string
  onValueChange?: (value: string) => void
}

const ScrollableTabs = ({
  tabs = [], // 提供默认空数组
  defaultValue, // 移除默认值，改为条件判断
  onValueChange,
}: ScrollableTabsProps) => {
  // 如果没有提供 defaultValue，使用第一个标签的值，如果没有标签则使用空字符串
  const actualDefaultValue = defaultValue || tabs[0]?.value || ''

  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showLeftArrow, setShowLeftArrow] = useState(false)
  const [showRightArrow, setShowRightArrow] = useState(false)

  // 检查是否需要显示滚动箭头
  const checkScrollArrows = () => {
    const container = scrollContainerRef.current
    if (!container) return

    const { scrollLeft, scrollWidth, clientWidth } = container
    setShowLeftArrow(scrollLeft > 0)
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth)
  }

  // 监听滚动容器大小变化
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const resizeObserver = new ResizeObserver(() => {
      checkScrollArrows()
    })

    resizeObserver.observe(container)
    checkScrollArrows()

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  // 处理滚动事件
  const handleScroll = () => {
    checkScrollArrows()
  }

  // 滚动到指定选项卡
  const scrollToTab = (tabValue: string) => {
    const container = scrollContainerRef.current
    if (!container) return

    const tabElement = container.querySelector(`[data-value="${tabValue}"]`) as HTMLElement
    if (!tabElement) return

    const containerWidth = container.clientWidth
    const tabWidth = tabElement.offsetWidth
    const tabLeft = tabElement.offsetLeft

    // 计算目标滚动位置，使选项卡尽量居中
    const targetScroll = tabLeft - (containerWidth - tabWidth) / 2

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth',
    })
  }

  // 箭头点击处理
  const handleArrowClick = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current
    if (!container) return

    const scrollAmount = container.clientWidth / 2
    const targetScroll =
      direction === 'left'
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth',
    })
  }

  // 如果没有标签，不渲染组件
  if (tabs.length === 0) {
    return null
  }

  return (
    <div className="relative w-full">
      {/* 左箭头 */}
      {showLeftArrow && (
        <button
          onClick={() => handleArrowClick('left')}
          className="absolute left-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-background p-1 shadow-md hover:bg-muted"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>
      )}

      {/* 右箭头 */}
      {showRightArrow && (
        <button
          onClick={() => handleArrowClick('right')}
          className="absolute right-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-background p-1 shadow-md hover:bg-muted"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      )}

      {/* Tabs 容器 */}
      <Tabs
        defaultValue={actualDefaultValue}
        onValueChange={(value) => {
          scrollToTab(value)
          onValueChange?.(value)
        }}
        className="w-full"
      >
        <div
          ref={scrollContainerRef}
          className="scrollbar-hide overflow-x-auto"
          onScroll={handleScroll}
        >
          <TabsList className="inline-flex h-10 w-max">
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                data-value={tab.value}
                className="px-1"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    </div>
  )
}

export default ScrollableTabs
