import React from 'react'
import { But<PERSON> } from './ui/button'
import { useAccounts } from '@/hooks/useAccounts'
import { toast } from 'sonner'
import introductioBg from '@/assets/images/introduction-bg.png'
import step1 from '@/assets/images/introduction-step1.png'
import step2 from '@/assets/images/introduction-step2.png'
import logotext from '@/assets/images/logo-text.png'
import { Page } from 'framework7-react'
export const Introduction: React.FC = () => {
  const { isLoading, query } = useAccounts()

  const handleGetStarted = async () => {
    const res = await query.refetch()
    if (!(res.data?.totalSize ?? 0)) {
      toast.warning('暂未检测到有效账号')
    }
  }

  return (
    <Page
      noToolbar
      noNavbar
      className="bg-contain bg-top bg-no-repeat"
      style={{
        backgroundImage: `url(${introductioBg})`,
      }}
    >
      {/* 内容区 */}
      <div className="relative flex h-full flex-col pt-4">
        <div className="space-y-8 overflow-y-auto pb-16">
          {/* Logo和说明文字 */}
          <div className="space-y-6 px-5">
            <img src={logotext} alt="Logo" className="h-9 w-auto" />
            <p className="text-lg font-medium text-foreground">
              蚁小二是一款新媒体一键发布工具, 在开始发布前, 需要先添加媒体账号。
            </p>
          </div>

          {/* 步骤图片 */}
          <div className="space-y-4">
            <img src={step1} alt="Step 1" className="w-full" />
            <img src={step2} alt="Step 2" className="w-full" />
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="absolute bottom-4 left-0 w-full px-4">
          <Button
            className="w-full rounded-full"
            size="lg"
            onClick={handleGetStarted}
            disabled={isLoading}
          >
            我已完成以上操作
          </Button>
        </div>
      </div>
    </Page>
  )
}
