import React, { useState, useRef, useMemo, useCallback } from 'react'
import { Page, Popup } from 'framework7-react'
import { captureVideoFrame, handleImageSelect } from '@/lib/media'
import type { ImageBase } from '@/types/media'
import { Camera, Image, Trash, X } from 'lucide-react'
import { Button } from '@/components/Button'

interface VideoEditorProps {
  videoUrl: string
  onCoverSelected?: (data: ImageBase) => void
  onDelete?: () => void
  open: boolean
  setOpen: (show: boolean) => void
}

export const VideoEditor: React.FC<VideoEditorProps> = ({
  videoUrl,
  onCoverSelected,
  onDelete,
  open,
  setOpen,
}) => {
  const [currentTime, setCurrentTime] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }

  const handleScreenshot = useCallback(async () => {
    try {
      setIsLoading(true)
      const data = await captureVideoFrame(videoUrl, currentTime)
      if (data) {
        onCoverSelected?.(data)
        setOpen(false)
      }
    } finally {
      setIsLoading(false)
    }
  }, [currentTime, onCoverSelected, setOpen, videoUrl])

  const handleLocalSelect = useCallback(async () => {
    const file = await handleImageSelect(1)
    if (file.length > 0) {
      onCoverSelected?.(file[0])
      setOpen(false)
    }
  }, [onCoverSelected, setOpen])

  const handleDelete = useCallback(() => {
    onDelete?.()
    setOpen(false)
  }, [onDelete, setOpen])

  const btnList = useMemo(() => {
    return [
      {
        label: '截取当前帧',
        onClick: handleScreenshot,
        icon: Camera,
        hidden: !onCoverSelected,
      },
      {
        label: '本地选择',
        onClick: handleLocalSelect,
        icon: Image,
        hidden: !onCoverSelected,
      },
      {
        label: '删除',
        onClick: handleDelete,
        icon: Trash,
        hidden: !onDelete,
      },
    ]
  }, [handleDelete, handleLocalSelect, handleScreenshot, onCoverSelected, onDelete])
  return (
    <Popup opened={open} onPopupClosed={() => setOpen(false)} className="rounded-lg">
      <Page noNavbar className="!bg-foreground">
        <div className="relative flex h-full items-center gap-4">
          <div
            className="absolute left-4 top-4 flex size-6 cursor-pointer items-center justify-center"
            onClick={() => setOpen(false)}
          >
            <X className="size-5 text-background" />
          </div>
          {open && videoUrl && (
            <video
              ref={videoRef}
              src={videoUrl}
              className="aspect-[9/14] max-w-full rounded-lg"
              preload="metadata"
              controls
              onTimeUpdate={handleTimeUpdate}
            />
          )}
          <div className="absolute bottom-2 right-2 flex gap-2">
            {btnList
              .filter((item) => !item.hidden)
              .map((btn) => (
                <Button
                  key={btn.label}
                  className="flex h-8 items-center gap-1 rounded-full bg-background/15 px-3 text-background"
                  onClick={btn.onClick}
                  disabled={isLoading}
                >
                  <btn.icon className="size-4" />
                  {btn.label}
                </Button>
              ))}
          </div>
        </div>
      </Page>
    </Popup>
  )
}
