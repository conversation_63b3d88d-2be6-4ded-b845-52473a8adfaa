import { Button } from '@/components/Button'
import CloseIcon from '@/assets/common/close.svg?react'

export function CloseButton({ onClick }: { onClick?: () => void }) {
  return (
    <Button
      size="icon"
      variant="ghost"
      className="h-5 w-5"
      onClick={(e) => {
        e.stopPropagation()
        onClick?.()
      }}
    >
      <CloseIcon className="h-4 w-4" />
    </Button>
  )
}
