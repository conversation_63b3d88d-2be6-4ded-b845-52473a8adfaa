<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>我的-选中</title>
    <defs>
        <linearGradient x1="0%" y1="16.2602065%" x2="94.8916428%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5083FB" offset="0%"></stop>
            <stop stop-color="#336DF4" offset="39.8793414%"></stop>
            <stop stop-color="#6F77EC" offset="70.7950832%"></stop>
            <stop stop-color="#BC98F9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="app发布结构优化" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="我的" transform="translate(-574.000000, -1473.000000)" fill-rule="nonzero">
            <g id="首页" transform="translate(0.000000, 1460.000000)">
                <g id="我的-选中" transform="translate(574.000000, 13.000000)">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="48" height="48"></rect>
                    <path d="M24,4.10035559 C35.049341,4.10035559 43.9,12.9510146 43.9,24.0003556 C43.9,35.0496966 35.049341,43.9003556 24,43.9003556 C12.950659,43.9003556 4.1,35.0496966 4.1,24.0003556 C4.1,12.9510146 12.950659,4.10035559 24,4.10035559 Z M18,17.2503556 C16.4812264,17.2503556 15.25,18.481582 15.25,20.0003556 C15.25,21.5191577 16.4812149,22.7503556 18,22.7503556 C19.5187851,22.7503556 20.75,21.5191577 20.75,20.0003556 C20.75,18.481582 19.5187736,17.2503556 18,17.2503556 Z M30,17.2503556 C28.4811979,17.2503556 27.25,18.4815705 27.25,20.0003556 C27.25,21.5191692 28.4811864,22.7503556 30,22.7503556 C31.5188136,22.7503556 32.75,21.5191692 32.75,20.0003556 C32.75,18.4815705 31.5188021,17.2503556 30,17.2503556 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>