class HtmlService {
  getTopics(html: string) {
    const doc = this.parseToDocument(html)

    const elements = doc.querySelectorAll(`topic[text]`)

    const topics = Array.from(elements).map((element) => element.getAttribute('text') ?? '')

    return topics
  }

  // get all text from html
  public getTextFromHtml(html: string) {
    const doc = this.parseToDocument(html)
    return doc.body.textContent || doc.body.innerText || ''
  }

  // get all text omit topic
  public getTextFromHtmlWithoutTopic(html: string) {
    const doc = this.parseToDocument(html)
    // 克隆文档以避免修改原始DOM
    const clone = doc.cloneNode(true) as Document
    // 移除所有topic元素
    const elements = clone.querySelectorAll('topic[text]')
    elements.forEach(element => element.remove())
    // 获取清理后的文本
    return clone.body.textContent || ''
  }

  public parseToDocument(html: string) {
    // 创建一个DOMParser实例
    const parser = new DOMParser()
    // 将HTML字符串解析为一个Document对象
    return parser.parseFromString(html, 'text/html')
  }
}

export const htmlService = new HtmlService()
