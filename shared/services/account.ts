import type { PlatformAccount } from '@/types'

/**
 * 判断平台账号是否可用
 * @param account 平台账号
 * @returns 是否可用
 */
export const isAccountEnabled = (account: PlatformAccount): boolean => {
  return (
    account.isOperate &&
    account.isRealNameVerified &&
    !account.isFreeze &&
    account.status === 1 &&
    !account.isLock
  )
}

// 账号不可用原因
export const getAccountDisabledReason = (account: PlatformAccount): string => {
  if (!account.isOperate) return '无运营权限'
  if (!account.isRealNameVerified) return '未实名'
  if (account.isFreeze) return '冻结'
  if (account.isLock) return '锁定'
  switch (account.status) {
    case 0:
      return '未登录'
    case 1:
      return '登录成功'
    case 2:
      return '登录过期'
    case 3:
      return '登录失败'
    case 4:
      return '取消授权'
    default:
      return '账号状态异常'
  }
}
