/**
 * 框架路由相关类型定义
 */
import type { Router } from 'framework7/types';

/**
 * 页面路由属性接口
 * 用于定义所有页面组件的 props 类型，包含 Framework7 路由器和路由信息
 */
export interface PageRouteProps {
  /**
   * Framework7 路由器实例
   */
  f7router: Router.Router;
  
  /**
   * Framework7 当前路由信息
   */
  f7route: Router.Route;
}

/**
 * 路由参数类型（处理可能为 undefined 的值）
 */
export type RouteParams = Record<string, string | undefined>;

/**
 * 获取路由参数的辅助函数
 * @param props 页面路由属性
 * @returns 路由参数对象
 */
export const getRouteParams = (props: PageRouteProps): RouteParams => {
  return props.f7route.params || {};
};

/**
 * 获取路由查询参数的辅助函数
 * @param props 页面路由属性
 * @returns 查询参数对象
 */
export const getRouteQuery = (props: PageRouteProps): RouteParams => {
  return props.f7route.query || {};
}; 