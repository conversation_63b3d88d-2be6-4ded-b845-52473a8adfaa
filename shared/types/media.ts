// 图片基础类型
export interface ImageBaseInfo {
  url: string
  width: number
  height: number
  size: number
  type: string
}

export interface ImageBaseWithPath extends ImageBaseInfo {
  path: string
}

export interface ImageBaseWithFile extends ImageBaseInfo {
  arrayBuffer: ArrayBuffer
}

export type ImageBase = ImageBaseWithPath | ImageBaseWithFile

// 视频基础类型
export interface VideoBaseInfo {
  url: string
  duration: number
  width: number
  height: number
  size: number
  type: string
}

export interface VideoBaseWithFile extends VideoBaseInfo {
  file: File
}

export interface VideoBaseWithPath extends VideoBaseInfo {
  path: string
}

export type VideoBase = VideoBaseWithFile | VideoBaseWithPath
