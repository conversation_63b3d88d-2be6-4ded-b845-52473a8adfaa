export interface PlatformDataItem<TRaw = unknown> {
  id: string;
  text: string;
  raw: TRaw;
}

export interface CascadingPlatformDataItem<TRaw = unknown> {
  id: string;
  text: string;
  children?: CascadingPlatformDataItem<TRaw>[];
  raw: TRaw;
}

export function mapRecursive<T>(
  data: T[],
  getter: {
    id: (item: T) => string;
    name: (item: T) => string;
    children: (item: T) => T[] | undefined;
  }
): CascadingPlatformDataItem<T>[] {
  return data.map((x) => {
    return {
      id: getter.id(x),
      text: getter.name(x),
      children:
        getter.children(x) ?
          mapRecursive(getter.children(x)!, getter)
        : undefined,
      raw: x,
    };
  });
}

// 作品数据概览项
export type PublishOverviewHeaderItem = {
  name: string;
  key: string;
};
export type OverviewContentType = 'video' | 'miniVideo' | 'article' | 'dynamic';
