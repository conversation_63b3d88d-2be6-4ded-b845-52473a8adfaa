import { MusicPlatformDataItem } from '@/lib/validations/formTypeSchema'
import type { BaseEntity, Status } from './utils'

/**
 * 任务请求
 */
export interface TaskRequest {
  coverKey?: string
  desc?: string
  platformAccountId: string
  publishArgs?: Record<string, unknown>
  videoKey?: string
  descRich?: Record<string, unknown>[]
}

/**
 * 任务状态类型
 */
export type TaskDeliveryStatus = Extract<Status, 'pending' | 'delivering' | 'successful' | 'failed'>
export type TaskAuditStatus = Extract<Status, 'auditing' | 'rejected' | 'approved'>
export type TaskSetStatus = 'publishing' | 'allsuccessful' | 'partialsuccessful' | 'allfailed'

export type ContentType = 'imageText' | 'video' | 'article' | 'gongzhonghao' | 'dynamic'

/**
 * 任务集
 */
export interface TaskWrapper extends BaseEntity {
  /**
   * 平均任务耗时(秒)
   */
  avTaskDuration: number
  /**
   * 封面
   */
  coverKey?: string
  /**
   * 封面
   */
  coverUrl?: string
  /**
   * 描述
   */
  desc?: string
  /**
   * 结构化描述信息（可以是富文本）
   */
  descRich?: Record<string, unknown>
  /**
   * 失败数
   */
  failedCount: number
  isAppContent: boolean
  /**
   * 是否是草稿
   */
  isDraft: boolean
  /**
   * 是否定时任务
   */
  isTimed: number
  nickName: string
  /**
   * 发布中数
   */
  penddingCount: number
  /**
   * 账号媒体
   */
  platforms: string[]
  /**
   * 编辑类型
   */
  publishType: ContentType
  successCount: number
  /**
   * 任务数
   */
  taskCount: number
  /**
   * 任务集状态，任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功),
   * allfailed(全部发布失败)
   */
  taskSetStatus: TaskSetStatus
  /**
   * 任务总耗时(秒)
   */
  totalTaskDuration: number
  userId: string
}

/**
 * 平台账号基础信息
 */
export interface PlatformAccountBase {
  id: string
  platformAccountName: string
  platformAvatar: string
  platformName: string
}

export enum PlatformResultStage {
  UPLOAD = 'upload',
  PUSH = 'push',
  TRANSCODING = 'transcoding',
  REVIEW = 'review',
  SCHEDULED = 'scheduled',
  SUCCESS = 'success',
}

export enum PlatformResultStageStatus {
  DOING = 'doing',
  SUCCESS = 'success',
  FAIL = 'fail',
}

export type AuditStatusResponse = 'auditing' | 'approved' | 'rejected' | 'scheduled' | null
export type DeliveryStatusResponse = 'pending' | 'delivering' | 'successful' | 'failed'

export interface PublishTaskStatistic {
  /**
   * 收藏数
   */
  collectCount: number
  /**
   * 评论数
   */
  commentCount: number
  /**
   * 点赞数
   */
  greatCount: number
  /**
   * 分享数
   */
  shareCount: number
  /**
   * 播放或阅读数
   */
  viewCount: number
}
/**
 * 任务
 */
export type Task = {
  errorMessage: string
  stageStatus: PlatformResultStageStatus | null //为null是服务端没有被上报状态情况下的初始值
  stages: PlatformResultStage | null //为null是服务端没有被上报状态情况下的初始值
  auditFailedMessage: string
  auditStatus: AuditStatusResponse
  cover: string
  createdAt: string
  deliveryFailedMessage: string
  deliveryStatus: DeliveryStatusResponse
  desc: string
  documentId: string
  id: string
  isAppContent: boolean
  nickName: string
  phone: string
  platformAccountId: string
  platformAccountName: string
  platformAvatar: string
  platformName: string
  publishId: string | null
  publishType: string
  mediaType?: string // 为空是历史数据
  /**
   * @deprecated This property is deprecated and will be removed in future versions.
   */
  reviewStatus: string
  taskId: string
  /**
   * @deprecated This property is deprecated and will be removed in future versions.
   */
  taskStatusCode: number
  auditTime: number
  deliveryTime: number

  statistic?: PublishTaskStatistic
}

export type PushingTaskSetResponse = {
  isTimed: number
  /**
   * 平均任务耗时(秒)
   */
  avTaskDuration: number
  /**
   * 封面
   */
  coverKey?: string
  /**
   * 封面
   */
  coverUrl?: string
  /**
   * 任务集创建时间
   */
  createdAt: number
  /**
   * 描述
   */
  desc?: string
  /**
   * 结构化描述信息（可以是富文本）
   */
  descRich?: { [key: string]: unknown }
  /**
   * 失败数
   */
  failedCount: number

  /**
   * 是否是草稿
   */
  isDraft: boolean
  /**
   * 任务集的taskIdentityId
   */
  id: string
  isAppContent: boolean
  nickName: string
  /**
   * 发布中数
   */
  penddingCount: number
  /**
   * 账号媒体
   */
  platforms: string[]
  /**
   * 发布类型
   */
  publishType: string
  /**
   * 成功数
   */
  successCount: number
  /**
   * 任务数
   */
  taskCount: number
  /**
   * 任务集状态，任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功),
   * allfailed(全部发布失败)
   */
  taskSetStatus: TaskSetStatus
  /**
   * 任务总耗时(秒)
   */
  totalTaskDuration: number
  userId: string
  publishChannel: 'local' | 'cloud'
}

/**
 * 任务阶段
 */
export type TaskStage = 'upload' | 'push' | 'transcoding' | 'review' | 'scheduled' | 'success'

/**
 * 任务阶段状态
 */
export type StageStatus = 'doing' | 'fail' | 'success'

/**
 * 媒体文件信息
 */
interface MediaInfo {
  key: string
  width?: number // 可选，因为示例中 cover 和 video 都有
  height?: number // 可选
  size?: number // 可选
  duration?: number // 可选，只在 video 中出现
}

/**
 * 单个平台账号的发布信息 (用于 publishArgs.accounts)
 */
interface PlatformAccountPublishInfo {
  accountId: string
  cover: MediaInfo
  video: MediaInfo
}

/**
 * 平台位置信息
 */
interface PlatformLocation {
  id: string | number // 抖音是 string, 快手是 number
  text: string
  raw: unknown // 使用 any 以适应其他平台
}

/**
 * 发布参数
 */
interface PublishArgsVideo {
  accounts: PlatformAccountPublishInfo[]
  // 平台特定参数，根据 platforms 数组变化，这里使用示例中的 a/b 命名
  aPlatform?: {
    // 可能对应抖音
    title: string
    description: string // HTML string
  }
  bPlatform?: {
    // 可能对应快手
    title: string
    description: string // HTML string
    tags?: string[]
  }
  isOriginal?: boolean
  timing?: number // 时间戳
  location?: {
    DouYin?: PlatformLocation
    KuaiShou?: PlatformLocation
    [key: string]: PlatformLocation | undefined // 支持其他平台
  }
  categories?: {
    [key: string]: unknown[] | undefined // 平台特定分类
  }
}

interface PublishArgsImageText {
  accounts: { accountId: string }[]
  title: string
  description: string
  timing?: number // 时间戳
  images: MediaInfo[]
  cover: MediaInfo | undefined
  location?: {
    [key: string]: PlatformLocation | undefined // 支持其他平台
  }
  music?: {
    [key: string]: MusicPlatformDataItem | undefined // 平台特定分类
  }
  isDraft: boolean
}

/**
 * 平台账号关联信息 (用于根级别 platformAccounts)
 */
interface PlatformAccountAssociation {
  videoKey: string
  coverKey: string
  platformAccountId: string
}

/**
 * 创建任务请求体类型
 */
export interface CreateTaskRequest {
  coverKey?: string
  desc?: string
  isDraft?: boolean
  isTimed?: number // 示例是时间戳
  platformAccounts: PlatformAccountAssociation[]
  platforms: string[] // e.g., ["抖音", "快手"]
  publishType: string // e.g., "video"
  publishChannel: 'local' | 'cloud' // e.g., "cloud"
  publishArgs: PublishArgsImageText | PublishArgsVideo
  isAppContent?: boolean
}
