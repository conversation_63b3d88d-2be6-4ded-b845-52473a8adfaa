import type { BaseEntity } from './utils'

/**
 * 验证码请求
 */
export interface VerifyCodeRequest {
  phone: string
  sence: string
}

/**
 * 验证码登录请求
 */
export interface CodeAuthRequest {
  phone: string
  code: string
}

/**
 * 密码登录请求
 */
export interface PasswordAuthRequest {
  phone: string
  password: string
}

/**
 * 授权请求联合类型
 */
export type AuthRequest = CodeAuthRequest | PasswordAuthRequest

/**
 * 用户信息
 */
export interface User extends BaseEntity {
  avatarKey: string
  avatarUrl: string
  latestTeamId: string
  nickName: string
  phone: string
}

/**
 * 认证信息
 */
export interface Auth {
  authorization: string
}

import { z } from 'zod'
import { phoneRegExp, verifyCodeRegExp } from '@/constants/user'

// 基础手机号验证
const phoneSchema = z.object({
  phone: z
    .string()
    .min(1, { message: '手机号码不能为空' })
    .regex(phoneRegExp, { message: '请输入正确的手机号码' }),
})

// 验证码登录 Schema
const codeLoginSchema = phoneSchema.extend({
  loginType: z.literal('code'),
  code: z
    .string()
    .min(1, { message: '验证码不能为空' })
    .regex(verifyCodeRegExp, { message: '验证码格式不正确' }),
  password: z.string().optional(), // 可选，不做验证
})

// 密码登录 Schema
const passwordLoginSchema = phoneSchema.extend({
  loginType: z.literal('password'),
  password: z.string().min(1, { message: '密码不能为空' }),
  code: z.string().optional(), // 可选，不做验证
})

// 合并为一个 Union 类型
export const loginSchema = z.discriminatedUnion('loginType', [codeLoginSchema, passwordLoginSchema])
export type LoginFormInputs = z.infer<typeof loginSchema>
