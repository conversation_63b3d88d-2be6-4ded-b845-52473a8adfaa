/**
 * API 相关核心类型定义
 */

// 基础响应类型
export interface ApiResult<T> {
  data: T
  statusCode: number
  message?: string
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  size?: number
}

// 通用查询参数（包含分页）
export interface QueryParams extends PaginationParams {
  [key: string]: unknown
}

// 分页结果类型
export interface PaginatedResult<T> {
  data: T[]
  page: number
  size: number
  totalPage: number
  totalSize: number
}

/**
 * HTTP 响应状态
 */
export enum ResponseStatus {
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  SERVER_ERROR = 500,
}

/**
 * apiStatus 状态
 */
export enum ApiStatus {
  SUCCESS = 0,
  ERROR = 1,
}

/**
 * API 异常
 */
export class ApiError extends Error {
  statusCode: number

  constructor(statusCode: number, message: string) {
    super(message)
    this.statusCode = statusCode
    this.name = 'ApiError'
  }
}

/**
 * API 响应类型
 */
export type ApiResponse<T> = Promise<T>
