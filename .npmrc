save-exact=true
public-hoist-pattern[]=*eslint*
public-hoist-pattern[]=*prettier*
public-hoist-pattern[]=@types/*
public-hoist-pattern[]=@tiptap/*


# shamefully-hoist=false
shared-workspace-lockfile=true

registry=https://registry.npmmirror.com

electron_mirror=https://npmmirror.com/mirrors/electron/
electron_builder_binaries_mirror=https://npmmirror.com/mirrors/electron-builder-binaries/

# 私有仓库
@yixiaoer:registry=https://registry.coozf.com/repository/npm/
@coozf:registry=https://registry.coozf.com/repository/npm-release/

@tiptap-pro:registry=https://registry.tiptap.dev/
//registry.tiptap.dev/:_authToken=h/RQJXkED80KF+oaZQBv8a2hZcwJ2xUzp/rbX6TbGW25XjlGsDwjm5TKAcW19Vdg
