import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react-swc'
import svgr from 'vite-plugin-svgr'
import packageJson from './package.json'
import { resolve } from 'path'
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    plugins: [react(), svgr()],
    base: '',
    build: {
      assetsInlineLimit: 0,
      emptyOutDir: true,
      rollupOptions: {
        treeshake: false,
      },
    },
    resolve: {
      alias: {
        '@renderer': resolve('desktop/src/renderer/src'),
        '@app': resolve('app/src'),
        '@common': resolve('desktop/src/common'),
        '@shared': resolve('shared'),
        '@preload': resolve('desktop/src/preload'),
        '@main': resolve('desktop/src/main'),
        '@': resolve('shared'),
      },
    },
    define: {
      'import.meta.env.VITE_APP_VERSION': JSON.stringify(packageJson.version),
    },
    server: {
      host: true,
      proxy: {
        // '/proxy-api/yixiaoer-video-cloud': {
        //   target: 'https://yixiaoer-video-cloud.changsha42.zos.ctyun.cn',
        //   changeOrigin: true,
        //   secure: false,
        //   rewrite: (path) => path.replace(/^\/proxy-api\/yixiaoer-video-cloud/, ''),
        // },
        '/api': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          secure: false,
        },
      },
    },
  }
})
