:root {
  --nutui-color-primary: hsl(var(--primary));
  --nutui-tabs-titles-item-active-color: hsl(var(--primary));
  --mobile-offset-top: var(--safe-area-inset-top);
  /* 
   * 关键点：设置根字体大小为视口宽度的一定比例
   * 假设设计稿是 375px 宽度，我们希望 1rem = 设计稿的 10px
   * 那么 html 的 font-size 应该是 (10/375)*100vw = 2.6666...vw
   */
  font-size: 4.2vw; /* 10px / 375px * 100 */

  /* 限制最大字体大小，防止在大屏上字体过大 */
  @media (min-width: 768px) {
    font-size: 20px; /* 你可以根据需要调整这个值 */
  }
}

.custom-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

.ios {
  --f7-tabbar-icons-height: 54px;
}

.tiptap p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.adm-popup {
  z-index: 12000;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 隐藏滚动条样式 */
.scrollbar-hidden {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

img {
  -webkit-user-drag: none; /* 禁止 WebKit 浏览器的默认图片拖拽行为 */
  user-drag: none;
}

input[type='datetime-local']::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

* {
  -webkit-user-select: none; /* iOS Safari */
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; /* 标准属性 */
  -webkit-touch-callout: none; /* iOS 长按菜单（复制/分享） */
}

/* iOS 设备特殊处理 */
@supports (-webkit-touch-callout: none) {
  .scrollbar-hidden {
    -webkit-overflow-scrolling: touch;
    overflow: -moz-scrollbars-none;
    overflow: -webkit-paged-x;
    overflow: paged-x;
  }
}

.nut-pulltorefresh-content {
  height: 100%;
}

.device-ios body>div[data-state="open"] , .device-android body>div[data-state="open"] {
  z-index: 11000;
}