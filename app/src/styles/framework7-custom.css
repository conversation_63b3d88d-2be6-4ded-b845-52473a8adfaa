:root {
  --f7-theme-color: #4f46e5;

  /* 页面 */
  --f7-page-bg-color: hsl(var(--background));

  [data-name='my'] {
    --f7-navbar-height: 6.0625rem; /* 97px */
  }

  .bg-page-bg {
    --f7-page-bg-color: hsl(var(--page-bg));
  }

  /* 安全区域 */
  --f7-safe-area-bottom: var(--safe-area-inset-bottom);
  --f7-safe-area-top: var(--safe-area-inset-top);

  /* 列表 */
  --f7-list-item-padding-horizontal: 1rem; /* 16px */
  --f7-list-item-padding-vertical: 1rem; /* 16px */
  --f7-list-font-size: 1rem; /* 16px 已是 rem */
  --f7-list-in-list-padding-left: 1.5rem; /* 24px */
  --f7-list-inset-border-radius: 1rem; /* 16px 已是 rem */
  --f7-list-margin-vertical: 2rem; /* 32px */
  --f7-list-font-size: 1rem; /* 16px 重复项 */
  --f7-list-chevron-icon-area: 1.625rem; /* 26px */
  --f7-list-item-subtitle-font-size: 0.875rem; /* 14px */
  --f7-list-item-text-font-size: 0.875rem; /* 14px */
  --f7-list-item-text-line-height: 1.25rem; /* 20px */
  --f7-list-item-after-font-size: 0.9rem; /* 已是 rem */
  --f7-list-item-after-padding: 0.5rem; /* 已是 rem */
  --f7-list-item-min-height: 3rem; /* 48px */
  --f7-list-item-media-icons-margin: 0.5rem; /* 8px */
  --f7-list-media-item-padding-vertical: 0.75rem; /* 12px */
  --f7-list-media-item-title-font-weight: 500; /* 不是尺寸单位 */
  --f7-list-button-border-color: transparent; /* 不是尺寸单位 */
  --f7-list-button-pressed-bg-color: transparent; /* 不是尺寸单位 */
  --f7-list-group-title-border-color: transparent; /* 不是尺寸单位 */
  --f7-list-group-title-height: 2rem; /* 已是 rem */
  --f7-list-group-title-font-size: 1rem; /* 16px */
  --f7-list-group-title-font-weight: 400; /* 不是尺寸单位 */
  --f7-menu-list-offset: 1rem; /* 16px */
  --f7-menu-list-border-radius: 0.5rem; /* 8px */
  --f7-menu-list-item-min-height: 2.5rem; /* 40px */
  --f7-list-link-pressed-bg-color: transparent; /* 不是尺寸单位 */
  --f7-list-strong-bg-color: var(--background);

  /* 按钮 */
  --f7-button-pressed-bg-color: transparent; /* 不是尺寸单位 */
  --f7-button-text-transform: none; /* 不是尺寸单位 */
  --f7-button-height: 2.5rem; /* 已是 rem */
  --f7-button-padding-horizontal: 1rem; /* 已是 rem */
  --f7-button-border-radius: 0.5rem; /* 已是 rem */
  --f7-button-font-weight: 500; /* 不是尺寸单位 */
  --f7-button-letter-spacing: normal; /* 不是尺寸单位 */
  --f7-button-outline-border-width: 0.0625rem; /* 1px */
  --f7-button-large-text-transform: none; /* 不是尺寸单位 */
  --f7-button-large-height: 3rem; /* 48px */
  --f7-button-large-font-size: 1rem; /* 16px */
  --f7-button-large-font-weight: 500; /* 不是尺寸单位 */
  --f7-button-small-text-transform: none; /* 不是尺寸单位 */
  --f7-button-small-outline-border-width: 0.0625rem; /* 1px */
  --f7-button-small-height: 2.5rem; /* 40px */
  --f7-button-small-font-size: 1rem; /* 16px */
  --f7-button-small-font-weight: 500; /* 不是尺寸单位 */
  --f7-segmented-strong-button-text-transform: none; /* 不是尺寸单位 */
  --f7-segmented-strong-button-active-font-weight: 500; /* 不是尺寸单位 */
  --f7-segmented-strong-button-pressed-bg-color: transparent; /* 不是尺寸单位 */

  /* 工具栏 */
  --f7-bars-bg-color: var(--background);
  --f7-bars-bg-color-rgb: var(--background);
  --f7-bars-border-color: var(--border);

  /* 导航栏 */
  --f7-navbar-font-size: 1.125rem; /* 18px */
  --f7-navbar-link-color: var(--background);

  /* 对话框 */
  --f7-dialog-font-size: 0.875rem; /* 14px */
  --f7-dialog-title-font-size: 1.125rem; /*    */
  --f7-dialog-button-font-size: 0.875rem; /* 14px */

  /* 输入框 */
  --f7-input-height: 2.5rem; /* 40px */
  --f7-input-font-size: 1rem; /* 16px */
}

p {
  margin: 0;
}

button {
  width: auto;
}

.custom {
  --f7-fab-size: auto;
}
