import AboutPage from './pages/about.tsx'
import LoginScreen from './pages/login/index.tsx'
import NotFoundPage from './pages/404.tsx'
import type { Router } from 'framework7/types'
import { TabBar } from './pages//TabBar.tsx'
import { LayoutPage } from './pages/layout/index.tsx'
import UserAgreementPage from './pages/agreement.tsx'
import PublishVideoPage from './pages/publish/video/index.tsx'
import PublishArticlePage from './pages/publish/article/index.tsx'
import PrivacyPolicyPage from './pages/privacyPolicy.tsx'
import PublishImageTextPage from './pages/publish/imageText/index.tsx'
const routes: Router.RouteParameters[] = [
  {
    path: '/',
    component: LayoutPage,
  },
  {
    path: '/login/',
    component: LoginScreen,
  },
  {
    path: '/tab/',
    component: TabBar,
  },
  {
    path: '/publish/video/',
    component: PublishVideoPage,
  },
  {
    path: '/publish/image-text/',
    component: PublishImageTextPage,
  },
  {
    path: '/publish/article/',
    component: PublishArticlePage,
  },
  {
    path: '/about/',
    component: AboutPage,
  },
  {
    path: '/user-agreement/',
    component: UserAgreementPage,
  },
  {
    path: '/privacy-policy/',
    component: PrivacyPolicyPage,
  },
  {
    path: '(.*)',
    component: NotFoundPage,
  },
]

export default routes
