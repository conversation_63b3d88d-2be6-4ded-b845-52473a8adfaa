import { useEffect, useState } from 'react'
import { checkLogin } from '@/lib/http'
import { Page } from 'framework7-react'
import type { PageRouteProps } from '@/types/router'
import { useInitData } from '@/hooks/common/useInitData'
import { LoadingContainer } from '@/components/loading'
import { Button } from '@/components/ui/button'
import { useUpdate } from '@/hooks/common/useUpdate'
import { useSystemStore } from '@/stores'
import { App } from '@capacitor/app'
import { Capacitor } from '@capacitor/core'
import {
  Drawer,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/Drawer'
import { useShallow } from 'zustand/react/shallow'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'
export const LayoutPage = ({ f7router }: PageRouteProps) => {
  const isLoggedIn = checkLogin()
  const [isAgreePrivacyPolicy, setIsAgreePrivacyPolicy] = useSystemStore(
    useShallow((state) => [state.isAgreePrivacyPolicy, state.setIsAgreePrivacyPolicy]),
  )
  const [isMounted, setIsMounted] = useState(false)
  const { isLoading, error, data, refetch } = useInitData(isLoggedIn)

  const updateMutation = useUpdate(true)

  useEffect(() => {
    if (!isAgreePrivacyPolicy) {
      return
    }
    next()
  }, [])

  const next = async () => {
    await updateMutation.mutateAsync()
    if (!isLoggedIn) {
      f7router.navigate('/login/', {
        reloadAll: true,
      })
      return
    }
  }

  useEffect(() => {
    if (!isLoading && !error && data) {
      console.log('初始化成功')
      f7router.navigate('/tab/', {
        // clearPreviousHistory: true,
        reloadAll: true,
      })
    }
  }, [isLoggedIn, isLoading, error, data, f7router])

  // useEffect(() => {
  //   SplashScreen.hide();
  // }, []);

  const handleExit = async () => {
    if (Capacitor.getPlatform() === 'ios') {
      alertBaseManager.open({
        title: '提示',
        description: 'iOS 系统不支持直接退出应用，请使用系统手势返回主屏幕。',
      })
    } else {
      App.minimizeApp()
    }
  }

  return (
    <Page
      name="layout"
      onPageBeforeIn={() => {
        setIsMounted(true)
      }}
      onPageBeforeOut={() => {
        setIsMounted(false)
      }}
      noNavbar
    >
      <div className="flex h-full items-center justify-center">
        {error ? (
          <div className="text-center">
            <p className="mb-4 text-destructive">初始化失败，请重试</p>
            <Button onClick={() => refetch()}>重试</Button>
          </div>
        ) : (
          <LoadingContainer />
        )}
      </div>
      <Drawer dismissible={false} open={!isAgreePrivacyPolicy && isMounted}>
        <DrawerContent hideHandle className="pb-safe">
          <DrawerHeader>
            <DrawerTitle>欢迎使用蚁小二</DrawerTitle>
          </DrawerHeader>
          <div className="max-h-[50vh] overflow-y-auto">
            <div className="prose prose-sm prose-slate space-y-3 p-4">
              <p>
                在使用我们的产品服务前，请仔细阅读
                <Button
                  variant="link"
                  className="h-5 p-0"
                  onClick={() => f7router.navigate('/privacy-policy/')}
                >
                  《隐私政策》
                </Button>
                ，在充分理解前述协议的基础上选择同意或拒绝。
              </p>
              <p>
                您同意
                <Button
                  variant="link"
                  className="h-5 p-0"
                  onClick={() => f7router.navigate('/user-agreement/')}
                >
                  《用户服务协议》
                </Button>
                仅代表您知悉和同意本应用提供的功能，以及功能运行所需的必要个人信息，并给予相应的收集使用授权，但并不代表您已单独同意开启附加功能、处理非必要个人信息。我们提供的拓展功能收集其他个人信息，将在您使用具体功能时征求您的同意。
              </p>
              <p>此外，在首次使用一键发布功能时，我们会向您询问是否授权您的如下权限：</p>
              <p>1、相册：用于一键发布功能的选择</p>
              <p>2、相机：用于一键发布功能的选择</p>
              <p>4、存储：用户本地文件的获取</p>
              <p>5、网络：用于上传发布内容</p>
              <p>6、通知：用于推送消息</p>
              <p>7、矢量传感器：用于获取陀螺仪数据优化用户体验</p>
            </div>
          </div>
          <DrawerFooter className="grid grid-cols-2 gap-2">
            <Button variant="outline" onClick={handleExit}>
              退出
            </Button>
            <Button
              variant="default"
              onClick={() => {
                setIsAgreePrivacyPolicy(true)
                setTimeout(() => {
                  next()
                }, 300)
              }}
            >
              同意并继续
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </Page>
  )
}
