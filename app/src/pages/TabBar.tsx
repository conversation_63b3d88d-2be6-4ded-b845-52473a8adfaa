import { useAppStore } from '@/stores'
import { useShallow } from 'zustand/react/shallow'
import { Link, Page, Tab, Tabs, Toolbar } from 'framework7-react'
import { cn } from '@/lib/utils'
import PublishPage from '@app/pages/publish/index.tsx'
import AccountsPage from '@app/pages/accounts/index.tsx'
import MyPage from '@app/pages/my/index.tsx'
import type { PageRouteProps } from '@/types/router'
import { useQuery } from '@tanstack/react-query'
import { getAccountList } from '@/lib/http'
import { useMemo } from 'react'
const tabItems = [
  {
    id: 'publish',
    label: '发布',
    icon: 'publish',
    page: PublishPage,
  },
  {
    id: 'accounts',
    label: '账号',
    icon: 'account',
    page: AccountsPage,
  },
  {
    id: 'my',
    label: '我的',
    icon: 'my',
    page: MyPage,
  },
]

export const TabBar = (props: PageRouteProps) => {
  const [navIndex, setNavIndex] = useAppStore(
    useShallow((state) => [state.navIndex, state.setNavIndex]),
  )
  const { data: allAccountsData } = useQuery({
    queryKey: ['accounts', 'all'],
    queryFn: () => getAccountList(),
    staleTime: 5 * 60 * 1000, // 5分钟不重新请求
  })

  const publishDisabled = useMemo(() => {
    return allAccountsData?.totalSize === 0
  }, [allAccountsData])

  return (
    <Page pageContent={false}>
      <Toolbar tabbar icons bottom outline className="border-t border-[#EEEEF1] bg-background">
        {tabItems.map((item, index) => (
          <Link
            key={item.id}
            onClick={() => {
              if (publishDisabled && index === 0) {
                return
              }
              setNavIndex(index)
            }}
            tabLinkActive={navIndex === index}
            className={cn('flex h-full w-full flex-col items-center justify-center', {
              'text-primary': navIndex === index,
              'opacity-45': publishDisabled && index === 0,
            })}
          >
            <div className="relative h-6 w-6">
              <span
                className={cn('custom-icon absolute inset-0', `icon-${item.icon}`, {
                  'opacity-0': navIndex === index,
                  'opacity-100': navIndex !== index,
                })}
              />
              <span
                className={cn('custom-icon absolute inset-0', `icon-${item.icon}-active`, {
                  'opacity-100': navIndex === index,
                  'opacity-0': navIndex !== index,
                })}
              />
            </div>
            <span className="text-[0.6rem] font-semibold">{item.label}</span>
          </Link>
        ))}
      </Toolbar>
      <Tabs>
        {tabItems.map((item, index) => (
          <Tab key={item.id} id={item.id} tabActive={navIndex === index}>
            <item.page {...props} />
          </Tab>
        ))}
      </Tabs>
    </Page>
  )
}
