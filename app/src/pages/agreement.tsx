import { Page, Navbar } from 'framework7-react'
import Markdown from 'react-markdown'
import md from '@/assets/userAgreement.md?raw'
import { Browser } from '@capacitor/browser'

const UserAgreementPage = () => {
  return (
    <Page name="agreement">
      <Navbar title="用户服务协议" backLink />
      <div className="prose prose-base prose-slate max-w-none overflow-hidden px-4 py-4">
        <Markdown
          components={{
            a: ({ ...props }) => (
              <a
                className="break-all break-words whitespace-normal overflow-hidden w-full"
                onClick={() => {
                  Browser.open({ url: props.href || '' })
                }}
              >
                {props.href}
              </a>
            ),
          }}
        >
          {md}
        </Markdown>
      </div>
    </Page>
  )
}

export default UserAgreementPage
