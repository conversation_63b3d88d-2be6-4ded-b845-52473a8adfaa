import { Page } from 'framework7-react'
import type { PageRouteProps } from '@/types/router'
import { Input } from '@/components/Input'
import { Logo } from '@/components/Logo'
import loginBgImg from '@/assets/images/login_bg.png'
import { LoadingButton } from '@/components/loadingButton'
import { Button } from '@/components/Button'
import { Checkbox } from '@/components/ui/checkbox'
import { VerificationCodeButton } from '@/components/VerificationCodeButton'
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form'
import { useLogin } from '@/hooks/login'

const LoginScreenComponent = ({ f7router }: PageRouteProps) => {
  const {
    form,
    isLoginButtonDisabled,
    handleSubmit,
    isAgree,
    setIsAgree,
    phone,
    loginType,
    isPending,
  } = useLogin(() => {
    f7router.navigate('/', {
      reloadAll: true,
    })
  })

  const { control } = form

  console.log(loginType)

  return (
    <Page
      noToolbar
      noNavbar
      noSwipeback
      pageContent={false}
      className="flex flex-col items-center bg-contain bg-top bg-no-repeat px-4"
      style={{
        backgroundImage: `url(${loginBgImg})`,
      }}
    >
      {/* Logo and Title Section */}
      <div className="mb-10 mt-[20vh] flex flex-col items-center gap-1">
        <Logo className="mb-4" />
        <div className="text-base text-muted-foreground">随时随地 想发就发</div>
      </div>
      <Form {...form}>
        {/* Form Section - Use RHF handleSubmit */}
        <form onSubmit={handleSubmit} className="flex w-full max-w-xs flex-col gap-6">
          <div className="flex flex-col gap-1">
            <FormField
              control={control}
              name="phone"
              render={({ field }) => (
                <FormItem className="mb-5">
                  <FormControl>
                    <Input
                      type="tel"
                      className="h-12 rounded-lg bg-secondary px-4"
                      placeholder="请输入手机号"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Code Input - Use Controller explicitly */}
            <FormField
              name="code"
              control={control}
              render={({ field }) => (
                <FormItem className="mb-5">
                  <FormControl>
                    <div className="relative">
                      <Input
                        className="h-12 rounded-lg bg-secondary px-4 pr-20"
                        placeholder="请输入验证码"
                        maxLength={6}
                        type="tel"
                        {...field}
                      />
                      <div className="absolute right-1 top-0 flex h-full items-center justify-center">
                        <VerificationCodeButton
                          onSendSuccess={(data) => {
                            field.onChange(data)
                          }}
                          phone={phone}
                        />
                      </div>
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Login Button - Update disabled state */}
          <div className="flex flex-col gap-4">
            <LoadingButton
              type="submit"
              disabled={isLoginButtonDisabled}
              isPending={isPending}
              className="h-12 rounded-lg text-lg"
            >
              登 录
            </LoadingButton>

            {/* Agreement Section */}
            <div className="flex items-center justify-center gap-4 text-sm">
              <label className="flex cursor-pointer items-center gap-2">
                <Checkbox
                  checked={isAgree}
                  onCheckedChange={(value) => setIsAgree(value as boolean)}
                  disabled={isPending}
                />
                <span className="flex flex-wrap items-center text-xs">
                  <span>已阅读并同意</span>
                  <Button
                    className="px-0 text-xs"
                    onClick={() => f7router.navigate('/privacy-policy/')}
                    variant="link"
                  >
                    《隐私政策》
                  </Button>
                  和
                  <Button
                    className="px-0 text-xs"
                    onClick={() => f7router.navigate('/user-agreement/')}
                    variant="link"
                  >
                    《用户服务协议》
                  </Button>
                </span>
              </label>
            </div>
          </div>
        </form>
      </Form>
    </Page>
  )
}

export default LoginScreenComponent
