import { useState, useMemo } from 'react'
import { Page, Navbar } from 'framework7-react'
import { useQuery } from '@tanstack/react-query'

// API 函数 和 类型
import { getAccountList } from '@/lib/http'
import type { PlatformAccount } from '@/types'

// 导入平台工具函数
import { getPlatformByName } from '@/lib/platform'

// 导入组件
import { AccountFilters, AccountList } from '@/components/accounts'
import { Introduction } from '@/components/Introduction'
import { PullToRefresh } from '@nutui/nutui-react'
import { Haptics, ImpactStyle } from '@capacitor/haptics'
import { Loading } from '@/components/loading'

interface AccountsPageProps {
  selectedAccounts?: PlatformAccount[]
  onAccountSelect?: (account: PlatformAccount) => void
}

const AccountsPage: React.FC<AccountsPageProps> = ({ selectedAccounts = [], onAccountSelect }) => {
  // 状态管理：选中的平台和分组
  const [filters, setFilters] = useState<{ platform?: string; group?: string }>({})

  // 获取全量账号列表（用于提取平台信息）
  const {
    data: allAccountsData,
    isLoading: isLoadingAllAccounts,
    isError: isErrorAllAccounts,
    error: errorAllAccounts,
    refetch: refetchAllAccounts,
  } = useQuery({
    queryKey: ['accounts', 'all'],
    queryFn: () => getAccountList(),
    staleTime: 5 * 60 * 1000, // 5分钟不重新请求
  })

  // 获取筛选后的账号列表
  const {
    data: filteredAccountsData,
    isLoading: isLoadingFilteredAccounts,
    isError: isErrorFilteredAccounts,
    error: errorFilteredAccounts,
    refetch: refetchFilteredAccounts,
  } = useQuery({
    queryKey: ['accounts', 'filtered', filters],
    queryFn: () => getAccountList(filters),
    enabled: !!filters.platform || !!filters.group, // 只有在有筛选条件时才请求
  })

  const handlerRefresh = async () => {
    if (!!filters.platform || !!filters.group) {
      refetchFilteredAccounts()
    } else {
      refetchAllAccounts()
    }
  }

  // 从全量数据中提取唯一的平台信息
  const uniquePlatforms = useMemo(() => {
    if (!allAccountsData?.data) return []
    const platformNames = [...new Set(allAccountsData.data.map((acc) => acc.platformName))]
    return platformNames
      .map((name) => getPlatformByName(name))
      .sort((a, b) => a.displayOrder - b.displayOrder)
  }, [allAccountsData])

  // 获取当前显示的账号列表
  const accounts = useMemo(() => {
    if (filters.platform || filters.group) {
      return filteredAccountsData?.data ?? []
    }
    return allAccountsData?.data ?? []
  }, [filters, filteredAccountsData, allAccountsData])
  if (!allAccountsData?.totalSize) {
    return <Introduction />
  }

  return (
    <Page name="accounts" className="accounts-page">
      <Navbar title="帐号" />

      {/* Filters */}
      <div className="flex h-full flex-col">
        <AccountFilters
          platforms={uniquePlatforms}
          value={filters}
          onChange={setFilters}
          isLoadingPlatforms={isLoadingAllAccounts}
          isErrorPlatforms={isErrorAllAccounts}
        />

        {/* Accounts List */}
        <PullToRefresh
          className="accounts-list-container flex-grow overflow-y-auto"
          onRefresh={async () => {
            Haptics.impact({ style: ImpactStyle.Medium })
            // 使用 refreshFirstPage 方法只刷新第一页数据
            return await handlerRefresh()
          }}
          renderIcon={(status) => {
            return (
              <>
                <div className="flex items-center justify-center pt-5">
                  {['refreshing', 'complete'].includes(status) && (
                    <Loading className="mb-2 size-5" />
                  )}
                </div>
              </>
            )
          }}
        >
          <AccountList
            accounts={accounts}
            isLoading={isLoadingAllAccounts || isLoadingFilteredAccounts}
            isError={isErrorAllAccounts || isErrorFilteredAccounts}
            error={(errorAllAccounts || errorFilteredAccounts) as Error | undefined}
            hasFilters={!!(filters.platform || filters.group)}
            selectedAccounts={selectedAccounts}
            onAccountSelect={onAccountSelect}
          />
        </PullToRefresh>
      </div>
    </Page>
  )
}

export default AccountsPage
