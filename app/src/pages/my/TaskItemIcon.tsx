import VideoIcon from '@/assets/publish/task/video-icon-item.svg?react'
import ImageTextIcon from '@/assets/publish/task/image-text-icon-item.svg?react'
import ArticleIcon from '@/assets/publish/task/article-icon-item.svg?react'
import GongZhongHaoIcon from '@/assets/publish/task/gong-zhong-hao-icon-item.svg?react'
import { ContentType } from '@/types'
export const TaskItemIcon = ({ type }: { type: ContentType }) => {
  switch (type) {
    case 'video':
      return <VideoIcon />
    case 'imageText':
      return <ImageTextIcon />
    case 'article':
      return <ArticleIcon />
    case 'gongzhonghao':
      return <GongZhongHaoIcon />
    default:
      return <VideoIcon />
  }
}
