import { getChildTaskList, getTaskSet } from '@/lib/http'
import { ScrollArea } from '@/components/ScrollArea'
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/Tabs'
import { DateUtils } from '@/utils'
import { getPlatformByName, platformNames } from '@/lib/platform'
import { PlatformResultStage, PlatformResultStageStatus } from '@/types'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useMemo, useState } from 'react'
import PendingIcon from '@/assets/publish/states/pending.svg?react'
import SuccessIcon from '@/assets/publish/states/success.svg?react'
import FailedIcon from '@/assets/publish/states/failed.svg?react'
import { Separator } from '@/components/separator'
import DownIcon from '@/assets/svg/down.svg?react'
import { Drawer, DrawerContent } from '@/components/ui/drawer'

interface TaskState {
  text: string
  status: PlatformResultStageStatus
  stage: string
}

const manualStatusMap = {
  [PlatformResultStageStatus.DOING]: '中',
  [PlatformResultStageStatus.SUCCESS]: '成功',
  [PlatformResultStageStatus.FAIL]: '失败',
}

const getTextColorClass = (status: PlatformResultStageStatus) => {
  switch (status) {
    case PlatformResultStageStatus.SUCCESS:
      return 'text-green-600 bg-green-100'
    case PlatformResultStageStatus.FAIL:
      return 'text-red-600 bg-red-100'
    case PlatformResultStageStatus.DOING:
      return 'text-blue-600 bg-blue-100'
    default:
      return ''
  }
}

const getManualStatusObj = (steap: PlatformResultStage, isTimed: number = 0) => {
  const obj: Record<string, string> = {
    upload: '上传',
    push: '推送',
    transcoding: '转码',
    review: '审核',
    success: '发布',
    scheduled: '定时发布',
  }
  if (!isTimed) {
    delete obj.scheduled
  }

  return obj[steap]
}

export default function TaskDetail({ id, onClose }: { id: string; onClose: () => void }) {
  const [isOpen, setIsOpen] = useState(false)
  const [tab, setTab] = useState('all')
  const [showMore, setShowMore] = useState<Record<string, boolean | undefined>>({})

  useEffect(() => {
    setIsOpen(!!id)
  }, [id])

  useEffect(() => {
    if (!isOpen) {
      setTab('all')
      onClose()
    }
  }, [isOpen])

  const { data } = useQuery({
    queryKey: ['taskset', id],
    queryFn: () => getTaskSet(id),
    enabled: !!id,
  })

  const { data: tasks } = useQuery({
    queryKey: ['childTasklist', id],
    queryFn: () => getChildTaskList(id),
    enabled: !!id,
  })

  const currentTasks = useMemo(() => {
    return (
      tasks?.filter((x) => {
        if (tab === 'all') return true
        return x.stageStatus === PlatformResultStageStatus.FAIL
      }) || []
    )
  }, [tab, tasks])

  const taskElements = useMemo(() => {
    return currentTasks?.map((x) => {
      const {
        errorMessage: message,
        stageStatus: rawStageStatus = PlatformResultStageStatus.DOING,
        stages: rawStages = PlatformResultStage.UPLOAD,
        platformName,
      } = x
      const platform = getPlatformByName(platformName)

      const stageStatus = rawStageStatus || PlatformResultStageStatus.DOING
      const stages = rawStages || PlatformResultStage.UPLOAD

      const { isDraft, isTimed = 0 } = data ?? {}

      const beforeList = [
        ['upload', '上传状态:'],
        ['push', '推送状态:'],
        ...(!isDraft ? [['transcoding', '转码状态:']] : []),
        ...(isTimed && !isDraft
          ? [['scheduled', `定时发布 ${DateUtils.formatDate(isTimed)}:`]]
          : []),
        ...(!isDraft ? [['review', '审核状态:']] : []),
        ['success', '发布状态:'],
      ]
      const afterList = [
        ['upload', '上传状态:'],
        ['push', '推送状态:'],
        ...(!isDraft ? [['transcoding', '转码状态:']] : []),
        ...(!isDraft ? [['review', '审核状态:']] : []),
        ...(isTimed && !isDraft
          ? [['scheduled', `定时发布 ${DateUtils.formatDate(isTimed)}:`]]
          : []),
        ['success', '发布状态:'],
      ]
      const otherList = [
        ['upload', '上传状态:'],
        ['push', '推送状态:'],
        ...(!isDraft
          ? [
              ['transcoding', '转码状态:'],
              ['review', '审核状态:'],
            ]
          : []),
        ['success', '发布状态:'],
      ]
      const openList = [
        ['push', '推送状态:'],
        ...(!isDraft ? [['review', '审核状态:']] : []),
        ['success', '发布状态:'],
      ]

      let stageStatusText = '成功'
      let stageStatusType = PlatformResultStageStatus.SUCCESS

      switch (stageStatus) {
        case PlatformResultStageStatus.SUCCESS:
          stageStatusText = '成功'
          stageStatusType = PlatformResultStageStatus.SUCCESS
          break
        case PlatformResultStageStatus.FAIL:
          stageStatusText = '失败'
          stageStatusType = PlatformResultStageStatus.FAIL
          break
        case PlatformResultStageStatus.DOING:
          stageStatusText = '中'
          stageStatusType = PlatformResultStageStatus.DOING
          break
        default:
          break
      }

      let resultList: TaskState[] = []
      let targetList: string[][] = []

      switch (platform.name) {
        case platformNames.BaiJiaHao:
        case platformNames.XiaoHongShu:
        case platformNames.DouYin:
        case platformNames.YiDianHao:
        case platformNames.WangYiHao:
        case platformNames.BiliBili:
        case platformNames.KuaiShou:
        case platformNames.TouTiaoHao:
        case platformNames.WeiXinShiPinHao:
          targetList = afterList
          break
        case platformNames.ZhiHu:
        case platformNames.QiEHao:
        case platformNames.TengXunWeiShi:
        case platformNames.AiQiYi:
        case platformNames.XinLangWeiBo:
          targetList = beforeList
          break
        case platformNames.WeiXinGongZhongHao:
          targetList = openList
          break
        default:
          targetList = otherList
          break
      }

      const currentIndex = targetList.findIndex((item) => item[0] === stages)

      if (currentIndex !== -1) {
        resultList = targetList.slice(0, currentIndex + 1).map((item, i) => {
          if (i < currentIndex) {
            return {
              text: item[1] + ` ${getManualStatusObj(item[0] as PlatformResultStage, isTimed)}成功`,
              status: 'success',
              stage: item[0],
            }
          } else if (i === currentIndex) {
            return {
              text:
                (i === targetList.length - 1 ? '' : item[1]) +
                ` ${getManualStatusObj(item[0] as PlatformResultStage, isTimed)}${stageStatusText}`,
              status: stageStatusType,
              stage: item[0],
            }
          }
          return null as never
        }) as TaskState[]

        if (
          stageStatus === PlatformResultStageStatus.SUCCESS &&
          currentIndex < targetList.length - 1
        ) {
          if (isDraft && stages === PlatformResultStage.PUSH) {
            resultList.push({
              text: `${getManualStatusObj(PlatformResultStage.SUCCESS, isTimed)}成功`,
              status: PlatformResultStageStatus.SUCCESS,
              stage: PlatformResultStage.SUCCESS,
            })
          } else {
            const nextStage = targetList[currentIndex + 1]
            resultList.push({
              text:
                nextStage[1] +
                ` ${getManualStatusObj(nextStage[0] as PlatformResultStage, isTimed)}中`,
              status: PlatformResultStageStatus.DOING,
              stage: nextStage[0],
            })
          }
        }
      } else {
        resultList = targetList.map((item, index) => ({
          text:
            (index === targetList.length - 1 ? '' : item[1]) +
            ` ${getManualStatusObj(item[0] as PlatformResultStage, isTimed)}成功`,
          status: PlatformResultStageStatus.SUCCESS,
          stage: item[0],
        }))
      }

      const getStatusIcon = (status: PlatformResultStageStatus) => {
        switch (status) {
          case PlatformResultStageStatus.SUCCESS:
            return <SuccessIcon className="z-10 h-3.5 w-3.5 bg-white text-green-500" />
          case PlatformResultStageStatus.FAIL:
            return <FailedIcon className="z-10 h-3.5 w-3.5 bg-white text-red-500" />
          case PlatformResultStageStatus.DOING:
            return <PendingIcon className="z-10 h-3.5 w-3.5 text-[#909090]" />
          default:
            return <SuccessIcon className="z-10 h-3.5 w-3.5 bg-white text-green-500" />
        }
      }

      const lastRes = resultList[resultList.length - 1]

      return (
        <div
          key={x.taskId}
          className="flex cursor-pointer flex-col gap-3 rounded-lg bg-white py-4"
          onClick={() => {
            setShowMore((pre) => {
              pre[x.taskId] = !pre[x.taskId]
              return { ...pre }
            })
          }}
        >
          <div className="flex items-center px-3.5">
            <DownIcon
              className="mr-1"
              style={{
                transform: showMore[x.taskId] ? 'rotate(0deg)' : 'rotate(-90deg)',
                transition: 'transform 0.3s ease-in-out',
              }}
            />
            {x.platformAvatar ? (
              <div className="relative mr-3 h-8 w-8 shrink-0 bg-white">
                <div className="h-full w-full overflow-hidden rounded-full">
                  <img src={x.platformAvatar} alt="Platform" className="h-full w-full" />
                </div>
                <img
                  src={platform.icon}
                  alt="Platform"
                  className={'absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-white'}
                />
              </div>
            ) : (
              <div className="mr-3 h-8 w-8 shrink-0 rounded-full">
                <img
                  src={platform.icon}
                  alt="Platform"
                  className={'h-full w-full rounded-full bg-white'}
                />
              </div>
            )}
            <span className="text-md grow truncate">{x.platformAccountName}</span>
            {lastRes.status && lastRes.stage && (
              <div
                className={`${getTextColorClass(lastRes.status)} rounded-sm px-2 py-[0.5px] text-[12px]`}
              >
                {getManualStatusObj(lastRes.stage as PlatformResultStage, data?.isTimed)}
                {manualStatusMap[lastRes.status]}
              </div>
            )}
          </div>
          {showMore[x.taskId] && (
            <>
              <Separator orientation="horizontal" className="h-0.5 bg-secondary" />
              <div className="flex shrink-0 flex-col px-3.5 pl-[25px]">
                <div className="relative flex flex-col gap-4">
                  {resultList.map((item, index) => {
                    const isLast = index === resultList.length - 1
                    return (
                      <div key={index}>
                        <div className="flex items-center gap-2">
                          <div className="relative">
                            {getStatusIcon(item.status)}
                            {!isLast && (
                              <div
                                className="absolute left-1/2 top-3.5 h-full -translate-x-1/2 border-l border-dashed border-gray-300"
                                style={{ height: 'calc(100% + 12px)' }}
                              ></div>
                            )}
                          </div>
                          <span className="text-[14px]">{item.text}</span>
                        </div>
                        {isLast && item.status === PlatformResultStageStatus.FAIL && (
                          <div className="mt-2 rounded-lg bg-[#F8F8FA] p-2 text-[12px] text-[#2D2933]">
                            {message}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </>
          )}
        </div>
      )
    })
  }, [currentTasks, data, showMore])

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerContent>
        {/* <div className="relative flex h-[50px] items-center justify-center">
          <div className="absolute left-0 top-0 flex h-full w-12 items-center justify-center">
            <Link onClick={() => setIsOpen(false)}>
              <X className="h-5 w-5 text-secondary-foreground" />
            </Link>
          </div>
          <span className="text-base font-medium">发布详情</span>
        </div> */}
        <div className="flex h-[85vh] flex-col bg-secondary">
          <Tabs
            value={tab}
            onValueChange={setTab}
            defaultValue="value"
            className="flex shrink-0 flex-col overflow-hidden bg-background px-4"
          >
            <TabsList className="border-none">
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="fail">失败</TabsTrigger>
            </TabsList>
          </Tabs>
          <ScrollArea className="grow overflow-hidden">
            <div className="space-y-3 px-5 py-5">{taskElements}</div>
          </ScrollArea>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
