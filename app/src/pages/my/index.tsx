import { Page, Navbar } from 'framework7-react'
import { useAppStore } from '@/stores'
import { useShallow } from 'zustand/react/shallow'
import Tasks from './Tasks' // 稍后创建 Tasks 组件
import { Menu } from 'lucide-react'
import NodeIcon from '@/assets/svg/node.svg'
import VipIcon from '@/assets/svg/vip.svg'
import SwitchIcon from '@/assets/svg/switch.svg'
import type { PageRouteProps } from '@/types/router'
import homeBgImg from '@/assets/images/home_bg.png'
import { useState } from 'react'
import { TeamSwitcher } from '@/components/TeamSwitcher'

const MyPage = ({ f7router }: PageRouteProps) => {
  const [user, team, onlineCount] = useAppStore(
    useShallow((state) => [state.user, state.team, state.onlineCount]),
  )

  // 团队切换弹窗状态
  const [teamSwitcherOpened, setTeamSwitcherOpened] = useState(false)

  return (
    <Page
      name="my"
      noToolbar
      noNavbar
      className="bg-contain bg-top bg-no-repeat"
      style={{
        backgroundImage: `url(${homeBgImg})`,
      }}
    >
      <Navbar hidden transparent></Navbar>
      <div className="flex h-full w-full flex-col overflow-hidden">
        <div className="relative flex h-24 w-full shrink-0 items-center overflow-hidden px-5">
          {/* 用户信息区域 */}
          <div className="relative flex w-full items-center gap-4 overflow-hidden">
            <div className="h-14 w-14 shrink-0 rounded-full bg-gradient-to-br from-blue-400 via-blue-500 to-purple-500 p-0.5">
              <div className="h-full w-full overflow-hidden rounded-full border border-background">
                <img src={user.avatarUrl} alt="用户头像" className="h-full w-full object-cover" />
              </div>
            </div>

            <div className="flex flex-1 flex-col gap-2 overflow-hidden">
              <div className="flex flex-1 items-center justify-between gap-1">
                <div className="flex flex-1 items-center gap-1 overflow-hidden">
                  <h2 className="truncate text-lg font-semibold">{user.nickName || '用户名'}</h2>
                  {team.isVip && (
                    <div className="shrink-0">
                      <img src={VipIcon} alt="VIP" className="h-5 w-5" />
                    </div>
                  )}
                </div>
                <div
                  onClick={() => f7router.navigate('/about/')}
                  className="flex h-6 w-6 shrink-0 items-center justify-center"
                >
                  <Menu className="h-6 w-6 text-foreground" />
                </div>
              </div>
              <div className="flex flex-1 items-center justify-between gap-2">
                <div
                  onClick={() => setTeamSwitcherOpened(true)}
                  className="flex items-center overflow-hidden text-sm text-muted-foreground"
                >
                  <span className="truncate">{team?.name || '团队名称'}</span>
                  <img src={SwitchIcon} alt="switch" className="h-4 w-4" />
                </div>

                <div className="shrink-0 text-sm">
                  <div className="flex items-center">
                    <img src={NodeIcon} alt="发布节点" className="h-4 w-4" />
                    <span className="ml-1">发布节点</span>
                    <span className="ml-1 font-semibold">{onlineCount}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-hidden rounded-t-xl bg-background">
          <Tasks />
        </div>
      </div>

      {/* 团队切换弹窗 */}
      <TeamSwitcher
        opened={teamSwitcherOpened}
        onSheetClosed={() => setTeamSwitcherOpened(false)}
      />
    </Page>
  )
}

export default MyPage
