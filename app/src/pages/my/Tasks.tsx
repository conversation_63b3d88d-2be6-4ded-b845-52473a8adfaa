import { PullToRefresh } from '@nutui/nutui-react'
import { useTaskList } from '@/hooks/useTaskList'
import { ScrollArea, ScrollBar } from '@/components/ScrollArea'
import { getPlatformByName } from '@/lib/platform'
import type { TaskSetStatus } from '@/types'
import SuccessIcon from '@/assets/svg/success.svg'
import FailedIcon from '@/assets/svg/allfailed.svg'
import PublishingIcon from '@/assets/svg/publishing.svg'
import PartialSuccessfulIcon from '@/assets/svg/partialsuccessful.svg'
import { Haptics, ImpactStyle } from '@capacitor/haptics'
import TaskDetail from './TaskDetail'
import { useState } from 'react'
import { ImageBase } from '@/components/ImageBase'
import { Loading, LoadingContainer } from '@/components/loading'
import taskEmpty from '@/assets/images/task-empty.png'
import { TaskItemIcon } from './TaskItemIcon'
import { DateUtils } from '@/utils'

// 任务状态组件
const TaskStatus = ({ status }: { status: TaskSetStatus }) => {
  const statusMap = {
    allsuccessful: {
      icon: SuccessIcon,
      text: '发布成功',
    },
    allfailed: {
      icon: FailedIcon,
      text: '发布失败',
    },
    publishing: {
      icon: PublishingIcon,
      text: '发布中',
    },
    partialsuccessful: {
      icon: PartialSuccessfulIcon,
      text: '部分成功',
    },
  }

  const { icon, text } = statusMap[status]

  return (
    <div className="flex items-center text-[0.94rem] text-secondary-foreground">
      <img src={icon} alt={text} className="mr-1 h-4 w-4" />
      {text}
    </div>
  )
}

const Tasks = () => {
  const { tasks, fetchNextPage, hasNextPage, isFetchingNextPage, refreshFirstPage } = useTaskList()
  const [activeId, setActiveId] = useState<string>('')

  return (
    <div className="h-full w-full overflow-hidden">
      <PullToRefresh
        className="h-full"
        onRefresh={async () => {
          Haptics.impact({ style: ImpactStyle.Medium })
          // 使用 refreshFirstPage 方法只刷新第一页数据
          return await refreshFirstPage()
        }}
        renderIcon={(status) => {
          return (
            <>
              <div className="flex items-center justify-center pt-5">
                {['refreshing', 'complete'].includes(status) && <Loading className="mb-2 size-5" />}
              </div>
            </>
          )
        }}
      >
        {tasks.length === 0 ? (
          <div className="mt-[20vh] flex h-full flex-1 flex-col items-center">
            <img src={taskEmpty} className="w-8/12" />
            <span className="text-sm text-muted-foreground">
              暂无发布任务，点击下方添加发布任务吧～
            </span>
          </div>
        ) : (
          <ScrollArea
            onScrollBottom={async () => {
              if (hasNextPage && !isFetchingNextPage) {
                await fetchNextPage()
              }
            }}
            className="h-full w-full overflow-hidden"
          >
            <div className="space-y-4 overflow-hidden p-4">
              {tasks.map((taskWrapper) => {
                if (!taskWrapper) return null

                const id = taskWrapper.id
                const title = taskWrapper.desc || ''
                const coverUrl = taskWrapper.coverUrl
                const platforms = taskWrapper.platforms || []
                return (
                  <div className="rounded-lg shadow" key={id} onClick={() => setActiveId(id)}>
                    <div className="flex gap-3 p-3">
                      <div className="relative h-[6.5rem] w-[4.8rem] flex-shrink-0 overflow-hidden rounded-lg">
                        <ImageBase src={coverUrl || ''} />
                        <div className="absolute bottom-0 left-0 h-8 w-full bg-gradient-to-t from-black/70 to-transparent p-2">
                          <TaskItemIcon type={taskWrapper.publishType} />
                        </div>
                        {taskWrapper.isAppContent && (
                          <div className="absolute right-0 top-0 flex items-center justify-center rounded-bl-lg rounded-tr-lg bg-foreground/50 px-1.5 py-0.5 text-xs text-secondary">
                            App
                          </div>
                        )}
                      </div>
                      <div className="flex flex-1 flex-col justify-between overflow-hidden">
                        <div className="flex flex-col">
                          <div className="flex h-7">
                            <div className="w-0 grow truncate text-base font-medium">{title}</div>
                          </div>
                          <ScrollArea className="flex-1 overflow-hidden">
                            <div className="flex items-center gap-2">
                              {platforms.map((platform: string) => {
                                const platformInfo = getPlatformByName(platform)
                                return (
                                  <img
                                    key={platform}
                                    src={platformInfo.icon}
                                    alt={platformInfo.name}
                                    className="h-6 w-6 rounded-full"
                                  />
                                )
                              })}
                            </div>
                            <ScrollBar className="h-0" orientation="horizontal" />
                          </ScrollArea>
                        </div>
                        <div className="flex items-end justify-between">
                          <TaskStatus status={taskWrapper.taskSetStatus} />
                          <span className='text-muted-foreground'>{DateUtils.formatTime(taskWrapper.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {hasNextPage && (
              <div className="flex items-center justify-center py-4">
                <LoadingContainer className="size-5" />
              </div>
            )}
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        )}
      </PullToRefresh>

      <TaskDetail id={activeId} onClose={() => setActiveId('')} />
    </div>
  )
}

export default Tasks
