import { Page, Navbar, List, ListItem } from 'framework7-react'
import { Button } from '@/components/ui/button'
import { Logo } from '@/components/Logo'
import { logout } from '@/lib/http'
import { LoadingButton } from '@/components/loadingButton'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'
import { useUpdate } from '@/hooks/common/useUpdate'

const AboutPage = () => {
  const handleLogout = () => {
    // TODO: 实现登出逻辑
    alertBaseManager.open({
      title: '退出登录',
      description: '确定要退出登录吗？',
      okText: '确定',
      cancelText: '取消',
      onSubmit: () => {
        logout()
      },
    })
  }

  const updateMutation = useUpdate()

  const handleCheckUpdate = () => {
    updateMutation.mutate()
  }
  return (
    <Page name="about" pageContent={false}>
      <Navbar title="关于蚁小二" backLink />
      <div className="h-full pt-safe-offset-10">
        <div className="bg-page-bg flex h-full flex-col justify-between px-4 pt-4 pb-safe">
          <div className="flex flex-col gap-2">
            <div className="flex flex-col items-center py-10">
              <Logo />
            </div>

            <List dividersIos dividersMd strong className="overflow-hidden rounded-lg">
              <ListItem title="版本号" after={import.meta.env.VITE_APP_VERSION || '1.0.0'} />
              <ListItem title="隐私政策" link="/privacy-policy/" />
              <ListItem title="用户服务协议" link="/user-agreement/" />
              <ListItem
                title="注销账号"
                link
                onClick={() => {
                  alertBaseManager.open({
                    title: '蚁小二',
                    description:
                      '请使用电脑打开链接下载蚁小二客户端注销账号https://www.yixiaoer.cn/',
                    buttons: [],
                    okText: '知道了',
                  })
                }}
              />
            </List>

            <LoadingButton
              size="lg"
              variant="outline"
              className="!bg-background !text-foreground"
              onClick={handleCheckUpdate}
              disabled={updateMutation.isPending}
              isPending={updateMutation.isPending}
            >
              检测更新
            </LoadingButton>
          </div>

          <div className="flex flex-col gap-2">
            <Button
              size="lg"
              variant="secondary"
              onClick={handleLogout}
              className="text-destructive"
            >
              退出登录
            </Button>
            <div className="mb-2 text-center text-sm text-muted-foreground">
              Copyright © 2025. 长沙草儿绽放科技有限公司
            </div>
          </div>
        </div>
      </div>
    </Page>
  )
}

export default AboutPage
