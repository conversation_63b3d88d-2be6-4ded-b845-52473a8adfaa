import { <PERSON>, Navbar, Nav<PERSON><PERSON><PERSON>, NavTitle, NavRight } from 'framework7-react'
import { PublishSheet } from '../components/publishSheet'
import { CreateTaskRequest, PlatformAccount } from '@/types'
import { useEffect, useMemo, useState } from 'react'
import { buildImageTextValidationSchema } from '@/lib/validations/imageTextForm'
import { createFormDataCompatibleSchema } from '@/lib/validations/formTypeSchema'
import { useController, useForm } from 'react-hook-form'
import { z } from 'zod'
import { isEmpty } from 'lodash'
import { AccountSelector } from '@/components/accounts'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form'
import { imageTextPlatforms } from '@/lib/specification/content-type/supports'
import { Label } from '@/components/ui/label'
import { ImageSelector } from './ImageSelector/ImageSelector'
import { ImageBase } from '@/types/media'
import { ErrorFab } from '@/components/ErrorMessagePopup'
import { Input } from '@/components/ui/input'
import { DescriptionEditor } from '@/components/DescriptionEditor'
import { htmlService } from '@/services/html-service'
import { Separator } from '@/components/ui/separator'
import { TimingPublish } from '@/components/TimingPublish'
import { MusicSelector } from './musicSelector'
import { useImageTextVisualHint } from '@/lib/visual-hint/image-text'
import { getPlatformByName } from '@/lib/platform'
import { useWechatAccountLock } from '@/hooks/publish/useWechatAccountLock'
import { usePublishMutation } from '@/hooks/publish/usePublishMutation'
import { uploadFile } from '@/lib/http'
import { PhotoProvider, PhotoView } from 'react-photo-view'
import { usePublishErrors } from '@/hooks/publish/usePublishErrors'

const PublishImageTextPage = () => {
  const [selectedAccounts, setSelectedAccounts] = useState<PlatformAccount[]>([])
  const selectedPlatformsNames = useMemo(() => {
    return [...new Set(selectedAccounts.map((account) => account.platformName))]
  }, [selectedAccounts])

  // 根据选择的平台动态构建验证Schema
  const validationSchema = useMemo(
    () => buildImageTextValidationSchema(selectedPlatformsNames),
    [selectedPlatformsNames],
  )

  const { musicSupport, scheduledTimeSupport } = useImageTextVisualHint(
    selectedAccounts.map((account) => getPlatformByName(account.platformName)),
  )

  // 使用兼容适配器
  const { zodResolver: compatibleResolver } = createFormDataCompatibleSchema(validationSchema)

  const { isUnlockedRef } = useWechatAccountLock(selectedAccounts, setSelectedAccounts)

  const form = useForm<z.infer<typeof validationSchema>>({
    resolver: compatibleResolver,
    mode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      images: [],
      title: '',
      description: '',
      topics: [],
      timing: 0,
      cover: undefined,
      music: undefined,
    },
  })

  const { publishMutation, publishDialogJsx, setPublishDescription, setPublishProgress, setOpen } =
    usePublishMutation(isUnlockedRef)

  const {
    getValues,
    formState: { errors, isValid },
    trigger,
  } = form

  useEffect(() => {
    trigger()
  }, [trigger, validationSchema])

  const handlePublish = async (type: 'local' | 'cloud') => {
    trigger()
    if (isValid) {
      const { images, cover, music, title, description, timing } = getValues()
      // 整理数据
      const req = {
        publishType: 'imageText',
        publishChannel: type,
        isTimed: timing,
        desc: htmlService.getTextFromHtml(description),
        isDraft: false,
        isAppContent: true,
        platforms: selectedAccounts.map((account) => account.platformName),
        platformAccounts: selectedAccounts.map((item) => ({
          platformAccountId: item.id,
        })),
      } as CreateTaskRequest
      let totalSize = 0
      let uploadedSize = 0
      const imageKeys: string[] = []
      try {
        setOpen(true)

        // 计算所有视频和封面的大小
        images.forEach((item) => {
          const size = 'size' in item ? item.size : 0
          totalSize += size
        })
        totalSize += cover?.size ?? 0
        // 加上任务封面大小
        totalSize += cover?.size ?? 0
        // 同步上传视频
        for (let i = 0; i < images.length; i++) {
          setPublishDescription(`正在上传第 ${i + 1}/${images.length} 张图片...`)
          const item = images[i]
          const file = 'path' in item ? item.path : item.arrayBuffer
          const size = item.size
          const videoKey = await uploadFile(file, 'cloud-publish', 'tianyiyun', (progress) => {
            const currentFileProgress = (progress / 100) * size
            setPublishProgress(Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100))
          })
          uploadedSize += size
          imageKeys.push(videoKey)
        }
        setPublishDescription('正在上传封面...')
        const coverSize = cover.size
        const coverFile = 'arrayBuffer' in cover ? cover.arrayBuffer : cover.path
        const coverKey = await uploadFile(coverFile, 'cloud-publish', 'tianyiyun', (progress) => {
          const currentFileProgress = (progress / 100) * coverSize
          setPublishProgress(Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100))
        })
        uploadedSize += coverSize
        // 上传任务封面
        setPublishDescription('正在上传任务封面...')
        const taskCoverSize = cover.size
        const taskCoverFile = 'arrayBuffer' in cover ? cover.arrayBuffer : cover.path
        const taskCoverKey = await uploadFile(
          taskCoverFile,
          'material-library',
          'aliyun',
          (progress) => {
            const currentFileProgress = (progress / 100) * taskCoverSize
            setPublishProgress(Math.floor(((uploadedSize + currentFileProgress) / totalSize) * 100))
          },
        )
        req.coverKey = taskCoverKey
        req.publishArgs = {
          accounts: selectedAccounts.map((item) => ({
            accountId: item.id,
          })),
          cover: {
            size: cover?.size,
            key: coverKey,
            width: cover?.width,
            height: cover?.height,
          },
          images: images.map((item, index) => ({
            size: item?.size,
            key: imageKeys[index],
            width: item?.width,
            height: item?.height,
          })),
          title,
          description,
          isDraft: false,
          music,
          timing,
        }
        setPublishDescription('正在发布...')
        publishMutation.mutate(req)
      } catch (error) {
        console.error(error)
        setOpen(false)
      }
    }
  }

  const topicsControl = useController({ control: form.control, name: 'topics' })
  const coverControl = useController({ control: form.control, name: 'cover' })

  const errorMessages = usePublishErrors(errors)

  return (
    <Page name="publish-image-text" id="publish-image-text">
      <Navbar>
        <NavLeft backLink />
        <NavTitle>发布图文</NavTitle>
        <NavRight>
          <PublishSheet
            accounts={selectedAccounts}
            disabled={!isEmpty(errorMessages) || !selectedAccounts.length}
            onPublish={handlePublish}
            videoAccounts={[]}
          />
        </NavRight>
      </Navbar>
      <Form {...form}>
        <form className="flex flex-col gap-5 px-5 py-4">
          <FormItem>
            <Label>
              账号<span className="text-destructive">*</span>
            </Label>
            <AccountSelector
              key="multiple"
              mode="multiple"
              selectedAccounts={selectedAccounts}
              onAccountsChange={(accounts) => {
                setSelectedAccounts(accounts)
              }}
              filter={(platform) =>
                imageTextPlatforms.some((item) => item.name === platform.platformName)
              }
            />
          </FormItem>
          <Separator orientation="horizontal" className="h-[1px] bg-secondary" />

          <FormField
            control={form.control}
            name="images"
            render={({ field }) => (
              <FormItem>
                <FormLabel>图片</FormLabel>
                <FormControl>
                  <ImageSelector
                    images={field.value as ImageBase[]}
                    onChange={(val) => {
                      field.onChange(val)
                      if (!coverControl.field.value) {
                        coverControl.field.onChange(val[0])
                      }
                    }}
                    setCover={(image) => {
                      coverControl.field.onChange(image)
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          {coverControl.field.value && (
            <FormField
              control={form.control}
              name="cover"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>封面</FormLabel>
                  <FormControl>
                    <PhotoProvider
                      bannerVisible={false}
                      portalContainer={document.querySelector('.view-main') as HTMLElement}
                    >
                      <PhotoView src={field.value.url}>
                        <img
                          className={'size-20 rounded border-destructive object-cover'}
                          alt=""
                          src={field.value.url}
                        />
                      </PhotoView>
                    </PhotoProvider>
                  </FormControl>
                </FormItem>
              )}
            />
          )}
          <div>
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="添加标题"
                      className="border-none px-0 text-base font-medium shadow-none"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <DescriptionEditor
                      description={field.value}
                      onChange={(description) => {
                        const topics = htmlService.getTopics(description)
                        field.onChange(description)
                        topicsControl.field.onChange(topics)
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <Separator orientation="horizontal" className="h-[1px] bg-secondary" />
          {musicSupport && (
            <FormField
              control={form.control}
              name="music"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <MusicSelector
                      accounts={selectedAccounts}
                      value={field.value}
                      onChange={(music) => {
                        field.onChange(music)
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}

          {scheduledTimeSupport && (
            <FormField
              control={form.control}
              name="timing"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <TimingPublish
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value)
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
        </form>
      </Form>
      <ErrorFab errors={errorMessages} />
      {publishDialogJsx}
    </Page>
  )
}

export default PublishImageTextPage
