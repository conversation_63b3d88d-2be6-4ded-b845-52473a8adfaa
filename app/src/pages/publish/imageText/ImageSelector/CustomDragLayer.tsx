import { useDragLayer } from 'react-dnd'

export function CustomDragLayer() {
  const { item, isDragging, currentOffset } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    isDragging: monitor.isDragging(),
    currentOffset: monitor.getSourceClientOffset(),
  }))

  if (!isDragging || !currentOffset) return null

  return (
    <div
     className='fixed pointer-events-none top-0 left-0 z-10'
      style={{
        transform: `translate(${currentOffset.x}px, ${currentOffset.y}px)`,
      }}
    >
      <img
        src={item.url}
        alt=""
        className='size-20 rounded-lg object-cover'
      />
    </div>
  )
}