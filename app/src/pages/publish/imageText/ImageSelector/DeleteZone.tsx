import { Haptics, ImpactStyle } from '@capacitor/haptics'
import { Trash2 } from 'lucide-react'
import { useEffect } from 'react'
import { useDrop } from 'react-dnd'

export function DeleteZone({ onDrop }: { onDrop: (index: number) => void }) {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: 'IMAGE',
    drop: (item: { index: number }) => {
      onDrop(item.index)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  })

  useEffect(() => {
    if (isOver) {
      Haptics.impact({ style: ImpactStyle.Medium })
    }
  }, [isOver])
  if (!canDrop) {
    return null
  }

  return (
    <div
      ref={(node) => {
        if (node) {
          drop(node)
        }
      }}
      className="fixed bottom-0 left-0 right-0 z-10 flex items-center justify-center bg-destructive text-lg text-background pb-safe"
    >
      <div className="flex h-14 flex-col items-center justify-center gap-1 text-sm">
        <Trash2 className="h-4 w-4" />
        <span>{isOver ? '松开删除' : '拖到这里删除'}</span>
      </div>
    </div>
  )
}
