import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { useCallback, useMemo, useRef, useState, createContext, useContext } from 'react'
import { useDrag, useDrop, XYCoord } from 'react-dnd'
import { DragPreviewImage } from 'react-dnd'
import { ImageBase } from '@/types/media'
import { handleImageSelect } from '@/lib/media'
import { DiscAlbum, Plus, Trash } from 'lucide-react'
import { ScrollArea, ScrollBar } from '@/components/ScrollArea'
import { PhotoProvider, PhotoView } from 'react-photo-view'
import 'react-photo-view/dist/react-photo-view.css'
import { OverlayRenderProps } from 'react-photo-view/dist/types'
import { DeleteZone } from './DeleteZone'
import { CustomDragLayer } from './CustomDragLayer'

const MAX_IMAGES = 35

const PhotoKeyContext = createContext<{ setPhotoKey: React.Dispatch<React.SetStateAction<number>> }>(null!)

interface ImageSelectorProps {
  images: ImageBase[]
  onChange: (images: ImageBase[]) => void
  setCover: (image: ImageBase) => void
}

export function ImageSelector({ images, onChange, setCover }: ImageSelectorProps) {
  const [photoKey, setPhotoKey] = useState(0)

  const selectFromLocalFiles = async () => {
    try {
      // 实现选择本地文件的逻辑
      const result = await handleImageSelect(MAX_IMAGES - images.length)
      onChange([...images, ...result])
    } catch {
      // 处理错误
    }
  }

  const handleRemoveImage = useCallback(
    (index: number, props: OverlayRenderProps) => {
      if (props.images.length === 1) {
        props.onClose()
      } else {
        if (props.index === props.images.length - 1) {
          props.onIndexChange(props.index - 1)
        }
      }
      const updatedImages = images.filter((_, i) => i !== index)
      onChange(updatedImages)
    },
    [images, onChange],
  )

  const moveImage = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      const newImages = [...images]
      const [reorderedItem] = newImages.splice(dragIndex, 1)
      newImages.splice(hoverIndex, 0, reorderedItem)
      onChange(newImages)
    },
    [images, onChange],
  )

  const handleSetCover = useCallback(
    (index: number, props: OverlayRenderProps) => {
      setCover(images[index])
      props.onClose()
    },
    [images, setCover],
  )

  const handleDropToDelete = useCallback(
    (index: number) => {
      const updatedImages = images.filter((_, i) => i !== index)
      onChange(updatedImages)
    },
    [images, onChange],
  )

  const btnList = useMemo(() => {
    return [
      {
        label: '设为封面',
        onClick: handleSetCover,
        icon: DiscAlbum,
      },
      {
        label: '删除',
        onClick: handleRemoveImage,
        icon: Trash,
      },
    ]
  }, [handleRemoveImage, handleSetCover])

  return (
    <PhotoKeyContext.Provider value={{ setPhotoKey }}>
      <ScrollArea className="w-full overflow-hidden">
        <PhotoProvider
          key={photoKey}
          maskOpacity={0.5}
          photoClosable
          bannerVisible={false}
          portalContainer={document.querySelector('.view-main') as HTMLElement}
          overlayRender={(props) => {
            return (
              <div className="absolute bottom-2 right-2 z-20 flex gap-2 pb-safe">
                {btnList.map((btn) => (
                  <Button
                    key={btn.label}
                    className="flex h-8 items-center gap-1 rounded-full bg-[#1F1F1F] px-3 text-background"
                    onClick={() => {
                      btn.onClick(props.index, props)
                    }}
                  >
                    <btn.icon className="size-4" />
                    {btn.label}
                  </Button>
                ))}
                <div className="flex items-center rounded-full bg-[#1F1F1F] px-3 text-background">
                  {props.index + 1} / {props.images.length}
                </div>
              </div>
            )
          }}
        >
          <div className="flex gap-2">
            {images.map((image, index) => (
              <DraggableImage
                key={image.url}
                image={image}
                index={index}
                moveImage={moveImage}
                setCover={() => setCover(image)}
              />
            ))}
            {images.length < MAX_IMAGES && (
              <Button
                variant="secondary"
                onClick={selectFromLocalFiles}
                size="icon"
                className="size-20 rounded"
              >
                <Plus />
              </Button>
            )}
          </div>
        </PhotoProvider>
        <ScrollBar orientation="horizontal" className="h-0" />
      </ScrollArea>
      <DeleteZone onDrop={handleDropToDelete} />
      <CustomDragLayer />
    </PhotoKeyContext.Provider>
  )
}

interface DraggableImageProps {
  image: ImageBase
  index: number
  moveImage: (dragIndex: number, hoverIndex: number) => void
  setCover: () => void
}

interface DragItem {
  index: number
  id: string
  type: string
  url: string
}

function DraggableImage({ image, index, moveImage }: DraggableImageProps) {
  const ref = useRef<HTMLDivElement>(null)
  const { setPhotoKey } = useContext(PhotoKeyContext)

  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: string | symbol | null }>({
    accept: 'IMAGE',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      }
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // 不替换自身
      if (dragIndex === hoverIndex) {
        return
      }

      // 获取屏幕上的矩形
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // 获取水平中点
      const hoverMiddleX = (hoverBoundingRect.left + hoverBoundingRect.right) / 2

      // 确定鼠标位置
      const clientOffset = monitor.getClientOffset()

      // 获取距离左侧的像素
      const hoverClientX = (clientOffset as XYCoord).x - hoverBoundingRect.left

      // 向右拖动
      if (dragIndex < hoverIndex && hoverClientX > hoverMiddleX) {
        return
      }

      // 向左拖动
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return
      }

      // 执行移动操作
      moveImage(dragIndex, hoverIndex)

      // 更新拖拽项的索引
      item.index = hoverIndex
    },
  })

  const [{ isDragging }, drag, preview] = useDrag({
    type: 'IMAGE',
    item: { index, ...image },
    end: () => {
      setPhotoKey(Date.now())
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [isPressing, setIsPressing] = useState(false)
  const pressTimer = useRef<NodeJS.Timeout | null>(null)

  drag(drop(ref))

  const isDraggingNow = isDragging || isPressing

  return (
    <>
      <DragPreviewImage connect={preview} src={image.url} />
      <div
        ref={ref}
        onTouchStart={() => {
          pressTimer.current = setTimeout(() => setIsPressing(true), 100)
        }}
        onTouchEnd={() => {
          if (pressTimer.current) clearTimeout(pressTimer.current)
          setIsPressing(false)
        }}
        onTouchMove={() => {
          if (pressTimer.current) clearTimeout(pressTimer.current)
          setIsPressing(false)
        }}
        onTouchCancel={() => {
          if (pressTimer.current) clearTimeout(pressTimer.current)
          setIsPressing(false)
        }}
        className="relative size-20 overflow-hidden rounded"
        data-handler-id={handlerId}
      >
        <PhotoView src={image.url}>
          <img
            className={cn('h-full w-full object-cover')}
            alt=""
            src={image.url}
            style={{
              opacity: isDraggingNow ? 0.5 : 1,
              transform: isDraggingNow ? 'scale(1.1)' : 'none',
              transition: 'transform 0.1s ease',
            }}
          />
        </PhotoView>
      </div>
    </>
  )
}
