import { useMemo, useRef, useState } from 'react'
import type { PlatformAccount } from '@/types'
import { ChevronRight, Music } from 'lucide-react'
import { getPlatformByName, platformNames, platforms } from '@/lib/platform'
import { ImageTextForm } from '@/lib/validations/imageTextForm'
import { MusicPlatformDataItem } from '@/lib/validations/formTypeSchema'
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer'
import { AvatarBase } from '@/components/Avater'
import Popup from '@/components/Popup'
import { MusicList } from './MusicList'
import { XBtn } from '@/components/XBtn'

interface MusicSelectorProps {
  accounts: PlatformAccount[]
  value: ImageTextForm['music']
  onChange: (music: ImageTextForm['music']) => void
}

export function MusicSelector({ value = {}, onChange, accounts }: MusicSelectorProps) {
  // 获取 accounts 中的 douyin 和 kuaiShou的 第一个AccountSeesion
  const [douyinAccount, kuaiShouAccount, weiXinShiPinHaoAccount] = useMemo(() => {
    const douyinAccount = accounts.find((account) => account.platformName === platformNames.DouYin)
    const kuaiShouAccount = accounts.find(
      (account) => account.platformName === platformNames.KuaiShou,
    )
    const weiXinShiPinHaoAccount = accounts.find(
      (account) => account.platformName === platformNames.WeiXinShiPinHao,
    )
    return [douyinAccount, kuaiShouAccount, weiXinShiPinHaoAccount]
  }, [accounts])

  const handleSelectMusic = (selectedMusic: MusicPlatformDataItem) => {
    if (!accountRef.current) return
    onChange({
      ...value,
      [accountRef.current.platformName]: selectedMusic,
    })
  }

  const showPlatforms = useMemo(() => {
    const dyMusic = value[platforms.DouYin.name]
    const ksMusic = value[platforms.KuaiShou.name]
    const sphMusic = value[platforms.WeiXinShiPinHao.name]
    return [
      {
        name: platforms.DouYin.name,
        music: dyMusic,
        enable: !!douyinAccount,
        account: douyinAccount ?? null,
      },
      {
        name: platforms.KuaiShou.name,
        music: ksMusic,
        enable: !!kuaiShouAccount,
        account: kuaiShouAccount ?? null,
      },
      {
        name: platforms.WeiXinShiPinHao.name,
        music: sphMusic,
        enable: !!weiXinShiPinHaoAccount,
        account: weiXinShiPinHaoAccount ?? null,
      },
    ].filter((x) => x.enable)
  }, [douyinAccount, kuaiShouAccount, value, weiXinShiPinHaoAccount])

  const handleClick = (account: PlatformAccount) => {
    accountRef.current = account
    setIsOpen(true)
  }

  const [isOpen, setIsOpen] = useState(false)
  const accountRef = useRef<PlatformAccount>(null)

  return (
    <Drawer repositionInputs={false}>
      <DrawerTrigger asChild>
        <div className="flex cursor-pointer items-center justify-between gap-4">
          <span className="flex items-center gap-3">
            <Music className="h-5 w-5" />
            <span className="text-base font-medium">选择音乐</span>
          </span>
          <ChevronRight className="h-5 w-5" />
        </div>
      </DrawerTrigger>
      <DrawerContent>
        <div className="flex flex-col gap-2 pb-safe">
          {showPlatforms
            .filter((item) => item.account !== null)
            .map((item) => {
              const platformName = item.name
              const platform = getPlatformByName(platformName)
              const music = item.music
              return (
                <div
                  className="flex h-12 items-center justify-between px-4"
                  onClick={() => handleClick(item.account!)}
                  key={item.name}
                >
                  <div className="flex items-center gap-2">
                    <AvatarBase src={platform.icon} className="h-7 w-7" alt={platformName} />
                    <span className="text-sm font-medium">{platformName}</span>
                  </div>
                  {music ? (
                    <div className="flex items-center gap-2 text-sm font-normal text-muted-foreground">
                      <span>{`${music.yixiaoerName} - ${music.authorName}`}</span>
                      <XBtn
                        onClick={(e) => {
                          e.stopPropagation()
                          onChange({
                            ...value,
                            [platformName]: undefined,
                          })
                        }}
                      />
                    </div>
                  ) : (
                    <ChevronRight />
                  )}
                </div>
              )
            })}
        </div>
        <Popup<MusicPlatformDataItem>
          title="选择音乐"
          open={isOpen}
          onOpenChange={setIsOpen}
          onConfirm={(val) => {
            if (val) {
              handleSelectMusic(val)
            }
          }}
        >
          {({ setData, data }) =>
            accountRef.current && (
              <MusicList
                value={data}
                onChange={setData}
                platformAccountId={accountRef.current.id}
                isCategory={accountRef.current.platformName === platformNames.DouYin}
              />
            )
          }
        </Popup>
      </DrawerContent>
    </Drawer>
  )
}
