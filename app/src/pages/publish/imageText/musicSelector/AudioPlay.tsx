import { Button } from '@/components/ui/button'
import Play from '@/assets/publish/play.svg?react'
import Pause from '@/assets/publish/pause.svg?react'

export function AudioPlay({ isPlaying }: { isPlaying: boolean }) {
  return (
    <Button
      size="icon"
      variant="default"
      className="h-6 w-6 rounded-full bg-transparent hover:bg-transparent"
    >
      {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
    </Button>
  )
}
