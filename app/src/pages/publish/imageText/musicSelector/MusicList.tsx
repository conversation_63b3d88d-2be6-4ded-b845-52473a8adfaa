import { ScrollArea } from '@/components/ScrollArea'
import { Fragment, useEffect, useMemo, useState } from 'react'
import { AudioPlay } from './AudioPlay'
import { DateUtils } from '@/utils'
import { LoadingContainer } from '@/components/loading'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { getMusicCategory, getMusicList } from '@/lib/http'
import { MusicPlatformDataItem } from '@/lib/validations/formTypeSchema'
import SearchInput from '@/components/searchInput'
import { Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import ScrollableTabs from '@/components/scrollable-tabs'
import { useControllableState } from '@radix-ui/react-use-controllable-state'

export function MusicList({
  onChange,
  value: propValue,
  platformAccountId,
  isCategory,
}: {
  onChange: (music: MusicPlatformDataItem) => void
  value?: MusicPlatformDataItem
  platformAccountId: string
  isCategory?: boolean
}) {
  const [playUrl, setPlayUrl] = useState<string>()
  const [category, setCategory] = useState<string>()
  const [keyWord, setKeyWord] = useState('')
  const [value, setValue] = useControllableState<MusicPlatformDataItem | undefined>({
    defaultProp: propValue,
    onChange: (value) => value && onChange(value),
  })
  const categoryQuery = useQuery({
    queryKey: ['musicCategory', platformAccountId],
    queryFn: () => getMusicCategory(platformAccountId),
    enabled: !!isCategory,
  })

  useEffect(() => {
    if (categoryQuery.data?.dataList) {
      setCategory(categoryQuery.data.dataList[0].yixiaoerId)
    }
  }, [categoryQuery.data?.dataList])
  const listQuery = useInfiniteQuery({
    queryKey: ['musicList', platformAccountId, keyWord, category],
    queryFn: ({ pageParam }) => {
      const params = {
        keyWord,
        nextPage: pageParam === '' ? undefined : pageParam,
      } as Parameters<typeof getMusicList>[1]
      if (isCategory && !keyWord) {
        params.categoryId = category
        params.categoryName = categoryQuery.data?.dataList.find(
          (item) => item.yixiaoerId === category,
        )?.yixiaoerName
      }
      return getMusicList(platformAccountId, params)
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: '',
    enabled: !!platformAccountId && (isCategory ? !!(keyWord || category) : true),
  })

  // audio 对象
  const audio = useMemo(() => new Audio(), [])
  useEffect(() => {
    return () => {
      audio.pause()
    }
  }, [audio])
  return (
    <div className="flex flex-1 flex-col overflow-hidden">
      <div className="px-4 pt-4">
        <SearchInput placeholder="搜索音乐" onSearch={setKeyWord} />
      </div>
      {keyWord === '' && isCategory && (
        <div className="px-4">
          <ScrollableTabs
            tabs={
              categoryQuery.data?.dataList.map((item) => ({
                label: item.yixiaoerName,
                value: item.yixiaoerId,
              })) || []
            }
            defaultValue={category}
            onValueChange={setCategory}
          />
        </div>
      )}
      <ScrollArea
        className="flex-1 px-4"
        onScrollBottom={() => {
          if (listQuery.hasNextPage && !listQuery.isFetchingNextPage) listQuery.fetchNextPage()
        }}
      >
        <div className="pb-safe">
          {listQuery.data?.pages.map((group, i) => (
            <Fragment key={i}>
              {group.dataList?.map((music) => {
                const isPlaying = playUrl === music.playUrl
                const isSelected = value?.yixiaoerId === music.yixiaoerId
                return (
                  <div
                    key={music.yixiaoerId}
                    className={cn(
                      'flex h-14 items-center justify-between gap-2 rounded-md px-3 text-sm text-foreground',
                      {
                        'text-primary': isSelected,
                      },
                    )}
                    onClick={() => {
                      if (!isSelected) {
                        setValue(music)
                      }
                      if (!isPlaying) {
                        audio.src = music.playUrl
                        audio.play()
                        setPlayUrl(music.playUrl)
                      } else {
                        audio.pause()
                        setPlayUrl(undefined)
                      }
                    }}
                  >
                    <div className="flex items-center gap-4">
                      <AudioPlay isPlaying={isPlaying} />
                      <div className="flex flex-1 flex-col">
                        <div className="line-clamp-1 flex-1">{music.yixiaoerName}</div>
                        <div className="line-clamp-1 text-xs text-muted-foreground">
                          {music.authorName}
                        </div>
                      </div>
                    </div>
                    <div className="text-gray flex items-center space-x-4 text-xs">
                      <span className="text-muted-foreground">
                        {DateUtils.duration.formatSeconds(music.duration, 'minimal')}
                      </span>
                      {isSelected && <Check />}
                    </div>
                  </div>
                )
              })}
            </Fragment>
          ))}
          {(listQuery.isLoading || listQuery.hasNextPage) && (
            <div className="flex h-10 w-full justify-center">
              <LoadingContainer className="size-5" />
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
