import { Page, Navbar, NavLeft, NavTitle, NavRight } from 'framework7-react'
import { PublishSheet } from '../components/publishSheet'

const PublishArticlePage = () => {
  return (
    <Page>
      <Navbar>
        <NavLeft backLink />
        <NavTitle>发布文章</NavTitle>
        <NavRight>
          <PublishSheet
            accounts={[]}
            disabled={false}
            onPublish={() => {}}
            videoAccounts={[]}
          />
        </NavRight>
      </Navbar>
    </Page>
  )
}

export default PublishArticlePage
