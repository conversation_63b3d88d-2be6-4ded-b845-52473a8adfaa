import localIcon from '@/assets/svg/Laptop.svg'
import cloudIcon from '@/assets/svg/CloudArrowUp.svg'
import { useMemo, useState } from 'react'
import { Button } from '@/components/ui/button'
import { PlatformAccount } from '@/types'
import { CircleAlert } from 'lucide-react'
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer'
import { AccountAvatar } from '@/components/accounts/AccountAvatar'
import { useAppStore } from '@/stores'
import { ByteSize } from '@/utils'
import { VideoFormData } from '@/hooks/publish/useVideoPublish'
import FlowWarning from '@/assets/publish/flow-warning.svg?react'

interface SheetProps {
  onPublish: (type: 'local' | 'cloud') => void
  disabled?: boolean
  accounts: PlatformAccount[]
  videoAccounts: VideoFormData['videoAccounts']
}

export const PublishSheet = ({ onPublish, disabled, accounts, videoAccounts }: SheetProps) => {
  const [opened, setOpened] = useState(false)

  const team = useAppStore((state) => state.team)
  // 未设置代理的账号
  const noProxyAccouts = useMemo(() => {
    return accounts.filter((item) => !item.kuaidailiArea)
  }, [accounts])

  // 计算总流量消耗
  const totalTrafficUsage = useMemo(() => {
    if (!videoAccounts) return 0

    if (Array.isArray(videoAccounts)) {
      // 多视频模式：每个视频的大小
      return videoAccounts.reduce((total, item) => {
        if (!item.video) return total
        return total + item.video.size
      }, 0)
    } else {
      // 单视频模式：视频大小 * 账号数量
      if (!videoAccounts.video) return 0
      return videoAccounts.video.size * videoAccounts.platforms.length
    }
  }, [videoAccounts])

  // 剩余流量
  const remainingTraffic = useMemo(() => {
    if (!team) return 0
    return team.networkTraffic - team.useNetworkTraffic
  }, [team])

  // 检查流量是否足够
  const isTrafficEnough = useMemo(() => {
    return remainingTraffic >= totalTrafficUsage
  }, [remainingTraffic, totalTrafficUsage])

  const publishDataList = [
    {
      title: '本机发布',
      description: '(需要电脑端在线)',
      icon: localIcon,
      onClick: () => {
        onPublish('local')
        setOpened(false)
      },
    },
    {
      title: '云端发布',
      description: '(适合异地、户外场景发布)',
      icon: cloudIcon,
      // 代理
      proxyDisabled: noProxyAccouts.length > 0,
      // 流量超过
      flowDisabled: !isTrafficEnough,
      onClick: () => {
        onPublish('cloud')
        setOpened(false)
      },
    },
  ]

  return (
    <Drawer open={opened} onOpenChange={setOpened}>
      <DrawerTrigger asChild>
        <Button disabled={disabled} className="h-8 rounded-full">
          发布
        </Button>
      </DrawerTrigger>
      <DrawerContent className="bg-secondary p-4 pt-0">
        <div className="flex flex-col gap-4 pb-safe">
          <div className="rounded-lg bg-background">
            {publishDataList.map((item) => (
              <div key={item.title} className="flex items-center justify-between">
                <Button
                  variant="link"
                  disabled={item.proxyDisabled || item.flowDisabled}
                  onClick={item.onClick}
                  className="h-16 text-foreground"
                >
                  <img src={item.icon} alt={item.title} className="h-8 w-8 rounded-lg" />
                  <span className="flex items-center justify-between gap-1 py-3">
                    <span className="flex-1 truncate">{item.title}</span>
                    <span className="text-sm text-muted-foreground">{item.description}</span>
                  </span>
                </Button>
                <Drawer>
                  {(item.proxyDisabled || item.flowDisabled) && (
                    <DrawerTrigger asChild>
                      {
                        <Button size="icon" className="mr-2" variant="link">
                          {item.flowDisabled ? (
                            <FlowWarning className="size-5 text-destructive" />
                          ) : (
                            <CircleAlert className="size-4 text-destructive" />
                          )}
                        </Button>
                      }
                    </DrawerTrigger>
                  )}
                  <DrawerContent>
                    <div className="max-h-[90vh] overflow-y-auto overflow-x-hidden p-5">
                      {item.flowDisabled ? (
                        <div className="flex flex-col items-center gap-2 text-base">
                          <span className="text-lg font-semibold">流量不足</span>
                          <span className="text-secondary-foreground">
                            本次所需流量
                            <span className="text-destructive">
                              {new ByteSize(totalTrafficUsage).toString()}
                            </span>
                            /
                            <span className="text-foreground">
                              {new ByteSize(remainingTraffic).toString()}
                            </span>
                          </span>
                          <span className="text-muted-foreground">请前往PC端升级</span>
                        </div>
                      ) : (
                        <div className="flex flex-col gap-2">
                          <div className="text-base text-secondary-foreground">
                            以下帐号未配置代理
                            <span className="text-muted-foreground">(请前往PC端设置代理)</span>
                          </div>
                          {noProxyAccouts.map((account) => (
                            <div key={account.id} className="flex h-16 items-center gap-3">
                              <AccountAvatar account={account} />
                              <div className="flex-1 truncate text-sm font-medium">
                                {account.platformAccountName || '未知名称'}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </DrawerContent>
                </Drawer>
              </div>
            ))}
          </div>
          <Button
            variant="secondary"
            className="h-11 w-full bg-background"
            onClick={() => setOpened(false)}
          >
            取消
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
