import { useState } from 'react'
import { Page, Navbar, f7 } from 'framework7-react'

// 导入背景图片
import publishBgImg from '@/assets/images/publish_bg.png'
// 导入发布图标
import videoIcon from '@/assets/images/publish/video.png'
import videoBgImg from '@/assets/images/publish/video_bg.png'
import videoArrowImg from '@/assets/images/publish/video_arrow.png'
import imageTextIcon from '@/assets/images/publish/image_text.png'
import imageTextBgImg from '@/assets/images/publish/image_text_bg.png'
import imageTextArrowImg from '@/assets/images/publish/image_text_arrow.png'
// import articleIcon from '@/assets/images/publish/article.png'
// import articleBgImg from '@/assets/images/publish/article_bg.png'
// import articleArrowImg from '@/assets/images/publish/article_arrow.png'
// import wechatMpIcon from '@/assets/images/publish/wechat_mp.png'
// import wechatMpBgImg from '@/assets/images/publish/wechat_mp_bg.png'
// import wechatMpArrowImg from '@/assets/images/publish/wechat_mp_arrow.png'
import { useAppStore } from '@/stores'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'

// 定义数据模型
interface PublishData {
  title: string
  iconPath: string
  iconBackgroundColor: string
  borderColor: string
  backgroundImage: string
  backgroundColor: string[]
  arrowPath: string
  isSoon?: boolean
  onClick?: () => void
}

const PublishPage = () => {
  const team = useAppStore((state) => state.team)
  const [publishDataList] = useState<PublishData[]>([
    {
      title: '视频发布',
      iconPath: videoIcon,
      iconBackgroundColor: '#DFDAFF',
      borderColor: '#E9E8F7',
      backgroundImage: videoBgImg,
      backgroundColor: ['#F3F2FF', '#F8FAFE'],
      arrowPath: videoArrowImg,
      onClick: () => {
        f7.view.current.router.navigate('/publish/video/')
      },
    },
    {
      title: '图文发布',
      iconPath: imageTextIcon,
      iconBackgroundColor: '#D2E9FA',
      borderColor: '#D1E9FD',
      backgroundImage: imageTextBgImg,
      backgroundColor: ['#D9F0FE', '#F2FAFE'],
      arrowPath: imageTextArrowImg,
      onClick: () => {
        f7.view.current.router.navigate('/publish/image-text/')
      },
    },
    // {
    //   title: '文章发布',
    //   iconPath: articleIcon,
    //   iconBackgroundColor: '#E2F5F6',
    //   borderColor: '#E9F3F4',
    //   backgroundImage: articleBgImg,
    //   backgroundColor: ['#F0FBFC', '#F8FDFD'],
    //   arrowPath: articleArrowImg,
    //   isSoon: true,
    // },
    // {
    //   title: '公众号发布',
    //   iconPath: wechatMpIcon,
    //   iconBackgroundColor: '#DFDAFF',
    //   borderColor: '#E9E8F7',
    //   backgroundImage: wechatMpBgImg,
    //   backgroundColor: ['#F0F4FE', '#F8FAFE'],
    //   arrowPath: wechatMpArrowImg,
    //   isSoon: true,
    // },
  ])

  return (
    <Page
      name="publish"
      className="w-full bg-background bg-contain bg-no-repeat"
      style={{
        backgroundImage: `url(${publishBgImg})`,
      }}
    >
      <Navbar transparent>
        <div className="flex w-full items-center justify-center">
          <span className="text-lg font-semibold">发布</span>
        </div>
      </Navbar>

      <div className="pt-5">
        <div className="px-5">
          <div className="grid grid-cols-2 gap-5">
            {publishDataList.map((data, index) => (
              <div key={index} className="relative aspect-[0.795/1]">
                <div
                  className="relative h-full overflow-hidden rounded-2xl border"
                  style={{
                    background: `linear-gradient(to bottom, ${data.backgroundColor[0]}, ${data.backgroundColor[1]})`,
                    borderColor: data.borderColor,
                  }}
                >
                  {/* 背景图片 */}
                  <div
                    className="absolute inset-0 bg-right-bottom bg-no-repeat"
                    style={{
                      backgroundSize: '60% auto',
                      backgroundImage: `url(${data.backgroundImage})`,
                    }}
                  />

                  {/* 内容区域 */}
                  <div className="relative flex h-full flex-col justify-between overflow-hidden p-5">
                    {/* 图标 */}
                    <div className="flex flex-col">
                      <div
                        className="flex h-9 w-9 items-center justify-center rounded-lg"
                        style={{
                          backgroundColor: data.iconBackgroundColor,
                        }}
                      >
                        <img src={data.iconPath} alt={data.title} className="h-6 w-6" />
                      </div>

                      {/* 标题 */}
                      <div className="mt-3.5 text-base font-semibold">{data.title}</div>
                    </div>

                    {/* 即将上线标签 */}
                    {data.isSoon && (
                      <div className="absolute right-2.5 top-2.5 rounded-full bg-primary px-2 py-0.5 text-xs text-background">
                        即将上线
                      </div>
                    )}

                    {/* 箭头 */}
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-background">
                      <img src={data.arrowPath} alt="arrow" className="h-4 w-4" />
                    </div>
                  </div>

                  {/* 点击按钮 */}
                  {data.onClick && (
                    <button
                      onClick={() => {
                        if (team.isVip) {
                          data.onClick?.()
                        } else {
                          alertBaseManager.open({
                            title: '没有权限',
                            description: '当前团队无权益，请使用电脑端开通VIP后使用。',
                            okText: '知道了',
                          })
                        }
                      }}
                      className="absolute inset-0 z-20 opacity-0"
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Page>
  )
}

export default PublishPage
