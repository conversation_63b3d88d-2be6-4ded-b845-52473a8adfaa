import { DescriptionEditor } from '@/components/DescriptionEditor'
import { htmlService } from '@/services/html-service'
import type { Control, UseControllerReturn } from 'react-hook-form'
import { Controller } from 'react-hook-form'
import type { VideoFormData } from '@/hooks/publish/useVideoPublish'
import { Input } from '@/components/ui/input'
export const APlatform = ({
  control,
  isAdvancedMode,
  bPlatformControl,
}: {
  control: Control<VideoFormData>
  isAdvancedMode: boolean
  bPlatformControl: UseControllerReturn<VideoFormData, 'bPlatform'>
}) => {
  return (
    <Controller
      control={control}
      name="aPlatform"
      render={({ field }) => (
        <div className="flex flex-col pb-2">
          <Input
            type="text"
            placeholder="添加标题"
            value={field.value.title}
            className="border-none px-0 text-base font-medium shadow-none"
            onChange={(e) => {
              const value = e.target.value
              field.onChange({
                ...field.value,
                title: value,
              })
              if (!isAdvancedMode) {
                bPlatformControl.field.onChange({
                  ...bPlatformControl.field.value,
                  title: value,
                })
              }
            }}
          />
          <DescriptionEditor
            description={field.value.description}
            onChange={(description) => {
              const text = htmlService.getTextFromHtmlWithoutTopic(description)
              const topics = htmlService.getTopics(description)
              field.onChange({
                ...field.value,
                description,
                topics,
              })
              if (!isAdvancedMode) {
                bPlatformControl.field.onChange({
                  ...bPlatformControl.field.value,
                  description: text,
                  tags: topics,
                })
              }
            }}
          />
        </div>
      )}
    />
  )
}
