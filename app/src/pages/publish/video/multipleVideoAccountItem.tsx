import type { VideoFormData } from '@/hooks/publish/useVideoPublish'
import type { UseControllerReturn } from 'react-hook-form'
import { VideoCard } from './videoCard'
import { Badge } from '@/components/Badge'
import { AccountAvatar } from '@/components/accounts/AccountAvatar'
import { AccountSelectorPopup } from '@/components/accounts'
import { useRef, useState } from 'react'
import { CircleChevronDown, CircleChevronUp, Plus } from 'lucide-react'
import { generateUUID } from '@/utils'
import { VideoEditor } from '@/components/VideoEditor'
import { Button } from '@/components/ui/button'
import { cloudVideoPlatforms } from '@/lib/specification/content-type/supports'

export const MultipleVideoAccountItem = ({
  control,
}: {
  control: UseControllerReturn<VideoFormData, 'videoAccounts'>
}) => {
  const { value, onChange } = control.field
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [open, setOpen] = useState(false)
  const currentIndex = useRef(0)
  const [showMore, setShowMore] = useState(false)
  if (value instanceof Array) {
    const visibleValue = showMore ? value : value.slice(0, 5)
    return (
      <div className="flex flex-col gap-2">
        <div className="text-base font-semibold">
          视频/账号/封面<span className="text-destructive">*</span>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {visibleValue.map((item, pIndex) => (
            <div key={item.id} className="flex flex-col gap-1">
              <VideoCard
                item={item.video}
                onClick={() => {
                  currentIndex.current = pIndex
                  setOpen(true)
                }}
                onRemove={() => {
                  onChange(value.filter((_, index) => index !== value.indexOf(item)))
                }}
                onCoverSelected={(cover) => {
                  onChange(
                    value.map((item, index) => (index === pIndex ? { ...item, cover } : item)),
                  )
                }}
                cover={item.cover?.url}
              />
              {item.platforms ? (
                <Badge
                  className="text-xs"
                  onDelete={() => {
                    onChange(
                      value.map((item, index) =>
                        index === pIndex ? { ...item, platforms: undefined } : item,
                      ),
                    )
                  }}
                >
                  <AccountAvatar account={item.platforms} slot="media" size="small" />
                  <span className="grow truncate text-xs">
                    {item.platforms.platformAccountName}
                  </span>
                </Badge>
              ) : (
                <Button
                  variant="secondary"
                  className="text-muted-foreground"
                  onClick={() => {
                    currentIndex.current = pIndex
                    setIsPopupOpen(true)
                  }}
                >
                  <Plus className="h-4 w-4" />
                  选择账号
                </Button>
              )}
            </div>
          ))}
          {value.length < 20 && (
            <VideoCard
              onChange={(video, cover) => {
                onChange([
                  ...value,
                  {
                    id: generateUUID(),
                    video,
                    cover,
                    platforms: undefined,
                  },
                ])
              }}
            />
          )}
        </div>

        {value.length > 5 && (
          <Button
            variant="link"
            className="w-full text-secondary-foreground"
            onClick={() => setShowMore(!showMore)}
          >
            {showMore ? (
              <>
                <CircleChevronUp className="h-4 w-4 text-muted-foreground" /> 收起视频
              </>
            ) : (
              <>
                <CircleChevronDown className="h-4 w-4 text-muted-foreground" /> 更多视频
              </>
            )}
          </Button>
        )}

        <AccountSelectorPopup
          mode={'single'}
          isOpen={isPopupOpen}
          onClose={() => setIsPopupOpen(false)}
          selectedAccounts={[]}
          onSelect={(accounts) => {
            onChange(
              value.map((item, index) =>
                index === currentIndex.current ? { ...item, platforms: accounts[0] } : item,
              ),
            )
          }}
          filter={(platform) => cloudVideoPlatforms.some((item) => item.name === platform.platformName)}
        />
        <VideoEditor
          key={'delete'}
          videoUrl={value[currentIndex.current]?.video?.url || ''}
          onDelete={() => {
            onChange(value.filter((_, index) => index !== currentIndex.current))
          }}
          open={open}
          setOpen={setOpen}
        />
      </div>
    )
  }
  return null
}
