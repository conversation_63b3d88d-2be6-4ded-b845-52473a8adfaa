import UploadIcon from '@/assets/publish/upload.svg?react'
import { handleVideoSelect } from '@/lib/media'
import type { VideoBase } from '@/types/media'
import type { JSX } from 'react'
// 使用函数重载
type AddVideoProps<T extends 'single' | 'multiple'> = {
  mode?: T
  onChange?: T extends 'single' 
    ? (videoInfo: VideoBase) => void 
    : (videoInfo: VideoBase[]) => void
}

export function AddVideo<T extends 'single'>(props: AddVideoProps<T>): JSX.Element
export function AddVideo<T extends 'multiple'>(props: AddVideoProps<T>): JSX.Element
export function AddVideo<T extends 'single' | 'multiple' = 'multiple'>({
  onChange,
  mode = 'multiple' as T,
}: AddVideoProps<T>) {
  async function selectFromLocalFile() {
    if (mode === 'single') {
      const videoInfo = await handleVideoSelect()
      console.log(videoInfo)
      if (videoInfo === null) return
      ;(onChange as (videoInfo: VideoBase) => void)?.(videoInfo)
    } else {
      const videoInfo = await handleVideoSelect()
      if (videoInfo === null) return
      ;(onChange as (videoInfo: VideoBase[]) => void)?.([videoInfo])
    }
  }
  return (
    <div
      onClick={selectFromLocalFile}
      className="flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-md bg-secondary"
    >
      <div className="mb-2">
        <UploadIcon className={'h-4 w-4'} />
      </div>
      <div className="text-sm text-muted-foreground">上传</div>
    </div>
  )
}
