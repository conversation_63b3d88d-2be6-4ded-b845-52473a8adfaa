import { useEffect, useState } from 'react'
import { Page, Navbar, NavLeft, NavTitle, NavRight } from 'framework7-react'
import { HelpPopover } from '@/components/HelpPopover'
import { cloudVideoPlatforms } from '@/lib/specification/content-type/supports'
import { useVideoPublish, type VideoFormData } from '@/hooks/publish/useVideoPublish'
import { Controller, useController } from 'react-hook-form'
import { useVideoVisualHint } from '@/lib/visual-hint/video'
import { getPlatformByName } from '@/lib/platform'
import { CategoryPopup } from './category'
import { ErrorFab } from '@/components/ErrorMessagePopup'
import { PublishSheet } from '../components/publishSheet'
import { APlatform } from './aPlatform'
import { BPlatform } from './bPlatform'
import { MultipleVideoAccountItem } from './multipleVideoAccountItem'
import { SingleVideoAccountItem } from './singleVideoAccountItem'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/Tabs'
import { TabsContent } from '@radix-ui/react-tabs'
import { Separator } from '@radix-ui/react-separator'
import { TimingPublish } from '@/components/TimingPublish'
import { usePublishErrors } from '@/hooks/publish/usePublishErrors'
import { isEmpty } from 'lodash'

const PublishVideoPage = () => {
  const {
    handlePublish,
    selectedAccounts,
    setSelectedAccounts,
    publishDialogJsx,
    formMethods: {
      control,
      watch,
      formState: { errors },
    },
  } = useVideoPublish()

  const [activeSegment, setActiveSegment] = useState<string>('a')

  const videoAccountsControl = useController({
    control,
    name: 'videoAccounts',
  })
  const bPlatformControl = useController({
    control,
    name: 'bPlatform',
  })
  const visualHint = useVideoVisualHint(
    selectedAccounts.map((account) => getPlatformByName(account.platformName)),
  )

  const [isAdvancedMode, setIsAdvancedMode] = useState(false)

  useEffect(() => {
    const { value, onChange } = videoAccountsControl.field
    if (value instanceof Array) {
      const { video, cover, platforms } = value[0]
      if (value.length < 2) {
        const newPlatforms = platforms ? [platforms] : []
        onChange({
          video,
          cover,
          platforms: newPlatforms,
        })
        setSelectedAccounts(newPlatforms)
      } else {
        setSelectedAccounts(value.map((item) => item.platforms).filter((item) => !!item))
      }
    }
  }, [setSelectedAccounts, videoAccountsControl.field])
  const categoriesControl = useController({
    control,
    name: 'categories',
  })

  const errorMessages = usePublishErrors(errors)

  const videoAccounts = watch('videoAccounts')
  return (
    <Page name="publish-video">
      <Navbar>
        <NavLeft backLink />
        <NavTitle>发布视频</NavTitle>
        <NavRight>
          <PublishSheet
            accounts={selectedAccounts}
            disabled={!isEmpty(errorMessages)}
            onPublish={handlePublish}
            videoAccounts={videoAccounts}
          />
        </NavRight>
      </Navbar>

      <form className="flex flex-col gap-5 px-5 py-4">
        {videoAccountsControl.field.value instanceof Array ? (
          <MultipleVideoAccountItem control={videoAccountsControl} />
        ) : (
          <SingleVideoAccountItem
            control={videoAccountsControl}
            selectedAccounts={selectedAccounts}
            setSelectedAccounts={setSelectedAccounts}
          />
        )}

        <Separator orientation="vertical" className="h-[1px] bg-secondary" />

        {/* 基础信息 */}
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-base font-semibold">基础信息</div>
              <HelpPopover popoverKey="video-platforms">
                <span>适配平台</span>
                <div className="mt-2 flex flex-wrap gap-2">
                  {cloudVideoPlatforms.map((platform) => (
                    <img
                      key={platform.name}
                      src={platform.icon}
                      alt={platform.name}
                      className="h-5 w-5 rounded-full"
                    />
                  ))}
                </div>
              </HelpPopover>
            </div>
            {visualHint.hasClassicVideoPlatforms && visualHint.hasModernVideoPlatforms && (
              <div className="flex items-center space-x-2">
                <Label htmlFor="airplane-mode" className="text-muted-foreground">
                  高级模式
                </Label>
                <Switch
                  id="airplane-mode"
                  checked={isAdvancedMode}
                  onCheckedChange={(checked) => {
                    setIsAdvancedMode(checked)
                  }}
                />
              </div>
            )}
          </div>

          {isAdvancedMode ? (
            <div className="overflow-hidden rounded-lg border">
              <Tabs
                value={activeSegment}
                onValueChange={(value) => setActiveSegment(value)}
                defaultValue="value"
                className="flex shrink-0 flex-col overflow-hidden bg-background"
              >
                <TabsList className="h-10 border-b px-4 text-sm">
                  <TabsTrigger className="text-sm" value="a">
                    A类平台基础信息
                  </TabsTrigger>
                  <TabsTrigger value="b">B类平台基础信息</TabsTrigger>
                </TabsList>
                <TabsContent value="a" className="px-4 py-2">
                  <APlatform
                    key="aPlatform"
                    control={control}
                    isAdvancedMode={isAdvancedMode}
                    bPlatformControl={bPlatformControl}
                  />
                </TabsContent>
                <TabsContent value="b" className="px-4 py-2">
                  <BPlatform key="bPlatform" bPlatformControl={bPlatformControl} />
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <APlatform
              key="aPlatform"
              control={control}
              isAdvancedMode={isAdvancedMode}
              bPlatformControl={bPlatformControl}
            />
          )}
        </div>
        <Separator orientation="vertical" className="h-[1px] bg-secondary" />
        <div className="flex flex-col">
          {visualHint.categorySupport && (
            <CategoryPopup
              platforms={visualHint.categoryPlatforms}
              value={categoriesControl.field.value}
              onChange={(value: VideoFormData['categories']) => {
                categoriesControl.field.onChange(value)
              }}
            />
          )}
          {visualHint.scheduledTimeSupport && (
            <Controller
              control={control}
              name="timing"
              render={({ field }) => (
                <TimingPublish
                  value={field.value}
                  onChange={(value) => {
                    field.onChange(value)
                  }}
                />
              )}
            />
          )}
        </div>
      </form>

      <ErrorFab errors={errorMessages} />
      {publishDialogJsx}
    </Page>
  )
}

export default PublishVideoPage
