import { cn } from '@/lib/utils'
import type { ImageBase, VideoBase } from '@/types/media'
import { AddVideo } from './addVideo'
import { captureVideoFrame, handleVideoSelect } from '@/lib/media'
import { VideoEditor } from '@/components/VideoEditor'
import { useState } from 'react'
import PlayIcon from '@/assets/common/play.svg?react'

export const VideoCard = ({
  item,
  onChange,
  className,
  cover,
  onCoverSelected,
  onClick,
}: {
  item?: VideoBase
  onRemove?: () => void
  onChange?: (item: VideoBase, cover: ImageBase) => void
  className?: string
  menus?: React.ReactNode
  footerChildren?: React.ReactNode
  cover?: string
  onCoverSelected?: (cover: ImageBase) => void
  onClick?: () => void
}) => {
  const [showCoverEditor, setShowCoverEditor] = useState(false)
  const handleVideoUpload = async (video: VideoBase | null) => {
    if (!video) {
      video = await handleVideoSelect()
    }
    if (video) {
      const cover = await captureVideoFrame(video.url)
      if (cover) {
        if (video.width) {
          video.width = cover.width
        }
      if (video.height) {
          video.height = cover.height
        }
        onChange?.(video, cover)
      }
    }
  }
  return (
    <div className={cn('relative aspect-[0.75/1] overflow-hidden', className)}>
      {item ? (
        <div className="relative h-full w-full" onClick={onClick}>
          <span className="absolute inset-0 z-10 flex items-center justify-center">
            <PlayIcon className="h-6 w-6 text-background" />
          </span>
          <div className="group/card relative h-full w-full">
            {cover ? (
              <img
                draggable={false}
                alt=""
                src={cover}
                className={cn(
                  'h-full w-full rounded-md bg-foreground',
                  item.height / item.width > 1.5 ? 'object-contain' : 'object-contain',
                )}
              />
            ) : (
              <video
                draggable={false}
                src={item?.url}
                className={cn(
                  'h-full w-full rounded-md bg-foreground',
                  item.height / item.width > 1.5 ? 'object-contain' : 'object-contain',
                )}
              />
            )}
          </div>
        </div>
      ) : (
        <div className="group/card relative h-full w-full">
          <AddVideo mode="single" onChange={handleVideoUpload} />
        </div>
      )}
      {item && (
        <div className="absolute bottom-0 left-0 z-20 flex h-8 w-full gap-0.5 overflow-hidden rounded-b-lg bg-gradient-to-t from-black/70 to-transparent p-1">
          <div
            className="flex w-full items-center justify-center text-xs text-background"
            onClick={() => setShowCoverEditor(true)}
          >
            编辑封面
          </div>
        </div>
      )}
      {item && (
        <VideoEditor
          key={'cover'}
          videoUrl={item.url}
          onCoverSelected={(cover) => {
            onCoverSelected?.(cover)
          }}
          open={showCoverEditor}
          setOpen={setShowCoverEditor}
        />
      )}
    </div>
  )
}
