import type { Platform } from '@/lib/platform'
import type { VideoFormData } from '@/hooks/publish/useVideoPublish'
import type { CascadingPlatformDataItem } from '@/types/platform-data-item'
import { Cascader, type CascaderOption } from '@nutui/nutui-react'
import { allPlatformCategories } from '@/lib/videoCategories'
import { useRef, useState } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { ChevronRight, LayoutGrid } from 'lucide-react'
import { AvatarBase } from '@/components/Avater'

type ExtendedCascaderOption = CascaderOption & {
  raw?: Record<string, unknown>
}

export function CategoryPopup({
  platforms,
  value,
  onChange,
}: {
  platforms: Platform[]
  value: VideoFormData['categories']
  onChange: (value: VideoFormData['categories']) => void
}) {
  const [visible, setVisible] = useState(false)
  const [options, setOptions] = useState<ExtendedCascaderOption[]>([])
  const [selectedValue, setSelectedValue] = useState<string[]>([])
  const currentPlatform = useRef<Platform>(null)
  const uniquePlatforms = platforms.filter(
    (platform, index, self) => index === self.findIndex((t) => t.key === platform.key),
  )
  const handleClick = async (platform: Platform, val?: CascadingPlatformDataItem[]) => {
    const pOptions = allPlatformCategories[platform.key]
    if (!pOptions) {
      return
    }
    setOptions(pOptions)
    currentPlatform.current = platform
    setVisible(true)
    setSelectedValue(val?.map((item) => item.id) ?? [])
  }

  const handleChange = (platform: Platform, val: CascadingPlatformDataItem[]) => {
    onChange({
      ...value,
      [platform.key]: val,
    })
  }
  return (
    <>
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1" className="border-b--0">
          <AccordionTrigger className="py-2 hover:no-underline">
            <div className="flex h-12 cursor-pointer items-center justify-between gap-2">
              <span className="flex items-center gap-3">
                <LayoutGrid className="h-5 w-5" />
                <span className="text-base font-medium">分类</span>
              </span>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col gap-2">
              {uniquePlatforms.map((platform) => {
                const platformName = platform.name
                const categories = value?.[platform.key]
                return (
                  <div
                    className="flex h-12 items-center justify-between"
                    onClick={() => handleClick(platform, categories)}
                    key={platform.key}
                  >
                    <div className="flex items-center gap-2">
                      <AvatarBase src={platform.icon} className="h-7 w-7" alt={platformName} />
                      <span className="text-sm font-medium">{platformName}</span>
                    </div>
                    <div className="flex items-center text-sm font-normal text-muted-foreground">
                      <span>{categories?.map((item) => item.text).join('/')}</span>
                      <ChevronRight />
                    </div>
                  </div>
                )
              })}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Cascader
        visible={visible}
        value={selectedValue}
        title="选择话题"
        options={options}
        optionKey={{
          textKey: 'label',
          valueKey: 'id',
          childrenKey: 'children',
        }}
        closeable
        onClose={() => {
          setVisible(false)
        }}
        onChange={(_, options) => {
          handleChange(
            currentPlatform.current!,
            options.map((item) => {
              const extendedItem = item as ExtendedCascaderOption
              return {
                id: `${item.value}`,
                text: item?.text ?? '',
                raw: extendedItem?.raw ?? {},
              }
            }),
          )
        }}
      />
    </>
  )
}
