import { AccountAvatar } from '@/components/accounts/AccountAvatar'
import { ScrollArea } from '@/components/ScrollArea'
import { Button } from '@/components/ui/button'
import { Drawer, DrawerContent } from '@/components/ui/drawer'
import { PlatformAccount } from '@/types'
import { Link } from 'framework7-react'
import { X } from 'lucide-react'
import { useEffect, useState } from 'react'

export default function PlatformManage({
  platforms,
  onChange,
  children,
}: {
  platforms: PlatformAccount[]
  onChange: (platforms: PlatformAccount[]) => void
  children: React.ReactNode
}) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedPlatforms, setSelectedPlatforms] = useState<PlatformAccount[]>(platforms)

  useEffect(() => {
    setSelectedPlatforms(platforms)
  }, [platforms])

  return (
    <div>
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerContent hideHandle>
          <div className="relative flex h-[50px] items-center justify-center">
            <div className="absolute left-0 top-0 flex h-full w-12 items-center justify-center">
              <Link
                onClick={() => {
                  setIsOpen(false)
                }}
              >
                <X className="h-5 w-5 text-secondary-foreground" />
              </Link>
            </div>
            <span className="text-base font-medium">发布详情</span>
            <div className="absolute right-0 top-0 flex h-full items-center justify-center">
              <Button
                className="text-primary"
                variant="ghost"
                onClick={() => {
                  setIsOpen(false)
                  setTimeout(() => {
                    onChange(selectedPlatforms)
                  }, 300)
                }}
              >
                确定
              </Button>
            </div>
          </div>
          <div className="flex h-[85vh] flex-col">
            <ScrollArea className="grow overflow-hidden">
              <div className="flex flex-col gap-5 px-5 pt-3 pb-safe-offset-4">
                {selectedPlatforms.map((platform) => (
                  <div key={platform.id} className="flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2">
                      <AccountAvatar account={platform} />
                      <span className="text-base">{platform.platformAccountName}</span>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={() => {
                        setSelectedPlatforms(selectedPlatforms.filter((p) => p.id !== platform.id))
                      }}
                      size="icon"
                    >
                      <X className="h-5 w-5 text-muted-foreground" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </DrawerContent>
      </Drawer>
      <div onClick={() => setIsOpen(true)}>{children}</div>
    </div>
  )
}
