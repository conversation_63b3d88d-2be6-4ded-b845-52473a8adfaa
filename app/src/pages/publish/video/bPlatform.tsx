import { DescriptionEditor } from '@/components/DescriptionEditor'
import type { UseControllerReturn } from 'react-hook-form'
import type { VideoFormData } from '@/hooks/publish/useVideoPublish'
import { Chip } from 'framework7-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { PlusCircle } from 'lucide-react'
import { alertBaseManager } from '@/components/alertBase/alertBaseManager'
import { toast } from 'sonner'
export const BPlatform = ({
  bPlatformControl,
}: {
  bPlatformControl: UseControllerReturn<VideoFormData, 'bPlatform'>
}) => {
  return (
    <div className="flex flex-col pb-2">
      <Input
        type="text"
        placeholder="添加标题"
        className="border-none px-0 text-base font-medium shadow-none"
        value={bPlatformControl.field.value.title}
        onChange={(e) => {
          const value = e.target.value
          bPlatformControl.field.onChange({
            ...bPlatformControl.field.value,
            title: value,
          })
        }}
      />
      <DescriptionEditor
        isSupportTopic={false}
        description={bPlatformControl.field.value.description}
        onChange={(description) => {
          bPlatformControl.field.onChange({
            ...bPlatformControl.field.value,
            description,
          })
        }}
      />
      <div className="flex flex-wrap items-center gap-2">
        {bPlatformControl.field.value.tags.map((tag) => (
          <Chip
            deleteable
            onDelete={() => {
              bPlatformControl.field.onChange({
                ...bPlatformControl.field.value,
                tags: bPlatformControl.field.value.tags.filter((t) => t !== tag),
              })
            }}
            text={tag}
          />
        ))}
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            alertBaseManager.open({
              title: '请输入标签',
              type: 'prompt',
              onSubmit: (value) => {
                if (!value) return
                const tags = bPlatformControl.field.value.tags
                if (tags.includes(value)) {
                  toast.warning('标签已存在')
                  return
                }
                bPlatformControl.field.onChange({
                  ...bPlatformControl.field.value,
                  tags: [...tags, value],
                })
              },
            })
          }}
        >
          <PlusCircle className="size-3 text-muted-foreground" />
          添加标签
        </Button>
      </div>
    </div>
  )
}
