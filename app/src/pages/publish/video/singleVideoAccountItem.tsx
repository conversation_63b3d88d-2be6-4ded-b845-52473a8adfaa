import type { UseControllerReturn } from 'react-hook-form'
import type { VideoFormData } from '@/hooks/publish/useVideoPublish'
import { AccountSelector } from '@/components/accounts'
import type { PlatformAccount } from '@/types'
import { VideoCard } from './videoCard'
import { generateUUID } from '@/utils'
import { VideoEditor } from '@/components/VideoEditor'
import { useState } from 'react'
import { cloudVideoPlatforms } from '@/lib/specification/content-type/supports'

export const SingleVideoAccountItem = ({
  control,
  selectedAccounts,
  setSelectedAccounts,
}: {
  control: UseControllerReturn<VideoFormData, 'videoAccounts'>
  selectedAccounts: PlatformAccount[]
  setSelectedAccounts: (accounts: PlatformAccount[]) => void
}) => {
  const { field } = control
  const [open, setOpen] = useState(false)
  if (field.value instanceof Array) {
    return null
  }
  return (
    <>
      <div className="flex flex-col gap-2">
        <div className="text-base font-semibold">
          账号<span className="text-destructive">*</span>
        </div>
        <AccountSelector
          key="multiple"
          mode="multiple"
          selectedAccounts={selectedAccounts}
          onAccountsChange={(accounts) => {
            field.onChange({
              ...field.value,
              platforms: accounts,
            })
            setSelectedAccounts(accounts)
          }}
          filter={(platform) => cloudVideoPlatforms.some((item) => item.name === platform.platformName)}
        />
      </div>
      {/* 视频和封面预览 */}
      <div className="text-base font-semibold">
        视频和封面<span className="text-destructive">*</span>
      </div>
      <div className="grid grid-cols-3 gap-2">
        <VideoCard
          onClick={() => setOpen(true)}
          item={field.value.video}
          cover={field.value.cover?.url}
          onRemove={() => field.onChange(undefined)}
          onChange={(video, cover) => {
            field.onChange({
              ...field.value,
              video,
              cover,
            })
          }}
          onCoverSelected={(cover) => {
            field.onChange({
              ...field.value,
              cover,
            })
          }}
        />
        {field.value.platforms.length < 2 && field.value.video && (
          <VideoCard
            onChange={(video, cover) => {
              if (!(field.value instanceof Array)) {
                field.onChange([
                  {
                    ...field.value,
                    platforms: field.value.platforms[0],
                    id: generateUUID(),
                  },
                  {
                    video,
                    cover,
                    id: generateUUID(),
                  },
                ])
              }
            }}
          />
        )}
      </div>
      <VideoEditor
        key={'delete'}
        videoUrl={field.value.video?.url || ''}
        onDelete={() => {
          field.onChange({
            ...field.value,
            video: undefined,
            cover: undefined,
          })
        }}
        open={open}
        setOpen={setOpen}
      />
    </>
  )
}
