import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Import Framework7
import Framework7 from 'framework7/lite-bundle'
import Framework7React from 'framework7-react'

// Import Framework7 Styles
import '@app/styles/framework7-bundle.css'
import '@app/styles/framework7-custom.css'
import '@app/styles/svg-icons.css'
import '@app/styles/theme.css'

// Import Icons and App Custom Styles
import '@app/index.css'

import { SafeArea } from '@capacitor-community/safe-area'
SafeArea.enable({
  config: {
    customColorsForSystemBars: true,
    statusBarColor: '#00000000', // transparent
    statusBarContent: 'light',
    navigationBarColor: '#00000000', // transparent
    navigationBarContent: 'light',
  },
});

// Init F7 React Plugin
// eslint-disable-next-line react-hooks/rules-of-hooks
Framework7.use(Framework7React)

createRoot(document.getElementById('root')!).render(<App />)