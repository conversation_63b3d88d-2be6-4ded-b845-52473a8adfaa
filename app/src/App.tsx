import { f7, f7ready, App, <PERSON> } from 'framework7-react'
import capacitorApp from '@app/lib/capacitor-app'
import routes from '@app/routes'
import type { AppProps } from 'framework7-react/components/app.js'
// import setupRouterGuard from '@app/lib/router-guard';
import logoSrc from '@/assets/images/icon_login_logo.png'
import { QueryClientProvider } from '@/lib/query'
import { SplashScreen } from '@capacitor/splash-screen'
import { AlertBaseWrapper } from '@/components/alertBase/alertBaseWrapper'
import { Toaster } from '@/components/ui/sonner'
import { useMemo } from 'react'
import { DndProvider } from 'react-dnd'
import { TouchBackend } from 'react-dnd-touch-backend'
import { LoadingToast } from '@/components/loading'

const MyApp = () => {
  // Framework7 Parameters
  const f7params = useMemo(() => {
    return {
      name: 'yixia<PERSON><PERSON>', // App name
      theme: 'ios', // Automatic theme detection
      colors: {
        primary: '#4F46E5',
      },
      // App routes
      routes: routes,
      touch: {
        activeState: false,
        // tapHold: true,
        // mdTouchRipple: false,
        // iosTouchRipple: true,
      },
      // Input settings
      input: {
        scrollIntoViewOnFocus: false,
        scrollIntoViewCentered: false,
      },
      // Capacitor Statusbar settings
      statusbar: {
        iosOverlaysWebView: true,
        androidOverlaysWebView: false,
      },
      // 通知设置
      notification: {
        icon: `<img src="${logoSrc}" />`,
        title: '蚁小二',
      },
      dialog: {
        title: '蚁小二',
        buttonOk: '确定',
        buttonCancel: '取消',
      },
      popup: {
        closeOnEscape: true,
      },
      sheet: {
        closeOnEscape: true,
      },
      popover: {
        closeOnEscape: true,
      },
      actions: {
        closeOnEscape: true,
      },
      smartSelect: {
        popupPush: true,
      },
    } satisfies AppProps
  }, [])

  f7ready(() => {
    // Init capacitor APIs (see capacitor-app.js)
    if (f7.device.capacitor) {
      capacitorApp.init(f7)
    }

    SplashScreen.hide()
    // setupRouterGuard(f7);
    // Call F7 APIs here
  })

  // useEffect(() => {
  //   const insets = getSafeAreaInsetsFromCSS()
  //   console.log(inxqsets)
  //   useSystemStore.getState().setSafeArea(insets)
  // }, [])

  return (
    <QueryClientProvider>
      <App {...f7params}>
        <DndProvider
          backend={TouchBackend}
          options={{
            delayTouchStart: 150,
            enableMouseEvents: false,
          }}
        >
          <View url="/" main className="safe-areas" />
        </DndProvider>
      </App>
      <Toaster
        mobileOffset={{ top: 'calc(var(--safe-area-inset-top) + 16px)' }}
        richColors
        position="top-center"
      />
      <LoadingToast />
      <AlertBaseWrapper />
    </QueryClientProvider>
  )
}

export default MyApp
