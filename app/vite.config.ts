import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path, { resolve } from 'path'
import svgr from 'vite-plugin-svgr'
import packageJson from '../package.json'
import vitePluginImp from 'vite-plugin-imp'
const SRC_DIR = path.resolve(__dirname, './src')
const PUBLIC_DIR = path.resolve(__dirname, './public')
const BUILD_DIR = path.resolve(__dirname, './dist')
const ROOT_DIR = path.resolve(__dirname)
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    plugins: [
      react(),
      svgr(),
      vitePluginImp({
        libList: [
          {
            libName: '@nutui/nutui-react',
            style: (name) => {
              return `@nutui/nutui-react/dist/es/packages/${name}/style/css`
            },
            replaceOldImport: false,
            camel2DashComponentName: false,
          },
        ],
      }),
    ],
    base: '',
    publicDir: PUBLIC_DIR,
    build: {
      outDir: BUILD_DIR,
      assetsInlineLimit: 0,
      emptyOutDir: true,
      rollupOptions: {
        treeshake: false,
      },
    },
    root: ROOT_DIR,
    envDir: process.cwd(),
    resolve: {
      alias: {
        '@app': SRC_DIR,
        '@': resolve(__dirname, './../shared'),
      },
    },
    define: {
      'import.meta.env.VITE_APP_VERSION': JSON.stringify(packageJson.version),
    },
    server: {
      port: 5174,
      host: true,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          secure: false,
        },
      },
    },
  }
})
