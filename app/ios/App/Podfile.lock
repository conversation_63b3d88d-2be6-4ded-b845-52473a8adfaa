PODS:
  - Capacitor (7.2.0):
    - Capac<PERSON><PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorBrowser (7.0.1):
    - Capacitor
  - CapacitorCamera (7.0.1):
    - Capacitor
  - CapacitorCommunitySafeArea (7.0.0-alpha.1):
    - Capacitor
  - CapacitorCordova (7.2.0)
  - CapacitorFilesystem (7.0.1):
    - Capacitor
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapawesomeCapacitorFilePicker (7.0.1):
    - Capacitor
  - CapgoCapacitorUploader (0.0.27):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/browser`)"
  - "CapacitorCamera (from `../../node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/camera`)"
  - "CapacitorCommunitySafeArea (from `../../node_modules/.pnpm/@capacitor-community+safe-area@7.0.0-alpha.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/safe-area`)"
  - "CapacitorCordova (from `../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios`)"
  - "CapacitorFilesystem (from `../../node_modules/.pnpm/@capacitor+filesystem@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/filesystem`)"
  - "CapacitorHaptics (from `../../node_modules/.pnpm/@capacitor+haptics@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/keyboard`)"
  - "CapacitorSplashScreen (from `../../node_modules/.pnpm/@capacitor+splash-screen@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/status-bar`)"
  - "CapawesomeCapacitorFilePicker (from `../../node_modules/.pnpm/@capawesome+capacitor-file-picker@7.0.1_@capacitor+core@7.2.0/node_modules/@capawesome/capacitor-file-picker`)"
  - "CapgoCapacitorUploader (from `../../node_modules/.pnpm/@capgo+capacitor-uploader@0.0.27_@capacitor+core@7.2.0/node_modules/@capgo/capacitor-uploader`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/browser"
  CapacitorCamera:
    :path: "../../node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/camera"
  CapacitorCommunitySafeArea:
    :path: "../../node_modules/.pnpm/@capacitor-community+safe-area@7.0.0-alpha.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/safe-area"
  CapacitorCordova:
    :path: "../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios"
  CapacitorFilesystem:
    :path: "../../node_modules/.pnpm/@capacitor+filesystem@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/filesystem"
  CapacitorHaptics:
    :path: "../../node_modules/.pnpm/@capacitor+haptics@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/keyboard"
  CapacitorSplashScreen:
    :path: "../../node_modules/.pnpm/@capacitor+splash-screen@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/status-bar"
  CapawesomeCapacitorFilePicker:
    :path: "../../node_modules/.pnpm/@capawesome+capacitor-file-picker@7.0.1_@capacitor+core@7.2.0/node_modules/@capawesome/capacitor-file-picker"
  CapgoCapacitorUploader:
    :path: "../../node_modules/.pnpm/@capgo+capacitor-uploader@0.0.27_@capacitor+core@7.2.0/node_modules/@capgo/capacitor-uploader"

SPEC CHECKSUMS:
  Capacitor: 106e7a4205f4618d582b886a975657c61179138d
  CapacitorApp: d63334c052278caf5d81585d80b21905c6f93f39
  CapacitorBrowser: 081852cf532acf77b9d2953f3a88fe5b9711fb06
  CapacitorCamera: eb8687d8687fed853598ec9460d94bcd5e16babe
  CapacitorCommunitySafeArea: cc370b4f8d4aa340e4616acef9b73eda41ba0914
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorFilesystem: 307f97c27a265edf8396a1c9c235592fd8572fe3
  CapacitorHaptics: 70e47470fa1a6bd6338cd102552e3846b7f9a1b3
  CapacitorKeyboard: 969647d0ca2e5c737d7300088e2517aa832434e2
  CapacitorSplashScreen: 19cd3573e57507e02d6f34597a8c421e00931487
  CapacitorStatusBar: 275cbf2f4dfc00388f519ef80c7ec22edda342c9
  CapawesomeCapacitorFilePicker: 6673764afb8768eba0197876e222adc19fd4c120
  CapgoCapacitorUploader: 4e8f662cc52b3b4d73054d47dad775a27b2c3c0a

PODFILE CHECKSUM: f47ec5a61b02bc75b30f66fe08022023dc10f8f6

COCOAPODS: 1.16.2
