{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "baseUrl": ".",
    "paths": {
      "@renderer/*": ["desktop/src/renderer/src/*"],
      "@common/*": ["desktop/src/common/*"],
      "@main/*": ["desktop/src/main/*"],
      "@preload/*": ["desktop/src/preload/*"],
      "@app/*": ["app/src/*"],
      "@/*": ["shared/*"]
    }
  },
  "include": [
    "env.d.ts",
    "vite-env.d.ts",
    "desktop/src/renderer/src/**/*",
    "desktop/src/renderer/src/**/*.tsx",
    "desktop/src/preload/*.d.ts",
    "app/src/**/*",
    "shared/**/*"
  ],
  "exclude": ["node_modules", "app/dist", "app/android", "app/ios"]
}
